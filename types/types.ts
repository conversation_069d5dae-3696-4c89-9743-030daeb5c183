import { PlayerGWStats, PlayerTotalStats, Team } from "@prisma/client";

export type Role = "C" | "V" | null;

export type Position = "GK" | "DEF" | "MID" | "ATK";

export type ViewMode = "list" | "pitch";
// current price is a decimal, but we want to work with numbers
export type PlayerInfo = {
  id: string;
  name: string;
  position: Position;
  currentPrice: number;
  teamId: string;
  team: Team;
  totalStats: PlayerTotalStats | null;
  gwStats: PlayerGWStats[];
}

import { create } from "zustand";
import { Position, Team, UserGWSheet } from "@prisma/client";
import { toast } from "sonner";
import { sortPlayers } from "@/lib/utils";
import { devtools } from "zustand/middleware";
import { generateFakeBoardingTeam } from "@/lib/utils";
import { PlayerInfo, ViewMode } from "@/types/types";

interface FantasyState {
  // View state
  view: ViewMode;
  setView: (view: ViewMode) => void;

  // Player state
  players: PlayerInfo[];
  marketPlayers: PlayerInfo[];
  isLoading: boolean;
  error: string | null;

  // Teams info
  teams: Team[];
  fetchTeams: () => Promise<void>;

  // Filter states
  filterPosition: Position | null;
  filterTeam: string;
  filterPrice: number;

  // Team state
  starters: PlayerInfo[];
  subs: PlayerInfo[];
  starterIds: Set<string>;
  subIds: Set<string>;
  removedPlayers: PlayerInfo[];
  captainId: string | null;
  viceCaptainId: string | null;
  hasChanges: boolean;
  userGWSheet: UserGWSheet | null;

  // Original team state for transfer tracking
  originalStarters: PlayerInfo[];
  originalSubs: PlayerInfo[];

  // Actions
  setFilterPosition: (position: Position | null) => void;
  setFilterTeam: (team: string | null) => void;
  setFilterPrice: (price: number) => void;
  fetchPlayers: () => Promise<void>;
  fetchTeam: (
    userId: string | null,
    round: string | null,
    isCreate?: boolean
  ) => Promise<void>;
  updateMarket: (players: PlayerInfo[]) => void;
  handleCaptainChange: (playerId: string) => void;
  handleViceCaptainChange: (playerId: string) => void;
  handleSubstitute: (outId: string, inId: string) => void;
  handleRemove: (player: PlayerInfo) => Position | null;
  handleAdd: (player: PlayerInfo) => boolean;
  handleRestore: (player: PlayerInfo) => void;
  saveTeam: (
    userId: string,
    round: string,
    isCreate?: boolean
  ) => Promise<boolean>;
}

export const useFantasyStore = create<FantasyState>()(
  devtools((set, get) => ({
    // View state
    view: "pitch",
    setView: (view) => set({ view }),

    // Player state
    players: [],
    marketPlayers: [],
    isLoading: false,
    error: null,

    // Teams info
    teams: [],

    // Filter states
    filterPosition: null,
    filterTeam: "all",
    filterPrice: 20,

    // Team state
    starters: [],
    subs: [],
    starterIds: new Set<string>(),
    subIds: new Set<string>(),
    removedPlayers: [],
    captainId: null,
    viceCaptainId: null,
    hasChanges: false,
    userGWSheet: null,

    // Original team state for transfer tracking
    originalStarters: [],
    originalSubs: [],

    // Actions
    setFilterPosition: (position) => set({ filterPosition: position }),
    setFilterTeam: (team) => set({ filterTeam: team || "all" }),
    setFilterPrice: (price) => set({ filterPrice: price }),

    fetchPlayers: async () => {
      try {
        set({ isLoading: true });

        const response = await fetch("/api/players");
        if (!response.ok) {
          throw new Error(`Failed to fetch data: ${response.status}`);
        }

        const players: PlayerInfo[] = await response.json();

        // Set initial price filter to a reasonable default
        if (players.length > 0) {
          const maxPrice = Math.ceil(
            Math.max(...players.map((p) => Number(p.currentPrice)))
          );
          set({
            players: sortPlayers(players),
            marketPlayers: players,
            filterPrice: maxPrice,
            error: null,
          });
        }
      } catch (err) {
        console.error("Error fetching market data:", err);
        set({ error: "Failed to fetch market data" });
        toast.error("Failed to fetch market data");
      } finally {
        set({ isLoading: false });
      }
    },

    fetchTeam: async (userId, round, isCreate?: boolean) => {
      if (!userId || !round) {
        if (!isCreate) return;
        const { starters, subs, removedPlayers } = generateFakeBoardingTeam();
        set({
          starters,
          subs,
          starterIds: new Set(starters.map((p) => p.id)),
          subIds: new Set(subs.map((p) => p.id)),
          removedPlayers,
          hasChanges: false,
          isLoading: false,
          originalStarters: [...starters],
          originalSubs: [...subs],
        });
        toast.info("No team found for this round. Create a new team.");
        return;
      }

      try {
        set({ isLoading: true });

        // First, ensure we have players loaded
        const { players } = get();
        if (players.length === 0) {
          await get().fetchPlayers();
        }

        // Fetch team data from API
        const response = await fetch(`/api/userGWSheet/${userId}/${round}`);

        if (response.status === 404) {
          // Team doesn't exist yet, set up empty team
          set({
            starters: [],
            subs: [],
            starterIds: new Set<string>(),
            subIds: new Set<string>(),
            captainId: null,
            viceCaptainId: null,
            hasChanges: false,
            isLoading: false,
            originalStarters: [],
            originalSubs: [],
          });
          toast.info("No team found for this round. Create a new team.");
          return;
        }

        if (!response.ok) {
          throw new Error(`Failed to fetch team: ${response.status}`);
        }

        const userGWSheet = await response.json();

        if (!userGWSheet) {
          throw new Error("Failed to fetch team data");
        }

        // Extract player IDs from the sheet
        const starterIds = new Set((userGWSheet.starters as string[]) || []);
        const subIds = new Set((userGWSheet.subs as string[]) || []);

        // Get the actual player objects from our players array
        const { players: allPlayers } = get();
        const starters = allPlayers.filter((p) => starterIds.has(p.id));
        const subs = allPlayers.filter((p) => subIds.has(p.id));

        // Update market players (players not in team)
        const teamPlayerIds = new Set([...starterIds, ...subIds]);
        const marketPlayers = allPlayers.filter(
          (p) => !teamPlayerIds.has(p.id)
        );

        set({
          starters,
          subs,
          starterIds,
          subIds,
          captainId: userGWSheet.captainId,
          viceCaptainId: userGWSheet.viceCaptainId,
          userGWSheet,
          marketPlayers,
          hasChanges: false,
          error: null,
          // Store original team state for transfer tracking
          originalStarters: [...starters],
          originalSubs: [...subs],
        });
      } catch (err) {
        console.error("Error fetching team data:", err);
        set({
          error: "Failed to fetch team data",
          isLoading: false,
        });
        toast.error("Failed to fetch team data");
      } finally {
        set({ isLoading: false });
      }
    },

    fetchTeams: async () => {
      try {
        set({ isLoading: true });

        const response = await fetch("/api/teams");
        if (!response.ok) {
          throw new Error(`Failed to fetch data: ${response.status}`);
        }

        const teams: Team[] = await response.json();

        set({ teams: teams.sort((a, b) => a.name.localeCompare(b.name)) });
      } catch (err) {
        console.error("Error fetching teams data:", err);
        set({ error: "Failed to fetch teams data" });
        toast.error("Failed to fetch teams data");
      } finally {
        set({ isLoading: false });
      }
    },

    updateMarket: (players) => set({ marketPlayers: players }),

    handleCaptainChange: (playerId) => {
      const { players } = get();
      const player = players.find((p) => p.id === playerId);

      set(() => ({
        captainId: playerId,
        hasChanges: true,
      }));

      if (player) {
        toast.success(`${player.name} is now your captain`);
      }
    },

    handleViceCaptainChange: (playerId) => {
      const { players } = get();
      const player = players.find((p) => p.id === playerId);

      set(() => ({
        viceCaptainId: playerId,
        hasChanges: true,
      }));

      if (player) {
        toast.success(`${player.name} is now your vice captain`);
      }
    },

    handleSubstitute: (outId, inId) => {
      const { starters, subs, starterIds, subIds, captainId, viceCaptainId } =
        get();

      // Find the players
      const outPlayer = starters.find((p) => p.id === outId);
      const inPlayer = subs.find((p) => p.id === inId);

      if (!outPlayer || !inPlayer) return;

      // Update the sets
      const newStarterIds = new Set(starterIds);
      const newSubIds = new Set(subIds);

      newStarterIds.delete(outId);
      newStarterIds.add(inId);
      newSubIds.delete(inId);
      newSubIds.add(outId);

      // Update the arrays
      const newStarters = starters
        .filter((p) => p.id !== outId)
        .concat(inPlayer);
      const newSubs = subs.filter((p) => p.id !== inId).concat(outPlayer);

      set({
        starters: newStarters,
        subs: newSubs,
        starterIds: newStarterIds,
        captainId: captainId === outId ? inId : captainId,
        viceCaptainId: viceCaptainId === outId ? inId : viceCaptainId,
        subIds: newSubIds,
        hasChanges: true,
      });

      toast.success(`Substituted ${outPlayer.name} with ${inPlayer.name}`);
    },

    handleRemove: (player) => {
      const { starters, subs, removedPlayers, marketPlayers } = get();

      // Check if player is in starters or subs
      const isInStarters = starters.some((p) => p.id === player.id);
      const isInSubs = subs.some((p) => p.id === player.id);

      if (!isInStarters && !isInSubs) return null;

      // Update the appropriate array
      set(() => ({
        removedPlayers: [...removedPlayers, player],
        marketPlayers: sortPlayers([...marketPlayers, player]),
        hasChanges: true,
      }));

      toast.info(`${player.name} removed`);

      return player.position;
    },

    handleAdd: (player: PlayerInfo) => {
      const { starters, subs, removedPlayers, marketPlayers } = get();

      // Check if there are any removed players
      if (!removedPlayers.length) return false;

      // find the first removed player that matches the position of the incoming player
      const removedPlayer = removedPlayers.find(
        (p) => p.position === player.position
      );
      if (!removedPlayer) return false;

      // Determine if the removed player was a starter
      const wasStarter = starters.some((p) => p.id === removedPlayer.id);

      if (wasStarter) {
        const newStarters = sortPlayers(
          starters.map((p) => (p.id === removedPlayer.id ? player : p))
        );
        set(() => ({
          starters: newStarters,
          starterIds: new Set(newStarters.map((p) => p.id)),
          removedPlayers: removedPlayers.filter(
            (p) => p.id !== removedPlayer.id
          ),
          marketPlayers: sortPlayers(
            marketPlayers.filter((p) => p.id !== player.id)
          ),
          hasChanges: true,
        }));

        toast.success(`${player.name} added`);
      } else {
        const newSubs = sortPlayers(
          subs.map((p) => (p.id === removedPlayer.id ? player : p))
        );
        set(() => ({
          subs: newSubs,
          subIds: new Set(newSubs.map((p) => p.id)),
          removedPlayers: removedPlayers.filter(
            (p) => p.id !== removedPlayer.id
          ),
          marketPlayers: sortPlayers(
            marketPlayers.filter((p) => p.id !== player.id)
          ),
          hasChanges: true,
        }));

        toast.success(`${player.name} added`);
      }

      return true;
    },

    handleRestore: (player) => {
      const { removedPlayers, marketPlayers } = get();

      // Check if player is not removed
      if (!removedPlayers.some((p) => p.id === player.id)) return;

      // Remove from removed players and remove from market players
      set(() => ({
        removedPlayers: removedPlayers.filter((p) => p.id !== player.id),
        marketPlayers: sortPlayers(
          marketPlayers.filter((p) => p.id !== player.id)
        ),
        // fix hasChanges with starterIds and subIds
        hasChanges: false,
      }));

      toast.success(`${player.name} restored`);
    },

    saveTeam: async (userId, round, isCreate = false) => {
      const { starters, subs, captainId, viceCaptainId } = get();

      try {
        set({ isLoading: true });

        const method = isCreate ? "POST" : "PUT";
        const url = `/api/userGWSheet/${userId}/${round}`;

        const response = await fetch(url, {
          method,
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            starters: starters.map((p) => p.id),
            subs: subs.map((p) => p.id),
            captainId,
            viceCaptainId,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to save team: ${response.status}`);
        }

        set({ hasChanges: false });
        toast.success("Team saved successfully");
        return true;
      } catch (err) {
        console.error("Error saving team:", err);
        set({
          error: `Failed to save team: ${
            err instanceof Error ? err.message : "Unknown error"
          }`,
        });
        toast.error(
          `Failed to save team: ${
            err instanceof Error ? err.message : "Unknown error"
          }`
        );
        return false;
      } finally {
        set({ isLoading: false });
      }
    },
  }))
);

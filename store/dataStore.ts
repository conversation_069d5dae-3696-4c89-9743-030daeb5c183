import { GameWeek, Player, Team } from "@prisma/client";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface DataState {
  gameweeks: GameWeek[];
  activeGameweek: GameWeek | null;
  players: Player[];
  teams: Team[];

  isLoading: boolean;
  error: string | null;

  // Actions
}

export const useDataStore = create<DataState>()(
  devtools((set, get) => ({
    isLoading: false,
    error: null,
    activeGameweek: null,
    gameweeks: [],
    players: [],
    teams: [],
    fetchData: async () => {
      try {
        set({ isLoading: true });

        // Fetch gameweeks
        const response = await fetch("/api/gameweek");
        if (!response.ok) {
          throw new Error(`Failed to fetch data: ${response.status}`);
        }
        const gameweeks: GameWeek[] = await response.json();
        set({ gameweeks });

        // Fetch active gameweek
        const activeResponse = await fetch("/api/activeGameweek");
        if (!activeResponse.ok) {
          throw new Error(`Failed to fetch data: ${activeResponse.status}`);
        }
        const activeGameweek: GameWeek = await activeResponse.json();
        set({ activeGameweek });

        // Fetch players
        const playersResponse = await fetch("/api/players");
        if (!playersResponse.ok) {
          throw new Error(`Failed to fetch data: ${playersResponse.status}`);
        }
        const players: Player[] = await playersResponse.json();
        set({ players });

        // Fetch teams
        const teamsResponse = await fetch("/api/teams");
        if (!teamsResponse.ok) {
          throw new Error(`Failed to fetch data: ${teamsResponse.status}`);
        }
        const teams: Team[] = await teamsResponse.json();
        set({ teams });
      } catch (err) {
        console.error("Error fetching data:", err);
        set({ error: "Failed to fetch data" });
      } finally {
        set({ isLoading: false });
      }
    },
  }))
);

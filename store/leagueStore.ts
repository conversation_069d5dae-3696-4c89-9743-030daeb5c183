import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface LeagueUser {
  id: string;
  name: string;
  email: string;
  totalPoints?: number;
}

export interface League {
  id: string;
  name: string;
  admins: LeagueUser[];
  members: LeagueUser[];
  _count: {
    members: number;
  };
}

export interface LeaguePlayer {
  id: string;
  name: string;
  position: string;
  currentPrice: number;
  totalPoints: number;
  team: {
    id: string;
    name: string;
    abbr: string;
    logo: string;
  };
  user: {
    id: string;
    name: string;
    email: string;
  };
}

interface LeagueState {
  // State
  leagues: League[];
  currentLeague: League | null;
  leaguePlayers: LeaguePlayer[];
  loading: boolean;
  error: string | null;
  
  // UI State
  searchTerm: string;
  createDialogOpen: boolean;
  joinDialogOpen: boolean;
  newLeagueName: string;
  joinCode: string;
  copiedLeagueId: string | null;
  
  // Actions
  setLeagues: (leagues: League[]) => void;
  setCurrentLeague: (league: League | null) => void;
  setLeaguePlayers: (players: LeaguePlayer[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // UI Actions
  setSearchTerm: (term: string) => void;
  setCreateDialogOpen: (open: boolean) => void;
  setJoinDialogOpen: (open: boolean) => void;
  setNewLeagueName: (name: string) => void;
  setJoinCode: (code: string) => void;
  setCopiedLeagueId: (id: string | null) => void;
  
  // API Actions
  fetchLeagues: () => Promise<void>;
  fetchLeague: (id: string) => Promise<void>;
  fetchLeaguePlayers: (id: string) => Promise<void>;
  createLeague: (name: string) => Promise<boolean>;
  joinLeague: (code: string) => Promise<boolean>;
  leaveLeague: (leagueId: string) => Promise<boolean>;
  updateLeague: (id: string, data: { name: string }) => Promise<boolean>;
  deleteLeague: (id: string) => Promise<boolean>;
  removeMember: (leagueId: string, memberId: string) => Promise<boolean>;
  toggleAdmin: (leagueId: string, memberId: string, isCurrentlyAdmin: boolean) => Promise<boolean>;
  
  // Utility Actions
  reset: () => void;
  clearError: () => void;
}

const initialState = {
  leagues: [],
  currentLeague: null,
  leaguePlayers: [],
  loading: false,
  error: null,
  searchTerm: '',
  createDialogOpen: false,
  joinDialogOpen: false,
  newLeagueName: '',
  joinCode: '',
  copiedLeagueId: null,
};

export const useLeagueStore = create<LeagueState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // Basic setters
      setLeagues: (leagues) => set({ leagues }),
      setCurrentLeague: (league) => set({ currentLeague: league }),
      setLeaguePlayers: (players) => set({ leaguePlayers: players }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),
      
      // UI setters
      setSearchTerm: (searchTerm) => set({ searchTerm }),
      setCreateDialogOpen: (createDialogOpen) => set({ createDialogOpen }),
      setJoinDialogOpen: (joinDialogOpen) => set({ joinDialogOpen }),
      setNewLeagueName: (newLeagueName) => set({ newLeagueName }),
      setJoinCode: (joinCode) => set({ joinCode }),
      setCopiedLeagueId: (copiedLeagueId) => set({ copiedLeagueId }),
      
      // API Actions
      fetchLeagues: async () => {
        set({ loading: true, error: null });
        try {
          const response = await fetch('/api/leagues');
          if (response.ok) {
            const leagues = await response.json();
            set({ leagues, loading: false });
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to fetch leagues', loading: false });
          }
        } catch (error) {
          console.error('Error fetching leagues:', error);
          set({ error: 'Failed to fetch leagues', loading: false });
        }
      },
      
      fetchLeague: async (id: string) => {
        set({ loading: true, error: null });
        try {
          const response = await fetch(`/api/leagues/${id}`);
          if (response.ok) {
            const league = await response.json();
            set({ currentLeague: league, loading: false });
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to fetch league', loading: false });
          }
        } catch (error) {
          console.error('Error fetching league:', error);
          set({ error: 'Failed to fetch league', loading: false });
        }
      },
      
      fetchLeaguePlayers: async (id: string) => {
        set({ loading: true, error: null });
        try {
          const response = await fetch(`/api/leagues/${id}/players`);
          if (response.ok) {
            const players = await response.json();
            set({ leaguePlayers: players, loading: false });
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to fetch league players', loading: false });
          }
        } catch (error) {
          console.error('Error fetching league players:', error);
          set({ error: 'Failed to fetch league players', loading: false });
        }
      },
      
      createLeague: async (name: string) => {
        set({ loading: true, error: null });
        try {
          const response = await fetch('/api/leagues', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name }),
          });
          
          if (response.ok) {
            const newLeague = await response.json();
            const { leagues } = get();
            set({ 
              leagues: [...leagues, newLeague], 
              loading: false,
              newLeagueName: '',
              createDialogOpen: false
            });
            return true;
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to create league', loading: false });
            return false;
          }
        } catch (error) {
          console.error('Error creating league:', error);
          set({ error: 'Failed to create league', loading: false });
          return false;
        }
      },
      
      joinLeague: async (code: string) => {
        set({ loading: true, error: null });
        try {
          const response = await fetch(`/api/leagues/${code}/join`, {
            method: 'POST',
          });
          
          if (response.ok) {
            const updatedLeague = await response.json();
            const { leagues } = get();
            const existingIndex = leagues.findIndex(l => l.id === updatedLeague.id);
            
            if (existingIndex >= 0) {
              const updatedLeagues = [...leagues];
              updatedLeagues[existingIndex] = updatedLeague;
              set({ leagues: updatedLeagues });
            } else {
              set({ leagues: [...leagues, updatedLeague] });
            }
            
            set({ 
              loading: false,
              joinCode: '',
              joinDialogOpen: false
            });
            return true;
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to join league', loading: false });
            return false;
          }
        } catch (error) {
          console.error('Error joining league:', error);
          set({ error: 'Failed to join league', loading: false });
          return false;
        }
      },
      
      leaveLeague: async (leagueId: string) => {
        set({ loading: true, error: null });
        try {
          const response = await fetch(`/api/leagues/${leagueId}/leave`, {
            method: 'POST',
          });
          
          if (response.ok) {
            const { leagues } = get();
            set({ 
              leagues: leagues.filter(l => l.id !== leagueId),
              loading: false
            });
            return true;
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to leave league', loading: false });
            return false;
          }
        } catch (error) {
          console.error('Error leaving league:', error);
          set({ error: 'Failed to leave league', loading: false });
          return false;
        }
      },
      
      updateLeague: async (id: string, data: { name: string }) => {
        set({ loading: true, error: null });
        try {
          const response = await fetch(`/api/leagues/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
          });
          
          if (response.ok) {
            const updatedLeague = await response.json();
            const { leagues } = get();
            set({ 
              leagues: leagues.map(l => l.id === id ? updatedLeague : l),
              currentLeague: updatedLeague,
              loading: false
            });
            return true;
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to update league', loading: false });
            return false;
          }
        } catch (error) {
          console.error('Error updating league:', error);
          set({ error: 'Failed to update league', loading: false });
          return false;
        }
      },
      
      deleteLeague: async (id: string) => {
        set({ loading: true, error: null });
        try {
          const response = await fetch(`/api/leagues/${id}`, {
            method: 'DELETE',
          });
          
          if (response.ok) {
            const { leagues } = get();
            set({ 
              leagues: leagues.filter(l => l.id !== id),
              currentLeague: null,
              loading: false
            });
            return true;
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to delete league', loading: false });
            return false;
          }
        } catch (error) {
          console.error('Error deleting league:', error);
          set({ error: 'Failed to delete league', loading: false });
          return false;
        }
      },
      
      removeMember: async (leagueId: string, memberId: string) => {
        set({ loading: true, error: null });
        try {
          const response = await fetch(`/api/leagues/${leagueId}/members/${memberId}`, {
            method: 'DELETE',
          });
          
          if (response.ok) {
            const { currentLeague } = get();
            if (currentLeague && currentLeague.id === leagueId) {
              set({
                currentLeague: {
                  ...currentLeague,
                  members: currentLeague.members.filter(m => m.id !== memberId),
                  admins: currentLeague.admins.filter(a => a.id !== memberId),
                },
                loading: false
              });
            }
            return true;
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to remove member', loading: false });
            return false;
          }
        } catch (error) {
          console.error('Error removing member:', error);
          set({ error: 'Failed to remove member', loading: false });
          return false;
        }
      },
      
      toggleAdmin: async (leagueId: string, memberId: string, isCurrentlyAdmin: boolean) => {
        set({ loading: true, error: null });
        try {
          const response = await fetch(`/api/leagues/${leagueId}/admins/${memberId}`, {
            method: isCurrentlyAdmin ? 'DELETE' : 'POST',
          });
          
          if (response.ok) {
            const updatedLeague = await response.json();
            set({ 
              currentLeague: updatedLeague,
              loading: false
            });
            return true;
          } else {
            const error = await response.json();
            set({ error: error.error || 'Failed to update admin rights', loading: false });
            return false;
          }
        } catch (error) {
          console.error('Error updating admin rights:', error);
          set({ error: 'Failed to update admin rights', loading: false });
          return false;
        }
      },
      
      // Utility actions
      reset: () => set(initialState),
      clearError: () => set({ error: null }),
    }),
    {
      name: 'league-store',
    }
  )
);

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { Player, Position } from "@prisma/client";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

const positionOrder = { GK: 1, DEF: 2, MID: 3, ATK: 4 };

export function sortPlayers(players: Player[]) {
  return players.sort((a, b) => {
    if (a.position !== b.position) {
      return positionOrder[a.position] - positionOrder[b.position];
    }
    return a.name.localeCompare(b.name);
  });
}

const generatePlayer = (position: Position): Player => {
  console.log("generatePlayer", position);
  return {
    id: `${name}-${Math.floor(Math.random() * 1000)}`,
    name: "",
    position: position,
    currentPrice: 0,
    teamId: "",
  };
};

export function generateFakeBoardingTeam() {
  const starters = Array.from(
    { length: 1 },
    () => generatePlayer("GK") as Player
  )
    .concat(Array.from({ length: 4 }, () => generatePlayer("DEF") as Player))
    .concat(Array.from({ length: 3 }, () => generatePlayer("MID") as Player))
    .concat(Array.from({ length: 3 }, () => generatePlayer("ATK") as Player));
  const subs = Array.from({ length: 1 }, () => generatePlayer("GK") as Player)
    .concat(Array.from({ length: 1 }, () => generatePlayer("DEF") as Player))
    .concat(Array.from({ length: 2 }, () => generatePlayer("MID") as Player));
  const removedPlayers = [...starters, ...subs];
  return {
    subs,
    starters,
    removedPlayers,
  };
}

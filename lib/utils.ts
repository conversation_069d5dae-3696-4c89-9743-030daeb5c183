import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { Player, Position } from "@prisma/client";

export interface TransferValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface TransferSummary {
  playersIn: Player[];
  playersOut: Player[];
  transferCount: number;
  penaltyPoints: number;
  newTeamValue: number;
  teamValueChange: number;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

const positionOrder = { GK: 1, DEF: 2, MID: 3, ATK: 4 };

export function sortPlayers(players: Player[]) {
  return players.sort((a, b) => {
    if (a.position !== b.position) {
      return positionOrder[a.position] - positionOrder[b.position];
    }
    return a.name.localeCompare(b.name);
  });
}

const generatePlayer = (position: Position): Player => {
  console.log("generatePlayer", position);
  return {
    id: `${name}-${Math.floor(Math.random() * 1000)}`,
    name: "",
    position: position,
    currentPrice: 0,
    teamId: "",
  };
};

export function generateFakeBoardingTeam() {
  const starters = Array.from(
    { length: 1 },
    () => generatePlayer("GK") as Player
  )
    .concat(Array.from({ length: 4 }, () => generatePlayer("DEF") as Player))
    .concat(Array.from({ length: 3 }, () => generatePlayer("MID") as Player))
    .concat(Array.from({ length: 3 }, () => generatePlayer("ATK") as Player));
  const subs = Array.from({ length: 1 }, () => generatePlayer("GK") as Player)
    .concat(Array.from({ length: 1 }, () => generatePlayer("DEF") as Player))
    .concat(Array.from({ length: 2 }, () => generatePlayer("MID") as Player));
  const removedPlayers = [...starters, ...subs];
  return {
    subs,
    starters,
    removedPlayers,
  };
}

// Transfer validation constants
export const TRANSFER_RULES = {
  MAX_BUDGET: 100,
  MAX_PLAYERS_PER_TEAM: 5,
  TRANSFER_PENALTY_POINTS: -4,
  REQUIRED_STARTERS: 11,
  REQUIRED_SUBS: 4,
} as const;

/**
 * Validates if a team meets all transfer rules
 */
export function validateTeamTransfers(
  starters: Player[],
  subs: Player[],
  originalStarters: Player[],
  originalSubs: Player[]
): TransferValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  const team = [...starters, ...subs];
  const originalTeam = [...originalStarters, ...originalSubs];

  // Rule 1: Check team size
  if (starters.length !== TRANSFER_RULES.REQUIRED_STARTERS) {
    errors.push(`Team must have exactly ${TRANSFER_RULES.REQUIRED_STARTERS} starters (currently ${starters.length})`);
  }

  if (subs.length !== TRANSFER_RULES.REQUIRED_SUBS) {
    errors.push(`Team must have exactly ${TRANSFER_RULES.REQUIRED_SUBS} substitutes (currently ${subs.length})`);
  }

  // Rule 2: Check budget constraint
  const totalValue = team.reduce((sum, player) => sum + player.currentPrice, 0);
  if (totalValue > TRANSFER_RULES.MAX_BUDGET) {
    errors.push(`Team value (${totalValue}M) exceeds budget limit of ${TRANSFER_RULES.MAX_BUDGET}M`);
  }

  // Rule 3: Check max players per team constraint
  const teamCounts = team.reduce((counts, player) => {
    counts[player.teamId] = (counts[player.teamId] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);

  Object.entries(teamCounts).forEach(([teamId, count]) => {
    if (count > TRANSFER_RULES.MAX_PLAYERS_PER_TEAM) {
      errors.push(`Too many players from ${teamId} (${count}/${TRANSFER_RULES.MAX_PLAYERS_PER_TEAM} max)`);
    }
  });

  // Rule 4: Check position requirements
  const positionCounts = team.reduce((counts, player) => {
    counts[player.position] = (counts[player.position] || 0) + 1;
    return counts;
  }, {} as Record<Position, number>);

  // Required positions: 2 GK, 5 DEF, 5 MID, 3 ATK
  const requiredPositions = { GK: 2, DEF: 5, MID: 5, ATK: 3 };
  Object.entries(requiredPositions).forEach(([position, required]) => {
    const current = positionCounts[position as Position] || 0;
    if (current !== required) {
      errors.push(`Invalid ${position} count: ${current}/${required} required`);
    }
  });

  // Add warnings for high team value
  if (totalValue > TRANSFER_RULES.MAX_BUDGET * 0.9) {
    warnings.push(`Team value is ${((totalValue / TRANSFER_RULES.MAX_BUDGET) * 100).toFixed(1)}% of budget`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Calculates transfer summary including penalties
 */
export function calculateTransferSummary(
  currentStarters: Player[],
  currentSubs: Player[],
  originalStarters: Player[],
  originalSubs: Player[]
): TransferSummary {
  const currentTeam = [...currentStarters, ...currentSubs];
  const originalTeam = [...originalStarters, ...originalSubs];

  // Find players transferred in and out
  const originalPlayerIds = new Set(originalTeam.map(p => p.id));
  const currentPlayerIds = new Set(currentTeam.map(p => p.id));

  const playersIn = currentTeam.filter(p => !originalPlayerIds.has(p.id));
  const playersOut = originalTeam.filter(p => !currentPlayerIds.has(p.id));

  const transferCount = playersIn.length; // Should equal playersOut.length
  const penaltyPoints = transferCount * TRANSFER_RULES.TRANSFER_PENALTY_POINTS;

  const originalTeamValue = originalTeam.reduce((sum, p) => sum + p.currentPrice, 0);
  const newTeamValue = currentTeam.reduce((sum, p) => sum + p.currentPrice, 0);
  const teamValueChange = newTeamValue - originalTeamValue;

  return {
    playersIn,
    playersOut,
    transferCount,
    penaltyPoints,
    newTeamValue,
    teamValueChange,
  };
}

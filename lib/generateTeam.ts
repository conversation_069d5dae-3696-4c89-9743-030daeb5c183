// lib/generateTeam.ts
import { Player, Position } from "@prisma/client";

const teams = [
  { name: "Club Africain", abbr: "CA", logo: "/logos/ca.png" },
  { name: "<PERSON><PERSON><PERSON>", abbr: "E<PERSON>", logo: "/logos/ess.png" },
  { name: "CS Sfaxien", abbr: "CSS", logo: "/logos/css.png" },
  { name: "US Monastir", abbr: "USM", logo: "/logos/usm.png" },
];

  

const generateFakeName = () => {
  const lastNames = [
    "<PERSON> Amor",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>uer<PERSON><PERSON>",
    "Bouzid<PERSON>",
    "K<PERSON>en",
    "<PERSON> Ali",
    "Bouguerra",
    "Bouhlel",
    "Ben Hammouda",
    "Ben Salem",
  ];
  return `${lastNames[Math.floor(Math.random() * lastNames.length)]}`;
};

const randomStats = () => ({
  MP: Math.floor(Math.random() * 90),
  GS: Math.floor(Math.random() * 2),
  A: Math.floor(Math.random() * 3),
  CS: Math.floor(Math.random() * 1),
  GC: Math.floor(Math.random() * 2),
  OG: Math.floor(Math.random() * 1),
  PS: Math.floor(Math.random() * 1),
  PM: Math.floor(Math.random() * 1),
  YC: Math.floor(Math.random() * 1),
  RC: Math.floor(Math.random() * 1),
  S: Math.floor(Math.random() * 5),
  B: Math.floor(Math.random() * 3),
  BPS: Math.floor(Math.random() * 50),
});

export function generateTeam(): Player[] {
  const result: Player[] = [];
  const limits = { GK: 2, DEF: 5, MID: 5, ATK: 3 };

  let playerId = 1;
  for (const pos of Object.keys(limits) as Position[]) {
    for (let i = 0; i < limits[pos]; i++) {
      const team = teams[Math.floor(Math.random() * teams.length)];
      const name = generateFakeName();
      result.push({
        id: `${name}-${playerId++}`,
        name,
        position: pos,
        team: team.name,
        teamAbbr: team.abbr,
        teamLogo: team.logo,
        price: parseFloat((Math.random() * 10 + 5).toFixed(1)),
        points: Math.floor(Math.random() * 15),
        role: null,
        isRemoved: false,
        isStarter: null,
        isSub: null,
        stats: randomStats(),
        nextFixture: teams[Math.floor(Math.random() * teams.length)].abbr,
      });
    }
  }

  return result;
}

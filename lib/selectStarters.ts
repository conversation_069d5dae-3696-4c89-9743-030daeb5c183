// lib/selectStarters.ts

import { Player } from "@prisma/client";

export function assignStartersAndSubs(players: Player[]) {
    let starters: Player[] = []
    let subs: Player[] = []

    // Sort by position
    const gks = players.filter(p => p.position === "GK")
    const defs = players.filter(p => p.position === "DEF")
    const mids = players.filter(p => p.position === "MID")
    const atks = players.filter(p => p.position === "ATK")

    starters.push(gks[0])      // 1 GK
    subs.push(gks[1])          // 1 GK

    starters.push(...defs.slice(0, 4)) // 4 DEF
    subs.push(defs[4])

    starters.push(...mids.slice(0, 3)) // 3 MID
    subs.push(...mids.slice(3))

    starters.push(...atks.slice(0, 3)) // 3 ATK

    starters = starters.map(p => ({ ...p, isStarter: true, isSub: false }))
    subs = subs.map(p => ({ ...p, isStarter: false, isSub: true }))

    return { starters, subs }
}


export function validateTeamComposition(team: Player[]): boolean {
    if (team.length !== 11) return false

    const count = { GK: 0, DEF: 0, MID: 0, ATK: 0 }
    for (const p of team) {
        count[p.position]++
    }

    return (
        count.GK === 1 &&
        count.DEF >= 3 &&
        count.MID >= 3 &&
        count.ATK >= 1
    )
}

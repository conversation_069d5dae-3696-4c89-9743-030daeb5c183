import { prisma } from "./prisma";
import { PlayerInfo } from "@/types/types";

export async function getPlayers() : Promise<PlayerInfo[]> {
  try {
    const players = await prisma.player.findMany({
      include: {
        team: true,
        totalStats: true,
        gwStats: true,
      },
    });
    const safePlayers : PlayerInfo[] = players.map((p) => {
      return {
        ...p,
        currentPrice: p.currentPrice.toNumber(),
      };
    });
    return safePlayers;
  } catch (error) {
    console.error(error);
    return [];
  }
}

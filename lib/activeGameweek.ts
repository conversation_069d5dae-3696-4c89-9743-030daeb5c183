import { prisma } from "@/lib/prisma";

export interface ActiveGameWeekData {
  id: string;
  gameweekId: string;
  gameWeek: {
    id: string;
    season: string;
    GW: number;
  };
}

/**
 * Get the current active gameweek from the database
 * @returns Promise<ActiveGameWeekData | null>
 */
export async function getActiveGameWeek(): Promise<ActiveGameWeekData | null> {
  try {
    const activeGameweek = await prisma.activeGameWeek.findFirst({
      include: {
        gameWeek: true
      }
    });
    
    return activeGameweek;
  } catch (error) {
    console.error("Error fetching active gameweek:", error);
    return null;
  }
}

/**
 * Get the current active gameweek number
 * @returns Promise<number | null>
 */
export async function getActiveGameWeekNumber(): Promise<number | null> {
  const activeGameweek = await getActiveGameWeek();
  return activeGameweek?.gameWeek.GW || null;
}

/**
 * Get the current active gameweek ID
 * @returns Promise<string | null>
 */
export async function getActiveGameWeekId(): Promise<string | null> {
  const activeGameweek = await getActiveGameWeek();
  return activeGameweek?.gameweekId || null;
}

/**
 * Set the active gameweek
 * @param gameweekId - The ID of the gameweek to set as active
 * @returns Promise<ActiveGameWeekData | null>
 */
export async function setActiveGameWeek(gameweekId: string): Promise<ActiveGameWeekData | null> {
  try {
    // Verify the gameweek exists
    const gameweek = await prisma.gameWeek.findUnique({
      where: { id: gameweekId }
    });

    if (!gameweek) {
      throw new Error("Gameweek not found");
    }

    // Use upsert to handle both create and update cases
    const activeGameweek = await prisma.activeGameWeek.upsert({
      where: { gameweekId },
      update: { gameweekId },
      create: { gameweekId },
      include: {
        gameWeek: true
      }
    });

    return activeGameweek;
  } catch (error) {
    console.error("Error setting active gameweek:", error);
    return null;
  }
}

import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

const isAdminRoute = createRouteMatcher(["/admin(.*)"]);
const isApiAdminRoute = createRouteMatcher(["/api/admin(.*)"]);

export default clerkMiddleware(async (auth, req: NextRequest) => {
  // Handle admin routes
  if (isAdminRoute(req) || isApiAdminRoute(req)) {
    const { userId } = await auth();
    
    // Redirect to sign-in if not authenticated
    if (!userId) {
      const signInUrl = new URL("/sign-in", req.url);
      signInUrl.searchParams.set("redirect_url", req.url);
      return NextResponse.redirect(signInUrl);
    }

    // For API routes, we'll let the route handlers check admin status
    // For page routes, the layout will handle admin verification
  }

  return NextResponse.next();
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};

import { NextResponse } from "next/server";

export function middleware(request: Request) {
  // For now, we'll just let all requests through
  // The admin authentication is handled client-side in the layout
  // redirect / to /admin
  if (request instanceof Request && new URL(request.url).pathname === "/") {
    return NextResponse.redirect(new URL("/admin", request.url));
  }
  return NextResponse.next();
}

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};

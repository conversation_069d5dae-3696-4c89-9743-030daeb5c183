import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";

const isProtectedRoute = createRouteMatcher([
  "/points(.*)",
  "/team(.*)",
  "/transfer(.*)",
  "/leagues(.*)",
  "/admin(.*)",
]);

export default clerkMiddleware(async (auth, req) => {
  const { userId, redirectToSignIn } = await auth();

  if (!userId && isProtectedRoute(req)) {
    return redirectToSignIn();
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files
    "/((?!_next/static|_next/image|favicon.ico).*)",
    // Include all API routes EXCEPT the webhook route
    "/(api(?!/webhooks/clerk))(.*)",
    // Include trpc routes
    "/trpc(.*)",
    "/admin(.*)",
  ],
};

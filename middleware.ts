import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

const isProtectedRoute = createRouteMatcher([
  "/points(.*)",
  "/team(.*)",
  "/transfers(.*)",
  "/leagues(.*)",
  "/admin(.*)",
]);

const isOnboardingRoute = createRouteMatcher(["/onboarding"]);

export default clerkMiddleware(async (auth, req) => {
  const { userId, redirectToSignIn } = await auth();

  // If user is not authenticated and trying to access protected routes
  if (!userId && isProtectedRoute(req)) {
    return redirectToSignIn();
  }

  // If user is authenticated, check onboarding status
  if (userId) {
    const isOnOnboardingPage = isOnboardingRoute(req);

    try {
      // Check if user exists in database (completed onboarding)
      const userResponse = await fetch(`${req.nextUrl.origin}/api/user/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const userExists = userResponse.ok;

      // If user hasn't completed onboarding and is not on onboarding page
      if (!userExists && !isOnOnboardingPage) {
        return NextResponse.redirect(new URL('/onboarding', req.url));
      }

      // If user has completed onboarding and is on onboarding page
      if (userExists && isOnOnboardingPage) {
        return NextResponse.redirect(new URL('/', req.url));
      }
    } catch (error) {
      console.error('Error checking user onboarding status:', error);
      // On error, allow the request to proceed
    }
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files
    "/((?!_next/static|_next/image|favicon.ico).*)",
    // Include all API routes EXCEPT the webhook route
    "/(api(?!/webhooks/clerk))(.*)",
    // Include trpc routes
    "/trpc(.*)",
    "/admin(.*)",
  ],
};

-- AlterTable
ALTER TABLE "PlayerGWStats" ALTER COLUMN "GW" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "UserGWSheet" ALTER COLUMN "GW" SET DATA TYPE TEXT;

-- CreateTable
CREATE TABLE "GameWeek" (
    "id" TEXT NOT NULL,
    "season" TEXT NOT NULL,
    "GW" INTEGER NOT NULL,

    CONSTRAINT "GameWeek_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "UserGWSheet" ADD CONSTRAINT "UserGWSheet_GW_fkey" FOREIGN KEY ("GW") REFERENCES "GameWeek"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerGWStats" ADD CONSTRAINT "PlayerGWStats_GW_fkey" FOREIGN KEY ("GW") REFERENCES "GameWeek"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Create<PERSON><PERSON>
CREATE TYPE "Position" AS ENUM ('GK', 'DEF', 'MID', 'ATK');

-- CreateEnum
CREATE TYPE "StatIdentifier" AS ENUM ('GOALS_SCORED', 'ASSISTS', 'YELLOW_CARDS', 'RED_CARDS', 'SAVES', 'PENALTIES_SAVED', 'PENALTIES_MISSED', 'OWN_GOALS', 'BONUS', 'BPS', 'C<PERSON>AN_SHEETS');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "phone" TEXT,
    "country" TEXT,
    "isAdmin" BOOLEAN NOT NULL DEFAULT false,
    "clerkId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "totalPoints" INTEGER NOT NULL DEFAULT 0,
    "transfersLeft" INTEGER NOT NULL DEFAULT 0,
    "moneyLeft" DOUBLE PRECISION NOT NULL DEFAULT 100.0,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserGWSheet" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "GW" INTEGER NOT NULL,
    "captainId" TEXT NOT NULL,
    "viceCaptainId" TEXT NOT NULL,
    "players" JSONB NOT NULL,
    "subs" JSONB NOT NULL,
    "points" INTEGER NOT NULL,

    CONSTRAINT "UserGWSheet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "League" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "League_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Player" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "teamId" TEXT NOT NULL,
    "currentPrice" INTEGER NOT NULL,
    "totalPoints" INTEGER NOT NULL,
    "position" "Position" NOT NULL,

    CONSTRAINT "Player_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlayerGWStats" (
    "id" TEXT NOT NULL,
    "GW" INTEGER NOT NULL,
    "playerId" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "points" INTEGER NOT NULL,
    "transfersIn" INTEGER NOT NULL,
    "transfersOut" INTEGER NOT NULL,
    "owners" INTEGER NOT NULL,
    "minutesPlayed" INTEGER NOT NULL,
    "goalsScored" INTEGER NOT NULL,
    "assists" INTEGER NOT NULL,
    "cleanSheets" INTEGER NOT NULL,
    "goalsConceded" INTEGER NOT NULL,
    "ownGoals" INTEGER NOT NULL,
    "penaltiesSaved" INTEGER NOT NULL,
    "penaltiesMissed" INTEGER NOT NULL,
    "yellowCards" INTEGER NOT NULL,
    "redCards" INTEGER NOT NULL,
    "saves" INTEGER NOT NULL,
    "bonus" INTEGER NOT NULL,
    "bps" INTEGER NOT NULL,

    CONSTRAINT "PlayerGWStats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Team" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "abbr" TEXT NOT NULL,
    "logo" TEXT NOT NULL,

    CONSTRAINT "Team_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Fixture" (
    "id" TEXT NOT NULL,
    "round" INTEGER NOT NULL,
    "kickoffTime" TIMESTAMP(3) NOT NULL,
    "teamHId" TEXT NOT NULL,
    "teamAId" TEXT NOT NULL,
    "teamHScore" INTEGER,
    "teamAScore" INTEGER,

    CONSTRAINT "Fixture_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FixtureStat" (
    "id" TEXT NOT NULL,
    "fixtureId" TEXT NOT NULL,
    "identifier" "StatIdentifier" NOT NULL,
    "h" JSONB NOT NULL,
    "a" JSONB NOT NULL,

    CONSTRAINT "FixtureStat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_LeagueAdmins" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_LeagueAdmins_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_UserLeagues" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_UserLeagues_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE INDEX "_LeagueAdmins_B_index" ON "_LeagueAdmins"("B");

-- CreateIndex
CREATE INDEX "_UserLeagues_B_index" ON "_UserLeagues"("B");

-- AddForeignKey
ALTER TABLE "UserGWSheet" ADD CONSTRAINT "UserGWSheet_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Player" ADD CONSTRAINT "Player_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerGWStats" ADD CONSTRAINT "PlayerGWStats_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Fixture" ADD CONSTRAINT "Fixture_teamHId_fkey" FOREIGN KEY ("teamHId") REFERENCES "Team"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Fixture" ADD CONSTRAINT "Fixture_teamAId_fkey" FOREIGN KEY ("teamAId") REFERENCES "Team"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FixtureStat" ADD CONSTRAINT "FixtureStat_fixtureId_fkey" FOREIGN KEY ("fixtureId") REFERENCES "Fixture"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_LeagueAdmins" ADD CONSTRAINT "_LeagueAdmins_A_fkey" FOREIGN KEY ("A") REFERENCES "League"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_LeagueAdmins" ADD CONSTRAINT "_LeagueAdmins_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserLeagues" ADD CONSTRAINT "_UserLeagues_A_fkey" FOREIGN KEY ("A") REFERENCES "League"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserLeagues" ADD CONSTRAINT "_UserLeagues_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

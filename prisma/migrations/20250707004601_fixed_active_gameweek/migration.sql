/*
  Warnings:

  - You are about to drop the column `GW` on the `ActiveGameWeek` table. All the data in the column will be lost.
  - You are about to drop the column `season` on the `ActiveGameWeek` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[gameweekId]` on the table `ActiveGameWeek` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[season,GW]` on the table `GameWeek` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `gameweekId` to the `ActiveGameWeek` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "ActiveGameWeek" DROP COLUMN "GW",
DROP COLUMN "season",
ADD COLUMN     "gameweekId" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "ActiveGameWeek_gameweekId_key" ON "ActiveGameWeek"("gameweekId");

-- CreateIndex
CREATE UNIQUE INDEX "GameWeek_season_GW_key" ON "GameWeek"("season", "GW");

-- AddForeignKey
ALTER TABLE "ActiveGameWeek" ADD CONSTRAINT "ActiveGameWeek_gameweekId_fkey" FOREIGN KEY ("gameweekId") REFERENCES "GameWeek"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

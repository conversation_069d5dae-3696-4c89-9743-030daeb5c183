-- CreateEnum
CREATE TYPE "Position" AS ENUM ('GK', 'DEF', 'MID', 'ATK');

-- CreateEnum
CREATE TYPE "Role" AS ENUM ('C', 'V');

-- CreateEnum
CREATE TYPE "StatIdentifier" AS ENUM ('GOALS_SCORED', 'ASSISTS', 'YELLOW_CARDS', 'RED_CARDS', 'SAVES', 'PENALTIES_SAVED', 'PENALTIES_MISSED', 'OWN_GOALS', 'BONUS', 'BPS', 'CLEAN_SHEETS');

-- CreateTable
CREATE TABLE "GameWeek" (
    "id" TEXT NOT NULL,
    "season" TEXT NOT NULL,
    "GW" INTEGER NOT NULL,

    CONSTRAINT "GameWeek_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ActiveGameWeek" (
    "id" TEXT NOT NULL,
    "gameweekId" TEXT NOT NULL,

    CONSTRAINT "ActiveGameWeek_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "phone" TEXT,
    "country" TEXT,
    "isAdmin" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "totalPoints" INTEGER NOT NULL DEFAULT 0,
    "transfersLeft" INTEGER NOT NULL DEFAULT 0,
    "moneyLeft" DOUBLE PRECISION NOT NULL DEFAULT 100.0,
    "favoriteClub" TEXT,
    "hasOnboarded" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserGWSheet" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "GW" TEXT NOT NULL,
    "captainId" TEXT,
    "viceCaptainId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "starters" TEXT[],
    "totalPoints" INTEGER NOT NULL DEFAULT 0,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "subs" TEXT[],

    CONSTRAINT "UserGWSheet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "League" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "League_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Player" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "teamId" TEXT NOT NULL,
    "currentPrice" DECIMAL(3,1) NOT NULL,
    "position" "Position" NOT NULL,

    CONSTRAINT "Player_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlayerGWStats" (
    "id" TEXT NOT NULL,
    "GW" TEXT NOT NULL,
    "playerId" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "points" INTEGER NOT NULL,
    "transfersIn" INTEGER NOT NULL,
    "transfersOut" INTEGER NOT NULL,
    "owners" INTEGER NOT NULL,
    "minutesPlayed" INTEGER NOT NULL,
    "goalsScored" INTEGER NOT NULL,
    "assists" INTEGER NOT NULL,
    "cleanSheets" INTEGER NOT NULL,
    "goalsConceded" INTEGER NOT NULL,
    "ownGoals" INTEGER NOT NULL,
    "penaltiesSaved" INTEGER NOT NULL,
    "penaltiesMissed" INTEGER NOT NULL,
    "yellowCards" INTEGER NOT NULL,
    "redCards" INTEGER NOT NULL,
    "saves" INTEGER NOT NULL,
    "bonus" INTEGER NOT NULL,
    "bps" INTEGER NOT NULL,

    CONSTRAINT "PlayerGWStats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlayerTotalStats" (
    "id" TEXT NOT NULL,
    "playerId" TEXT NOT NULL,
    "points" INTEGER NOT NULL DEFAULT 0,
    "minutesPlayed" INTEGER NOT NULL DEFAULT 0,
    "goalsScored" INTEGER NOT NULL DEFAULT 0,
    "assists" INTEGER NOT NULL DEFAULT 0,
    "cleanSheets" INTEGER NOT NULL DEFAULT 0,
    "goalsConceded" INTEGER NOT NULL DEFAULT 0,
    "ownGoals" INTEGER NOT NULL DEFAULT 0,
    "penaltiesSaved" INTEGER NOT NULL DEFAULT 0,
    "penaltiesMissed" INTEGER NOT NULL DEFAULT 0,
    "yellowCards" INTEGER NOT NULL DEFAULT 0,
    "redCards" INTEGER NOT NULL DEFAULT 0,
    "saves" INTEGER NOT NULL DEFAULT 0,
    "bonus" INTEGER NOT NULL DEFAULT 0,
    "bps" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "PlayerTotalStats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Team" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "abbr" TEXT NOT NULL,
    "logo" TEXT NOT NULL,
    "jersey" TEXT NOT NULL,

    CONSTRAINT "Team_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Fixture" (
    "id" TEXT NOT NULL,
    "round" INTEGER NOT NULL,
    "kickoffTime" TIMESTAMP(3) NOT NULL,
    "teamHId" TEXT NOT NULL,
    "teamAId" TEXT NOT NULL,
    "teamHScore" INTEGER,
    "teamAScore" INTEGER,

    CONSTRAINT "Fixture_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FixtureStat" (
    "id" TEXT NOT NULL,
    "fixtureId" TEXT NOT NULL,
    "identifier" "StatIdentifier" NOT NULL,
    "h" JSONB NOT NULL,
    "a" JSONB NOT NULL,

    CONSTRAINT "FixtureStat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_LeagueAdmins" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_LeagueAdmins_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_UserLeagues" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_UserLeagues_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "GameWeek_season_GW_key" ON "GameWeek"("season", "GW");

-- CreateIndex
CREATE UNIQUE INDEX "ActiveGameWeek_gameweekId_key" ON "ActiveGameWeek"("gameweekId");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_phone_key" ON "User"("phone");

-- CreateIndex
CREATE UNIQUE INDEX "UserGWSheet_userId_GW_key" ON "UserGWSheet"("userId", "GW");

-- CreateIndex
CREATE UNIQUE INDEX "PlayerGWStats_playerId_GW_key" ON "PlayerGWStats"("playerId", "GW");

-- CreateIndex
CREATE UNIQUE INDEX "PlayerTotalStats_playerId_key" ON "PlayerTotalStats"("playerId");

-- CreateIndex
CREATE INDEX "_LeagueAdmins_B_index" ON "_LeagueAdmins"("B");

-- CreateIndex
CREATE INDEX "_UserLeagues_B_index" ON "_UserLeagues"("B");

-- AddForeignKey
ALTER TABLE "ActiveGameWeek" ADD CONSTRAINT "ActiveGameWeek_gameweekId_fkey" FOREIGN KEY ("gameweekId") REFERENCES "GameWeek"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserGWSheet" ADD CONSTRAINT "UserGWSheet_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserGWSheet" ADD CONSTRAINT "UserGWSheet_GW_fkey" FOREIGN KEY ("GW") REFERENCES "GameWeek"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Player" ADD CONSTRAINT "Player_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerGWStats" ADD CONSTRAINT "PlayerGWStats_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerGWStats" ADD CONSTRAINT "PlayerGWStats_GW_fkey" FOREIGN KEY ("GW") REFERENCES "GameWeek"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerTotalStats" ADD CONSTRAINT "PlayerTotalStats_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Fixture" ADD CONSTRAINT "Fixture_teamAId_fkey" FOREIGN KEY ("teamAId") REFERENCES "Team"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Fixture" ADD CONSTRAINT "Fixture_teamHId_fkey" FOREIGN KEY ("teamHId") REFERENCES "Team"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FixtureStat" ADD CONSTRAINT "FixtureStat_fixtureId_fkey" FOREIGN KEY ("fixtureId") REFERENCES "Fixture"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_LeagueAdmins" ADD CONSTRAINT "_LeagueAdmins_A_fkey" FOREIGN KEY ("A") REFERENCES "League"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_LeagueAdmins" ADD CONSTRAINT "_LeagueAdmins_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserLeagues" ADD CONSTRAINT "_UserLeagues_A_fkey" FOREIGN KEY ("A") REFERENCES "League"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserLeagues" ADD CONSTRAINT "_UserLeagues_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

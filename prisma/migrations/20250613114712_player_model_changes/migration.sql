/*
  Warnings:

  - You are about to drop the column `totalPoints` on the `Player` table. All the data in the column will be lost.
  - You are about to drop the column `players` on the `UserGWSheet` table. All the data in the column will be lost.
  - You are about to drop the column `points` on the `UserGWSheet` table. All the data in the column will be lost.
  - The `subs` column on the `UserGWSheet` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - A unique constraint covering the columns `[playerId,GW]` on the table `PlayerGWStats` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "Role" AS ENUM ('C', 'V');

-- DropForeignKey
ALTER TABLE "UserGWSheet" DROP CONSTRAINT "UserGWSheet_userId_fkey";

-- AlterTable
ALTER TABLE "Player" DROP COLUMN "totalPoints";

-- AlterTable
ALTER TABLE "UserGWSheet" DROP COLUMN "players",
DROP COLUMN "points",
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "starters" TEXT[],
ADD COLUMN     "totalPoints" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "captainId" DROP NOT NULL,
ALTER COLUMN "viceCaptainId" DROP NOT NULL,
DROP COLUMN "subs",
ADD COLUMN     "subs" TEXT[];

-- CreateTable
CREATE TABLE "PlayerTotalStats" (
    "id" TEXT NOT NULL,
    "playerId" TEXT NOT NULL,
    "points" INTEGER NOT NULL DEFAULT 0,
    "minutesPlayed" INTEGER NOT NULL DEFAULT 0,
    "goalsScored" INTEGER NOT NULL DEFAULT 0,
    "assists" INTEGER NOT NULL DEFAULT 0,
    "cleanSheets" INTEGER NOT NULL DEFAULT 0,
    "goalsConceded" INTEGER NOT NULL DEFAULT 0,
    "ownGoals" INTEGER NOT NULL DEFAULT 0,
    "penaltiesSaved" INTEGER NOT NULL DEFAULT 0,
    "penaltiesMissed" INTEGER NOT NULL DEFAULT 0,
    "yellowCards" INTEGER NOT NULL DEFAULT 0,
    "redCards" INTEGER NOT NULL DEFAULT 0,
    "saves" INTEGER NOT NULL DEFAULT 0,
    "bonus" INTEGER NOT NULL DEFAULT 0,
    "bps" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "PlayerTotalStats_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PlayerTotalStats_playerId_key" ON "PlayerTotalStats"("playerId");

-- CreateIndex
CREATE UNIQUE INDEX "PlayerGWStats_playerId_GW_key" ON "PlayerGWStats"("playerId", "GW");

-- AddForeignKey
ALTER TABLE "UserGWSheet" ADD CONSTRAINT "UserGWSheet_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerTotalStats" ADD CONSTRAINT "PlayerTotalStats_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

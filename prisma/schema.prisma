generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              String        @id @default(cuid())
  email           String        @unique
  name            String?
  phone           String?       @unique
  country         String?
  isAd<PERSON>       @default(false)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  totalPoints     Int           @default(0)
  transfersLeft   Int           @default(0)
  moneyLeft       Float         @default(100.0)
  favoriteClub    String?
  gwSheets        UserGWSheet[]
  leaguesAsAdmin  League[]      @relation("LeagueAdmins")
  leaguesAsMember League[]      @relation("UserLeagues")
}

model UserGWSheet {
  id            String   @id @default(cuid())
  userId        String
  GW            Int
  captainId     String?
  viceCaptainId String?
  createdAt     DateTime @default(now())
  starters      String[]
  totalPoints   Int      @default(0)
  updatedAt     DateTime @default(now())
  subs          String[]
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, GW])
}

model League {
  id      String @id @default(cuid())
  name    String
  admins  User[] @relation("LeagueAdmins")
  members User[] @relation("UserLeagues")
}

model Player {
  id           String            @id @default(cuid())
  name         String
  teamId       String
  currentPrice Int
  position     Position
  team         Team              @relation(fields: [teamId], references: [id])
  gwStats      PlayerGWStats[]   @relation("PlayerToGWStats")
  totalStats   PlayerTotalStats?
}

model PlayerGWStats {
  id              String @id @default(cuid())
  GW              Int
  playerId        String
  price           Int
  points          Int
  transfersIn     Int
  transfersOut    Int
  owners          Int
  minutesPlayed   Int
  goalsScored     Int
  assists         Int
  cleanSheets     Int
  goalsConceded   Int
  ownGoals        Int
  penaltiesSaved  Int
  penaltiesMissed Int
  yellowCards     Int
  redCards        Int
  saves           Int
  bonus           Int
  bps             Int
  player          Player @relation("PlayerToGWStats", fields: [playerId], references: [id])

  @@unique([playerId, GW])
}

model PlayerTotalStats {
  id              String @id @default(cuid())
  playerId        String @unique
  points          Int    @default(0)
  minutesPlayed   Int    @default(0)
  goalsScored     Int    @default(0)
  assists         Int    @default(0)
  cleanSheets     Int    @default(0)
  goalsConceded   Int    @default(0)
  ownGoals        Int    @default(0)
  penaltiesSaved  Int    @default(0)
  penaltiesMissed Int    @default(0)
  yellowCards     Int    @default(0)
  redCards        Int    @default(0)
  saves           Int    @default(0)
  bonus           Int    @default(0)
  bps             Int    @default(0)
  player          Player @relation(fields: [playerId], references: [id])
}

model Team {
  id           String    @id @default(cuid())
  name         String
  abbr         String
  logo         String
  awayFixtures Fixture[] @relation("AwayFixtures")
  homeFixtures Fixture[] @relation("HomeFixtures")
  players      Player[]
}

model Fixture {
  id          String        @id @default(cuid())
  round       Int
  kickoffTime DateTime
  teamHId     String
  teamAId     String
  teamHScore  Int?
  teamAScore  Int?
  teamA       Team          @relation("AwayFixtures", fields: [teamAId], references: [id])
  teamH       Team          @relation("HomeFixtures", fields: [teamHId], references: [id])
  stats       FixtureStat[]
}

model FixtureStat {
  id         String         @id @default(cuid())
  fixtureId  String
  identifier StatIdentifier
  h          Json
  a          Json
  fixture    Fixture        @relation(fields: [fixtureId], references: [id])
}

enum Position {
  GK
  DEF
  MID
  ATK
}

enum Role {
  C
  V
}

enum StatIdentifier {
  GOALS_SCORED
  ASSISTS
  YELLOW_CARDS
  RED_CARDS
  SAVES
  PENALTIES_SAVED
  PENALTIES_MISSED
  OWN_GOALS
  BONUS
  BPS
  CLEAN_SHEETS
}

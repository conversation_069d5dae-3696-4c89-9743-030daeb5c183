User: {
id,
email,
name,
phone,
Country,
isAdmin,
clerk_id,
createdAt,
updatedAt,
totalPoints,
transfersLeft,
moneyLeft,  
Leagues,  // array of leaguesID
GWSheets  // array of GWSheetsID
}

UserGWSheets :{
id,
userId,
G<PERSON>,
Captain,
viceCaptain,
players,
subs,
points
}

Leagues :{
id,
name,
admins, // array of usersID
members, // array of usersID
}

Player: {
id,
name,
team, // teamID
currentprice: number,
totalPoints: number,
position, // GK, DEF, MID, ATK
GWStats // array of GWStats

}

PlayerGWStats :{
id,
GW,
player, // playerID
points,
price,
numberOfTransfersIn,
numberOfTransfersOut,
numberOfOwners,
MP, // minutes played
GS, // goals scored
A,  // assists
CS, // clean sheets
GC, // goals conceded
OG, // own goals
PS, // penalties saved
PM, // penalties missed
YC, // yellow cards
RC, // red cards
S, // saves
B, // bonus 
BPS, // bonus points system
}

Team :{
id,
name,
abbr,
logo,
players, // array of playersID
}

Fixture :   {
id,
round,
kickoff_time,
team_h: Team, // teamID
team_a: Team, // teamID
team_h_score: number,
team_a_score: number,
stats: // array of FixtureStatIds
}

FixtureStat :{
id,s
fixture, // fixtureID
identifier, // goals_scored, assists, yellow_cards, red_cards, saves, own_goals, penalties_saved, penalties_missed, bonus, bonus_points_system
h, // array of FixtureStatEntry
a, // array of FixtureStatEntry
}

FixtureStatEntry :{
id,
fixtureStat, // fixtureStatID
element, // playerID
value, // number of times this stat was achieved
}

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Position {
  GK
  DEF
  MID
  ATK
}

enum StatIdentifier {
  GOALS_SCORED
  ASSISTS
  YELLOW_CARDS
  RED_CARDS
  SAVES
  PENALTIES_SAVED
  PENALTIES_MISSED
  OWN_GOALS
  BONUS
  BPS
  CLEAN_SHEETS
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  name          String?
  phone         String?
  country       String?
  isAdmin       Boolean  @default(false)
  clerkId       String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  totalPoints   Int      @default(0)
  transfersLeft Int      @default(0)
  moneyLeft     Float    @default(100.0)

  gwSheets UserGWSheet[]

  leaguesAsAdmin  League[] @relation("LeagueAdmins")
  leaguesAsMember League[] @relation("UserLeagues")
}

model UserGWSheet {
  id            String @id @default(cuid())
  userId        String
  GW            Int
  captainId     String
  viceCaptainId String
  players       Json
  subs          Json
  points        Int

  user User @relation(fields: [userId], references: [id])
}

model League {
  id      String @id @default(cuid())
  name    String
  admins  User[] @relation("LeagueAdmins")
  members User[] @relation("UserLeagues")
}

model Player {
  id           String          @id @default(cuid())
  name         String
  teamId       String
  currentPrice Int
  totalPoints  Int
  position     Position
  gwStats      PlayerGWStats[]
  team         Team            @relation(fields: [teamId], references: [id])
}

model PlayerGWStats {
  id              String @id @default(cuid())
  GW              Int
  playerId        String
  price           Int
  points          Int
  transfersIn     Int
  transfersOut    Int
  owners          Int
  minutesPlayed   Int
  goalsScored     Int
  assists         Int
  cleanSheets     Int
  goalsConceded   Int
  ownGoals        Int
  penaltiesSaved  Int
  penaltiesMissed Int
  yellowCards     Int
  redCards        Int
  saves           Int
  bonus           Int
  bps             Int

  player Player @relation(fields: [playerId], references: [id])
}

model Team {
  id           String    @id @default(cuid())
  name         String
  abbr         String
  logo         String
  players      Player[]
  homeFixtures Fixture[] @relation("HomeFixtures")
  awayFixtures Fixture[] @relation("AwayFixtures")
}

model Fixture {
  id          String   @id @default(cuid())
  round       Int
  kickoffTime DateTime
  teamHId     String
  teamAId     String
  teamHScore  Int?
  teamAScore  Int?

  teamH Team          @relation("HomeFixtures", fields: [teamHId], references: [id])
  teamA Team          @relation("AwayFixtures", fields: [teamAId], references: [id])
  stats FixtureStat[]
}

model FixtureStat {
  id         String         @id @default(cuid())
  fixtureId  String
  identifier StatIdentifier
  h          Json
  a          Json

  fixture Fixture @relation(fields: [fixtureId], references: [id])
}

const teams = [
  { name: "Club Africain", abbr: "CA", logo: "/logos/ca.png" },
  { name: "Etoile Sahel", abbr: "ESS", logo: "/logos/ess.png" },
  { name: "CS Sfaxien", abbr: "CSS", logo: "/logos/css.png" },
  { name: "US Monastir", abbr: "USM", logo: "/logos/usm.png" },
];



const generateFakeName = [
    "Ben Amor",
    "Jaziri",
    "Trabelsi",
    "Kechrida",
    "Dhaouadi",
    "Ben Hammouda",
    "Ben Salem",
    "Abid",
    "Haddad",
    "Zouari",
    "Mellouli",
    "Guerfali",
    "Bouzidi",
    "Krichen",
    "Ben Ali",
    "Bouguerra",
    "Bouhlel",
  ];

INSERT INTO "Team" (id, name, abbr, logo) VALUES
  (1, 'Club Africain', 'CA', '/logos/ca.png'),
  (2, 'Etoile Sahel', 'ESS', '/logos/ess.png'),
  (3, 'CS Sfaxien', 'CSS', '/logos/css.png'),
  (4, 'US Monastir', 'USM', '/logos/usm.png');

INSERT INTO "Player" (id, name, "teamId", "currentPrice", "totalPoints", position) VALUES
  (1, 'Dhaouadi', 1, 5.0, 10, 'GK'),
  (2, 'Abid', 1, 5.5, 12, 'DEF'),
  (3, 'Zouari', 2, 6.0, 8, 'DEF'),
  (4, 'Saidi', 2, 6.5, 15, 'DEF'),
  (5, 'Hammami', 3, 7.0, 18, 'DEF'),
  (6, 'Messaoudi', 3, 7.5, 20, 'MID'),
  (7, 'Rekik', 4, 8.0, 22, 'MID'),
  (8, 'Baccouche', 4, 8.5, 25, 'MID'),
  (9, 'Ben Amor', 1, 9.0, 28, 'MID'),
  (10, 'Jaziri', 1, 9.5, 31, 'ATK'),
  (11, 'Trabelsi', 2, 10.0, 34, 'ATK'),
  (12, 'Kechrida', 2, 10.5, 37, 'GK'),
  (13, 'Abid', 3, 11.0, 40, 'DEF'),
  (14, 'Haddad', 3, 11.5, 43, 'MID'),
  (15, 'Zouari', 4, 12.0, 46, 'ATK'),
  (16, 'Saidi', 4, 12.5, 49, 'GK'),
  (17, 'Hammami', 1, 13.0, 52, 'GK'),
  (18, 'Messaoudi', 1, 13.5, 55, 'DEF'),
  (19, 'Rekik', 2, 14.0, 58, 'DEF'),
  (20, 'Baccouche', 2, 14.5, 61, 'DEF'),
  (21, 'Ben Amor', 3, 15.0, 64, 'DEF'),
  (22, 'Jaziri', 3, 15.5, 67, 'MID'),
  (23, 'Trabelsi', 4, 16.0, 70, 'MID'),
  (24, 'Kechrida', 4, 16.5, 73, 'ATK'),
  (25, 'Abid', 1, 17.0, 76, 'ATK'),
  (26, 'Haddad', 1, 17.5, 79, 'ATK');

INSERT INTO "Fixture" (id, round, "kickoffTime", "teamHId", "teamAId", "teamHScore", "teamAScore") VALUES
  (1, 1, '2025-06-10T18:00:00Z', 1, 2, 2, 1),
  (2, 1, '2025-06-11T18:00:00Z', 3, 4, 0, 1),
  (3, 2, '2025-06-12T18:00:00Z', 1, 3, 1, 0),
  (4, 2, '2025-06-13T18:00:00Z', 2, 4, 0, 0);

INSERT INTO "FixtureStat" (id, "fixtureId", identifier, h, a) VALUES
  (1, 1, 'GOALS_SCORED', '[{"element": 1, "value": 1}, {"element": 2, "value": 1}]', '[{"element": 3, "value": 1}]'),
  (2, 1, 'ASSISTS', '[{"element": 2, "value": 1}]', '[{"element": 4, "value": 1}]'),
  (3, 2, 'GOALS_SCORED', '[{"element": 5, "value": 1}]', '[{"element": 6, "value": 1}]'),
  (4, 2, 'ASSISTS', '[{"element": 5, "value": 1}]', '[{"element": 6, "value": 1}]'),
  (5, 3, 'GOALS_SCORED', '[{"element": 7, "value": 1}]', '[{"element": 8, "value": 1}]'),
  (6, 3, 'ASSISTS', '[{"element": 7, "value": 1}]', '[{"element": 8, "value": 1}]'),
  (7, 4, 'GOALS_SCORED', '[{"element": 9, "value": 1}]', '[{"element": 10, "value": 1}]'),
  (8, 4, 'ASSISTS', '[{"element": 9, "value": 1}]', '[{"element": 10, "value": 1}]');

INSERT INTO "PlayerGWStats" (id, "GW", "playerId", price, points, "transfersIn", "transfersOut", owners, "minutesPlayed", "goalsScored", assists, "cleanSheets", "goalsConceded", "ownGoals", "penaltiesSaved", "penaltiesMissed", "yellowCards", "redCards", saves, bonus, bps) VALUES
  (1, 1, 1, 5, 10, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (2, 1, 2, 5, 12, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),   
  (3, 1, 3, 6, 8, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (4, 1, 4, 6, 15, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (5, 1, 5, 7, 18, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (6, 1, 6, 7, 20, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (7, 1, 7, 8, 22, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (8, 1, 8, 8, 25, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (9, 1, 9, 9, 28, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (10, 1, 10, 9, 31, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (11, 1, 11, 10, 34, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (12, 2, 1, 11, 37, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (13, 2, 2, 10, 40, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (14, 2, 3, 11, 43, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (15, 2, 4, 11, 46, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (16, 2, 5, 12, 49, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (17, 2, 6, 12, 52, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (18, 2, 7, 13, 55, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (19, 2, 8, 13, 58, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (20, 2, 9, 14, 61, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (21, 2, 10, 14, 64, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (22, 2, 11, 15, 67, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

INSERT INTO "User" (id, email, name, phone, country, "isAdmin", "clerkId", "createdAt", "updatedAt", "totalPoints", "transfersLeft", "moneyLeft") VALUES
  (1, '<EMAIL>', 'User 1', '1234567890', 'TN', false, 'clerk_id_1', '2025-06-05T17:04:14.000Z', '2025-06-05T17:04:14.000Z', 200, 1, 2.5);

INSERT INTO "UserGWSheet" (id, "userId", "GW", "captainId", "viceCaptainId", "players", "subs", "points") VALUES
  (1, 1, 1, '1', '2', '["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11"]', '["12", "13", "14", "15"]', 66),
  (2, 1, 2, '2', '4', '["12", "2", "3", "4", "5", "6", "7", "8", "9", "10", "15"]', '["1", "13", "14", "11"]', 77);

  INSERT INTO "League" (id, name) VALUES
  (1, 'League 1'),
  (2, 'League 2');

  INSERT INTO "_LeagueAdmins" ("A", "B") VALUES
  (1, 1),
  (2, 2);

  INSERT INTO "_UserLeagues" ("A", "B") VALUES
  (1, 1),
  (2, 2);




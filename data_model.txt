User: {
id,
email,
name,
phone,
Country,
isAdmin,
clerk_id,
createdAt,
updatedAt,
totalPoints,
transfersLeft,
moneyLeft,  
Leagues,  // array of leaguesID
GWSheets  // array of GWSheetsID
}

UserGWSheets :{
id,
userId,
G<PERSON>,
Captain,
viceCaptain,
players,
subs,
points
}

Leagues :{
id,
name,
admins, // array of usersID
members, // array of usersID
}

Player: {
id,
name,
team, // teamID
currentprice: number,
totalPoints: number,
position, // GK, DEF, MID, ATK
GWStats // array of GWStats

}

PlayerGWStats :{
id,
GW,
player, // playerID
points,
price,
numberOfTransfersIn,
numberOfTransfersOut,
numberOfOwners,
MP, // minutes played
GS, // goals scored
A,  // assists
CS, // clean sheets
GC, // goals conceded
OG, // own goals
PS, // penalties saved
PM, // penalties missed
YC, // yellow cards
RC, // red cards
S, // saves
B, // bonus 
BPS, // bonus points system
}

Team :{
id,
name,
abbr,
logo,
players, // array of playersID
}

Fixture :   {
id,
round,
kickoff_time,
team_h: Team, // teamID
team_a: Team, // teamID
team_h_score: number,
team_a_score: number,
stats: // array of FixtureStatIds
}

FixtureStat :{
id,s
fixture, // fixtureID
identifier, // goals_scored, assists, yellow_cards, red_cards, saves, own_goals, penalties_saved, penalties_missed, bonus, bonus_points_system
h, // array of FixtureStatEntry
a, // array of FixtureStatEntry
}

FixtureStatEntry :{
id,
fixtureStat, // fixtureStatID
element, // playerID
value, // number of times this stat was achieved
}

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Position {
  GK
  DEF
  MID
  ATK
}

enum StatIdentifier {
  GOALS_SCORED
  ASSISTS
  YELLOW_CARDS
  RED_CARDS
  SAVES
  PENALTIES_SAVED
  PENALTIES_MISSED
  OWN_GOALS
  BONUS
  BPS
  CLEAN_SHEETS
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  name          String?
  phone         String?
  country       String?
  isAdmin       Boolean  @default(false)
  clerkId       String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  totalPoints   Int      @default(0)
  transfersLeft Int      @default(0)
  moneyLeft     Float    @default(100.0)

  gwSheets UserGWSheet[]

  leaguesAsAdmin  League[] @relation("LeagueAdmins")
  leaguesAsMember League[] @relation("UserLeagues")
}

model UserGWSheet {
  id            String @id @default(cuid())
  userId        String
  GW            Int
  captainId     String
  viceCaptainId String
  players       Json
  subs          Json
  points        Int

  user User @relation(fields: [userId], references: [id])
}

model League {
  id      String @id @default(cuid())
  name    String
  admins  User[] @relation("LeagueAdmins")
  members User[] @relation("UserLeagues")
}

model Player {
  id           String          @id @default(cuid())
  name         String
  teamId       String
  currentPrice Int
  totalPoints  Int
  position     Position
  gwStats      PlayerGWStats[]
  team         Team            @relation(fields: [teamId], references: [id])
}

model PlayerGWStats {
  id              String @id @default(cuid())
  GW              Int
  playerId        String
  price           Int
  points          Int
  transfersIn     Int
  transfersOut    Int
  owners          Int
  minutesPlayed   Int
  goalsScored     Int
  assists         Int
  cleanSheets     Int
  goalsConceded   Int
  ownGoals        Int
  penaltiesSaved  Int
  penaltiesMissed Int
  yellowCards     Int
  redCards        Int
  saves           Int
  bonus           Int
  bps             Int

  player Player @relation(fields: [playerId], references: [id])
}

model Team {
  id           String    @id @default(cuid())
  name         String
  abbr         String
  logo         String
  players      Player[]
  homeFixtures Fixture[] @relation("HomeFixtures")
  awayFixtures Fixture[] @relation("AwayFixtures")
}

model Fixture {
  id          String   @id @default(cuid())
  round       Int
  kickoffTime DateTime
  teamHId     String
  teamAId     String
  teamHScore  Int?
  teamAScore  Int?

  teamH Team          @relation("HomeFixtures", fields: [teamHId], references: [id])
  teamA Team          @relation("AwayFixtures", fields: [teamAId], references: [id])
  stats FixtureStat[]
}

model FixtureStat {
  id         String         @id @default(cuid())
  fixtureId  String
  identifier StatIdentifier
  h          Json
  a          Json

  fixture Fixture @relation(fields: [fixtureId], references: [id])
}

INSERT INTO "Team" (id, name, abbr, logo, jersey) VALUES
  (1, 'Club Africain', 'CA', '/logos/ca.png', '/jerseys/ca.png'),
  (2, 'Etoile Sportive du Sahel', 'ESS', '/logos/ess.png', '/jerseys/ess.png'),
  (3, 'Club Sportif Sfaxien', 'CSS', '/logos/css.png', '/jerseys/css.png'),
  (4, 'Union Sportive Monastirienne', 'USM', '/logos/usm.png', '/jerseys/usm.png'),
  (5, 'Espérance Sportive de Tunis', 'EST', '/logos/est.png', '/jerseys/est.png'),
  (6, 'Stade Tunisien', 'ST', '/logos/st.png', '/jerseys/st.png'),
  (7, 'ES Métlaoui', 'ESM', '/logos/esm.png', '/jerseys/esm.png'),
  (8, 'Club Athlétique Bizertin', 'CAB', '/logos/cab.png', '/jerseys/cab.png'),
  (9, 'AS Soliman', 'ASS', '/logos/ass.png', '/jerseys/ass.png'),
  (10, 'US Ben Guerdane', 'USBG', '/logos/usb.png', '/jerseys/usb.png'),
  (11, 'Olympique Béja', 'OB', '/logos/ob.png', '/jerseys/ob.png'),
  (12, 'AS Gabés', 'ASG', '/logos/asg.png', '/jerseys/asg.png'),
  (13, 'JS Omrane', 'JSO', '/logos/jso.png', '/jerseys/jso.png'),
  (14, 'AS Marsa', 'ASM', '/logos/asm.png', '/jerseys/asm.png'),
  (15, 'JS Kairouanaise', 'JSK', '/logos/jsk.png', '/jerseys/jsk.png'),
  (16, 'ES Zarzis', 'ESZ', '/logos/esz.png', '/jerseys/esz.png');






// generate diffrent currentPrice for players between 4 and 10
// Attaquant = ATK price between 6 and 9
// Milieu = MID price between 5 and 8
// Défenseur = DEF price between 4 and 6
// Gardien = GK price between 4 and 6
INSERT INTO "Player" (id, name, "teamId", "currentPrice", position) VALUES
  (1, 'Rodrigues', 5, 9.8, 'ATK'),
  (2, 'Jabri', 5, 7.2, 'ATK'),
  (3, 'Maacha', 5, 8.2, 'ATK'),
  (4, 'Jbéli', 5, 5.8, 'ATK'),
  (5, 'Sahli', 5, 6.8, 'ATK'),
  (6, 'Kada', 5, 7.5, 'ATK'),
  (7, 'Diakite', 5, 8.1, 'ATK'),
  (8, 'Hamrouni', 5, 7.9, 'ATK'),
  (9, 'Diarra', 5, 6.5, 'ATK'),
  (10, 'Belaïli', 5, 9.1, 'ATK'),
  (11, 'Sasse', 5, 7.7, 'MID'),
  (12, 'Mokwana', 5, 8.3, 'MID'),
  (13, 'Aholou', 5, 7.1, 'MID'),
  (14, 'Konate', 5, 8.5, 'MID'),
  (15, 'Mouhli', 5, 7.3, 'MID'),
  (16, 'Derbali', 5, 6.9, 'MID'),
  (17, 'Ayeb', 5, 7.6, 'MID'),
  (18, 'Oumarou', 5, 8.0, 'MID'),
  (19, 'Guenichi', 5, 7.4, 'MID'),
  (20, 'Dhaou', 5, 6.7, 'MID'),
  (21, 'Tougai', 5, 7.0, 'DEF'),
  (22, 'Meriah', 5, 8.4, 'DEF'),
  (23, 'B.Mohamed', 5, 7.2, 'DEF'),
  (24, 'Bouzaiene', 5, 8.6, 'DEF'),
  (25, 'Bouchniba', 5, 7.8, 'DEF'),
  (26, 'Jelassi', 5, 6.6, 'DEF'),
  (27, 'B.Ali', 5, 7.9, 'DEF'),
  (28, 'Fadaa', 5, 8.7, 'DEF'),
  (29, 'Smiri', 5, 7.5, 'DEF'),
  (30, 'Kodhaii', 5, 8.2, 'DEF'),
  (31, 'Memmiche', 5, 6.8, 'DEF'),
  (32, 'B.Said', 5, 7.3, 'GK'),
  (33, 'Debchi', 5, 8.0, 'GK'),
  (34, 'Kinzumbi', 1, 7.0, 'ATK'),
  (35, 'Labidi', 1, 6.5, 'ATK'),
  (36, 'A.Malek', 1, 8.0, 'ATK'),
  (37, 'Mesmary', 1, 7.5, 'ATK'),
  (38, 'Kooh', 1, 8.5, 'ATK'),
  (39, 'Garreb', 1, 7.2, 'ATK'),
  (40, 'Srarfi', 1, 8.1, 'MID'),
  (41, 'Khadraoui', 1, 7.9, 'MID'),
  (42, 'Zemzemi', 1, 6.8, 'MID'),
  (43, 'Kelaleche', 1, 7.3, 'MID'),
  (44, 'Semakula', 1, 8.0, 'MID'),
  (45, 'Khalil', 1, 7.6, 'MID'),
  (46, 'Sghaier', 1, 6.9, 'MID'),
  (47, 'Mohammed', 1, 7.4, 'MID'),
  (48, 'Mahmoud', 1, 6.7, 'MID'),
  (49, 'Ajimi', 1, 7.0, 'MID'),
  (50, 'Yusuf', 1, 7.8, 'DEF'),
  (51, 'Zaalouni', 1, 7.6, 'DEF'),
  (52, 'Cherifi', 1, 6.9, 'DEF'),
  (53, 'B.Abda', 1, 7.4, 'DEF'),
  (54, 'Shili', 1, 6.7, 'DEF'),
  (55, 'Tene', 1, 7.2, 'DEF'),
  (56, 'Bouabid', 1, 8.0, 'DEF'),
  (57, 'Bedoui', 1, 7.5, 'DEF'),
  (58, 'Sghaier', 1, 6.8, 'DEF'),
  (59, 'Ghrissi', 1, 7.3, 'DEF'),
  (60, 'Hassen', 1, 8.1, 'DEF'),
  (61, 'Arfaoui', 1, 7.9, 'GK'),
  (62, 'Yeferni', 1, 6.6, 'GK'),
  (63, 'Maghzaoui', 1, 7.2, 'GK'),
  (64, 'Mastouri', 4, 7.0, 'ATK'),
  (65, 'Abdelli', 4, 6.5, 'ATK'),
  (66, 'Elhmidi', 4, 8.0, 'ATK'),
  (67, 'Bouatay', 4, 7.5, 'ATK'),
  (68, 'Khalifa', 4, 7.2, 'ATK'),
  (69, 'Michael', 4, 7.9, 'MID'),
  (70, 'Dridi', 4, 6.8, 'MID'),
  (71, 'Orkuma', 4, 8.5, 'MID'),
  (72, 'B.H.Ali', 4, 7.3, 'MID'),
  (73, 'Ganouni', 4, 8.1, 'MID'),
  (74, 'Trayi', 4, 6.9, 'MID'),
  (75, 'Harzi', 4, 7.6, 'MID'),
  (76, 'Diané', 4, 7.4, 'MID'),
  (77, 'Samb', 4, 6.7, 'MID'),
  (78, 'Souissi', 4, 7.0, 'MID'),
  (79, 'Slama', 4, 7.8, 'MID'),
  (80, 'Ghorbel', 4, 7.6, 'DEF'),
  (81, 'Jaziri', 4, 6.9, 'DEF'),
  (82, 'Soltani', 4, 7.4, 'DEF'),
  (83, 'Zeguei', 4, 6.7, 'DEF'),
  (84, 'Salhi', 4, 7.2, 'DEF'),
  (85, 'Miladi', 4, 8.0, 'DEF'),
  (86, 'Azzouz', 4, 7.5, 'DEF'),
  (87, 'Hallaoui', 4, 6.8, 'GK'),
  (88, 'Besbes', 4, 4.3, 'GK'),
  (89, 'Chaouat', 2, 9.5, 'ATK'),
  (90, 'Aouani', 2, 8.2, 'ATK'),
  (91, 'Kanté', 2, 7.9, 'ATK'),
  (92, 'Chamakhi', 2, 9.1, 'ATK'),
  (93, 'Smichi', 2, 8.5, 'ATK'),
  (94, 'Souissi', 2, 8.0, 'ATK'),
  (95, 'Anane', 2, 7.7, 'ATK'),
  (96, 'Hassine', 2, 9.3, 'ATK'),
  (97, 'Bousbaai', 2, 8.8, 'ATK'),
  (98, 'Abid', 2, 7.5, 'MID'),
  (99, 'B.Amor', 2, 8.0, 'MID'),
  (100, 'B.Choug', 2, 7.8, 'MID'),
  (101, 'Baayou', 2, 8.3, 'MID'),
  (102, 'Karoui', 2, 7.6, 'MID'),
  (103, 'Gbo', 2, 8.1, 'MID'),
  (104, 'Chouchen', 2, 7.9, 'MID'),
  (105, 'Ouertani', 2, 8.4, 'MID'),
  (106, 'Jebali', 2, 7.7, 'MID'),
  (107, 'Chihi', 2, 8.2, 'MID'),
  (108, 'A.Jebali', 2, 7.9, 'MID'),
  (109, 'Hnid', 2, 8.0, 'DEF'),
  (110, 'C.Amar', 2, 7.8, 'DEF'),
  (111, 'B.Ali', 2, 8.3, 'DEF'),
  (112, 'Dagdoug', 2, 7.7, 'DEF'),
  (113, 'Ghedamsi', 2, 7.6, 'DEF'),
  (114, 'Boughattas', 2, 8.1, 'DEF'),
  (115, 'Naouali', 2, 7.9, 'DEF'),
  (116, 'B.Abdallah', 2, 8.4, 'DEF'),
  (117, 'Khardani', 2, 7.8, 'GK'),


  








INSERT INTO "Fixture" (id, round, "kickoffTime", "teamHId", "teamAId", "teamHScore", "teamAScore") VALUES
  (1, 1, '2025-06-10T18:00:00Z', 1, 2, 2, 1),
  (2, 1, '2025-06-11T18:00:00Z', 3, 4, 0, 1),
  (3, 2, '2025-06-12T18:00:00Z', 1, 3, 1, 0),
  (4, 2, '2025-06-13T18:00:00Z', 2, 4, 0, 0);

INSERT INTO "FixtureStat" (id, "fixtureId", identifier, h, a) VALUES
  (1, 1, 'GOALS_SCORED', '[{"element": 1, "value": 1}, {"element": 2, "value": 1}]', '[{"element": 3, "value": 1}]'),
  (2, 1, 'ASSISTS', '[{"element": 2, "value": 1}]', '[{"element": 4, "value": 1}]'),
  (3, 2, 'GOALS_SCORED', '[{"element": 5, "value": 1}]', '[{"element": 6, "value": 1}]'),
  (4, 2, 'ASSISTS', '[{"element": 5, "value": 1}]', '[{"element": 6, "value": 1}]'),
  (5, 3, 'GOALS_SCORED', '[{"element": 7, "value": 1}]', '[{"element": 8, "value": 1}]'),
  (6, 3, 'ASSISTS', '[{"element": 7, "value": 1}]', '[{"element": 8, "value": 1}]'),
  (7, 4, 'GOALS_SCORED', '[{"element": 9, "value": 1}]', '[{"element": 10, "value": 1}]'),
  (8, 4, 'ASSISTS', '[{"element": 9, "value": 1}]', '[{"element": 10, "value": 1}]');

INSERT INTO "PlayerGWStats" (id, "GW", "playerId", price, points, "transfersIn", "transfersOut", owners, "minutesPlayed", "goalsScored", assists, "cleanSheets", "goalsConceded", "ownGoals", "penaltiesSaved", "penaltiesMissed", "yellowCards", "redCards", saves, bonus, bps) VALUES
  (1, 1, 1, 5, 10, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (2, 1, 2, 5, 12, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),   
  (3, 1, 3, 6, 8, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (4, 1, 4, 6, 15, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (5, 1, 5, 7, 18, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (6, 1, 6, 7, 20, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (7, 1, 7, 8, 22, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (8, 1, 8, 8, 25, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (9, 1, 9, 9, 28, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (10, 1, 10, 9, 31, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (11, 1, 11, 10, 34, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (12, 2, 1, 11, 37, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (13, 2, 2, 10, 40, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (14, 2, 3, 11, 43, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (15, 2, 4, 11, 46, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (16, 2, 5, 12, 49, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (17, 2, 6, 12, 52, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (18, 2, 7, 13, 55, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (19, 2, 8, 13, 58, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (20, 2, 9, 14, 61, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (21, 2, 10, 14, 64, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0),
  (22, 2, 11, 15, 67, 0, 0, 10, 90, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

INSERT INTO "User" (id, email, name, phone, country, "isAdmin", "clerkId", "createdAt", "updatedAt", "totalPoints", "transfersLeft", "moneyLeft") VALUES
  (1, '<EMAIL>', 'User 1', '1234567890', 'TN', false, 'clerk_id_1', '2025-06-05T17:04:14.000Z', '2025-06-05T17:04:14.000Z', 200, 1, 2.5);

INSERT INTO "UserGWSheet" (id, "userId", "GW", "captainId", "viceCaptainId", "players", "subs", "points") VALUES
  (1, 1, 1, '1', '2', '["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11"]', '["12", "13", "14", "15"]', 66),
  (2, 1, 2, '2', '4', '["12", "2", "3", "4", "5", "6", "7", "8", "9", "10", "15"]', '["1", "13", "14", "11"]', 77);

  INSERT INTO "League" (id, name) VALUES
  (1, 'League 1'),
  (2, 'League 2');

  INSERT INTO "_LeagueAdmins" ("A", "B") VALUES
  (1, 1),
  (2, 2);

  INSERT INTO "_UserLeagues" ("A", "B") VALUES
  (1, 1),
  (2, 2);




import { useDataStore } from '@/store/dataStore';

/**
 * Hook that provides access to all core application data
 * Uses the centralized dataStore for players, teams, gameweeks, and active gameweek
 */
export function useData() {
  const {
    gameweeks,
    activeGameweek,
    players,
    teams,
    isLoading,
    error,
    fetchData,
    getPlayerById,
    getTeamById,
    getGameweekById,
  } = useDataStore();

  return {
    // Core data
    gameweeks,
    activeGameweek,
    players,
    teams,

    // Loading states
    isLoading,
    error,

    // Actions
    fetchData,

    // Helper functions
    getPlayerById,
    getTeamById,
    getGameweekById,

    // Enhanced helper functions
    getPlayerWithTeam: (playerId: string) => {
      const player = getPlayerById(playerId);
      if (!player) return null;

      const team = getTeamById(player.teamId);
      return {
        ...player,
        team,
      };
    },

    getTeamPlayers: (teamId: string) => {
      return players.filter(player => player.teamId === teamId);
    },

    getPlayersByPosition: (position: string) => {
      return players.filter(player => player.position === position);
    },
  };
}

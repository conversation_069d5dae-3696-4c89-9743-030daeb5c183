"use client";

import { useMemo } from "react";
import { useFantasyStore } from "@/store/useFantasyStore";

export function useFantasy(
) {
  const {
    // Active gameweek state
    activeGameweek,
    gameweeks,
    fetchActiveGameweek,
    fetchGameweeks,

    // View state
    view,
    setView,

    // Player state
    players,
    marketPlayers,
    isLoading,
    error,

    // Teams info
    teams,

    // Filter states
    filterPosition,
    filterTeam,
    filterPrice,
    setFilterPosition,
    setFilterTeam,
    setFilterPrice,

    // Team state
    starters,
    subs,
    starterIds,
    subIds,
    removedPlayers,
    captainId,
    viceCaptainId,
    hasChanges,
    userGWSheet,
    originalStarters,
    originalSubs,

    // Actions
    fetchPlayers,
    fetchTeam,
    updateMarket,
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
    handleRemove,
    handleAdd,
    handleRestore,
    saveTeam,
    fetchTeams,
  } = useFantasyStore();


  const saveTeamWrapper = async (
    userId: string | null,
    round?: string | null,
    isCreate: boolean = false
  ) => {
    if (!userId) return false;
    // Use the gameweek number as string, not the gameweek ID
    const gameweekToUse = round || (activeGameweek ? activeGameweek.gameWeek.id : undefined);
    return saveTeam(userId, gameweekToUse, isCreate);
  };

  // Player filter functions
  const filteredMarket = useMemo(() => {
    return marketPlayers.filter((p) => {
      if (filterPosition && p.position !== filterPosition) return false;
      if (filterTeam !== "all" && filterTeam && p.team.abbr !== filterTeam)
        return false;
      if (Number(p.currentPrice) > filterPrice) return false;
      return true;
    });
  }, [marketPlayers, filterPosition, filterTeam, filterPrice]);

  const availableTeams = useMemo(() => {
    return Array.from(new Set(players.map((p) => p.team.abbr)));
  }, [players]);

  const priceRangeMax: number = useMemo(() => {
    if (marketPlayers.length === 0) return 20;
    const prices = marketPlayers.map((p) => Number(p.currentPrice));
    return Math.ceil(Math.max(...prices));
  }, [marketPlayers]);

  // Stats calculation
  const stats = {
    totalGWPoints: userGWSheet?.totalPoints ?? 0,
    avgPoints: 0,
    highestPoints: 0,
    roundRank: 0,
    transfersRemaining: 0,
    squadValue: [starters, subs]
      .flat()
      .reduce((sum, p) => sum + Number(p.currentPrice), 0),
  };

  const getGameweekBySeasonAndGW = (season: string, gw: number) => {
    return gameweeks.find(
      (gameweek) => gameweek.season === season && gameweek.GW === gw
    );
  };

  return {
    // Active gameweek
    activeGameweek,
    gameweeks,
    getGameweekBySeasonAndGW,

    // View state
    view,
    setView,

    // Player data
    players,
    filteredMarket,

    // Teams data
    teams,

    // Team data
    starters,
    subs,
    starterIds,
    subIds,
    removedPlayers,
    captainId,
    viceCaptainId,
    stats,
    originalStarters,
    originalSubs,

    // Status
    isLoading,
    error,
    hasChanges,

    // Filter state and actions
    filterPosition,
    setFilterPosition,
    filterTeam,
    setFilterTeam,
    filterPrice,
    setFilterPrice,
    priceRangeMax,
    availableTeams,

    // Team actions
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
    handleRemove,
    handleAdd,
    handleRestore,
    saveTeam: saveTeamWrapper,
    updateMarket,

    // API actions
    fetchPlayers,
    fetchTeam,
    fetchTeams,
    fetchGameweeks,
    fetchActiveGameweek,
  };
}

"use client";

import { useEffect, useMemo } from "react";
import { useFantasyStore } from "@/store/useFantasyStore";

export function useFantasy(
  userId: string | null = null,
  round: string | null = null,
  isCreate: boolean = false
) {
  const {
    view,
    setView,

    // Player state
    players,
    marketPlayers,
    isLoading,
    error,

    // Filter states
    filterPosition,
    filterTeam,
    filterPrice,
    setFilterPosition,
    setFilterTeam,
    setFilterPrice,

    // Team state
    starters,
    subs,
    starterIds,
    subIds,
    removedPlayers,
    captainId,
    viceCaptainId,
    hasChanges,
    userGWSheet,
    originalStarters,
    originalSubs,

    // Actions
    fetchPlayers,
    fetchTeam,
    updateMarket,
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
    handleRemove,
    handleAdd,
    handleRestore,
    saveTeam,
  } = useFantasyStore();

  // Initialize data
  useEffect(() => {
    if (players.length === 0) fetchPlayers();
  }, [userId, round, players, fetchPlayers]);

  useEffect(() => {
    if (players.length > 0) fetchTeam(userId, round, isCreate);
  }, [userId, round, isCreate, players, fetchTeam]);

  const saveTeamWrapper = async (
    userId: string | null,
    round: string | null,
    isCreate: boolean = false
  ) => {
    if (!userId || !round) return false;
    return saveTeam(userId, round, isCreate);
  };

  // Player filter functions
  const filteredMarket = useMemo(() => {
    return marketPlayers.filter((p) => {
      if (filterPosition && p.position !== filterPosition) return false;
      if (filterTeam !== "all" && filterTeam && p.team.abbr !== filterTeam)
        return false;
      if (p.currentPrice > filterPrice) return false;
      return true;
    });
  }, [marketPlayers, filterPosition, filterTeam, filterPrice]);

  const availableTeams = useMemo(() => {
    return Array.from(new Set(players.map((p) => p.team.abbr)));
  }, [players]);

  const priceRangeMax: number = useMemo(() => {
    if (marketPlayers.length === 0) return 20;
    const prices = marketPlayers.map((p) => p.currentPrice);
    return Math.ceil(Math.max(...prices));
  }, [marketPlayers]);

  // Stats calculation
  const stats = {
    totalGWPoints: userGWSheet?.totalPoints ?? 0,
    avgPoints: 0,
    highestPoints: 0,
    roundRank: 0,
    transfersRemaining: 0,
    squadValue: [starters, subs]
      .flat()
      .reduce((sum, p) => sum + p.currentPrice, 0),
  };

  return {
    // View state
    view,
    setView,

    // Player data
    players,
    filteredMarket,

    // Team data
    starters,
    subs,
    starterIds,
    subIds,
    removedPlayers,
    captainId,
    viceCaptainId,
    stats,
    originalStarters,
    originalSubs,

    // Status
    isLoading,
    error,
    hasChanges,

    // Filter state and actions
    filterPosition,
    setFilterPosition,
    filterTeam,
    setFilterTeam,
    filterPrice,
    setFilterPrice,
    priceRangeMax,
    availableTeams,

    // Team actions
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
    handleRemove,
    handleAdd,
    handleRestore,
    saveTeam: saveTeamWrapper,
    updateMarket,
  };
}

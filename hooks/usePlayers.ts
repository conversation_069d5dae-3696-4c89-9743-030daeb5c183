"use client";

import { useEffect, useMemo } from "react";
import { useFantasyStore } from "@/store/useFantasyStore";

export function usePlayers() {
  const {
    players,
    marketPlayers,
    isLoading,
    error,
    filterPosition,
    filterTeam,
    filterPrice,
    setFilterPosition,
    setFilterTeam,
    setFilterPrice,
    updateMarket,
    fetchPlayers
  } = useFantasyStore();

  useEffect(() => {
    fetchPlayers();
  }, [fetchPlayers]);

  // Apply filters to market data
  const filteredMarket = useMemo(() => {
    return marketPlayers.filter((p) => {
      // Filter by position
      if (filterPosition && p.position !== filterPosition) return false;

      // Filter by team
      if (filterTeam && p.teamId !== filterTeam) return false;

      // Filter by price - include players with price LESS THAN OR EQUAL TO the max
      if (p.currentPrice > filterPrice) return false;

      return true;
    });
  }, [marketPlayers, filterPosition, filterTeam, filterPrice]);

  // Get unique teams for filter dropdown
  const availableTeams = useMemo(() => {
    return Array.from(new Set(players.map((p) => p.teamId)));
  }, [players]);

  // Get max price for range slider
  const priceRangeMax: number = useMemo(() => {
    if (marketPlayers.length === 0) return 20;
    const prices = marketPlayers.map((p) => p.currentPrice);
    return Math.ceil(Math.max(...prices));
  }, [marketPlayers]);

  return {
    players,
    filteredMarket,
    isLoading,
    error,
    filterPosition,
    setFilterPosition,
    filterTeam,
    setFilterTeam,
    filterPrice,
    setFilterPrice,
    priceRangeMax,
    availableTeams,
    updateMarket,
  };
}

import { useState, useEffect } from 'react';

export interface ActiveGameWeekData {
  id: string;
  gameweekId: string;
  gameWeek: {
    id: string;
    season: string;
    GW: number;
  };
}

export function useActiveGameweek() {
  const [activeGameweek, setActiveGameweek] = useState<ActiveGameWeekData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchActiveGameweek = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/activeGameweek');
      
      if (!response.ok) {
        throw new Error('Failed to fetch active gameweek');
      }
      
      const data = await response.json();
      setActiveGameweek(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error fetching active gameweek:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchActiveGameweek();
  }, []);

  const refetch = () => {
    fetchActiveGameweek();
  };

  return {
    activeGameweek,
    isLoading,
    error,
    refetch,
    // Use gameweek ID (string) instead of GW number (int)
    currentGameweekId: activeGameweek?.gameweekId || null,
    currentGW: activeGameweek?.gameWeek.GW || null,
    currentSeason: activeGameweek?.gameWeek.season || null,
  };
}

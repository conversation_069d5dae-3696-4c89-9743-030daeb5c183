import { useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { useLeagueStore, League } from "@/store/leagueStore";
import { toast } from "sonner";

export const useLeague = () => {
  const { user } = useUser();
  const {
    leagues,
    currentLeague,
    leaguePlayers,
    loading,
    error,
    searchTerm,
    createDialogOpen,
    joinDialogOpen,
    newLeagueName,
    joinCode,
    copiedLeagueId,
    setSearchTerm,
    setCreateDialogOpen,
    setJoinDialogOpen,
    setNewLeagueName,
    setJoinCode,
    setCopiedLeagueId,
    fetchLeagues,
    setCurrentLeague,
    setLeaguePlayers,
  } = useLeagueStore();

  // Auto-fetch leagues when user is available
  useEffect(() => {
    if (leagues.length === 0 && !loading) {
      fetchLeagues();
    }
  }, []);

  // Helper functions
  const isUserAdmin = (league: League) => {
    return league.admins.some((admin) => admin.id === user?.id);
  };

  const isUserMember = (league: League) => {
    return league.members.some((member) => member.id === user?.id);
  };

  const getFilteredLeagues = () => {
    return leagues.filter((league) =>
      league.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const copyLeagueCode = async (leagueId: string) => {
    try {
      await navigator.clipboard.writeText(leagueId);
      setCopiedLeagueId(leagueId);
      toast.success("League code copied to clipboard!");
      setTimeout(() => setCopiedLeagueId(null), 2000);
    } catch {
      toast.error("Failed to copy league code");
    }
  };

  const createLeague = async (name: string): Promise<boolean> => {
    if (!name.trim()) {
      toast.error("Please enter a league name");
      return false;
    }

    const success = await createLeague(name.trim());
    if (success) {
      toast.success("League created successfully!");
    } else if (error) {
      toast.error(error);
    }
    return success;
  };

  const joinLeague = async (code: string): Promise<boolean> => {
    if (!code.trim()) {
      toast.error("Please enter a league code");
      return false;
    }

    const success = await joinLeague(code.trim());
    if (success) {
      toast.success("Joined league successfully!");
    } else if (error) {
      toast.error(error);
    }
    return success;
  };

  const leaveLeague = async (leagueId: string): Promise<boolean> => {
    const success = await leaveLeague(leagueId);
    if (success) {
      toast.success("Left league successfully!");
    } else if (error) {
      toast.error(error);
    }
    return success;
  };

  const updateLeague = async (
    id: string,
    data: { name: string }
  ): Promise<boolean> => {
    if (!data.name.trim()) {
      toast.error("Please enter a league name");
      return false;
    }

    const success = await updateLeague(id, { name: data.name.trim() });
    if (success) {
      toast.success("League updated successfully!");
    } else if (error) {
      toast.error(error);
    }
    return success;
  };

  const deleteLeague = async (id: string): Promise<boolean> => {
    const success = await deleteLeague(id);
    if (success) {
      toast.success("League deleted successfully!");
    } else if (error) {
      toast.error(error);
    }
    return success;
  };

  const removeMember = async (
    leagueId: string,
    memberId: string
  ): Promise<boolean> => {
    const success = await removeMember(leagueId, memberId);
    if (success) {
      toast.success("Member removed successfully!");
    } else if (error) {
      toast.error(error);
    }
    return success;
  };

  const toggleAdmin = async (
    leagueId: string,
    memberId: string,
    isCurrentlyAdmin: boolean
  ): Promise<boolean> => {
    const success = await toggleAdmin(leagueId, memberId, isCurrentlyAdmin);
    if (success) {
      toast.success(
        isCurrentlyAdmin
          ? "Admin rights removed successfully!"
          : "Admin rights granted successfully!"
      );
    } else if (error) {
      toast.error(error);
    }
    return success;
  };

  const fetchLeague = async (id: string) => {
    await fetchLeague(id);
    if (error) {
      toast.error(error);
    }
  };

  const fetchLeaguePlayers = async (id: string) => {
    await fetchLeaguePlayers(id);
    if (error) {
      toast.error(error);
    }
  };

  return {
    // State
    leagues: leagues,
    currentLeague: currentLeague,
    leaguePlayers: leaguePlayers,
    loading: loading,
    error: error,

    // UI State
    searchTerm: searchTerm,
    createDialogOpen: createDialogOpen,
    joinDialogOpen: joinDialogOpen,
    newLeagueName: newLeagueName,
    joinCode: joinCode,
    copiedLeagueId: copiedLeagueId,

    // Computed values
    filteredLeagues: getFilteredLeagues(),

    // UI Actions
    setSearchTerm: setSearchTerm,
    setCreateDialogOpen: setCreateDialogOpen,
    setJoinDialogOpen: setJoinDialogOpen,
    setNewLeagueName: setNewLeagueName,
    setJoinCode: setJoinCode,
    setCopiedLeagueId: setCopiedLeagueId,

    // API Actions (with toast notifications)
    createLeague,
    joinLeague,
    leaveLeague,
    updateLeague,
    deleteLeague,
    removeMember,
    toggleAdmin,
    fetchLeague,
    fetchLeaguePlayers,

    // Helper functions
    isUserAdmin,
    isUserMember,
    copyLeagueCode,

    // Store actions
    setCurrentLeague,
    setLeaguePlayers,
  };
};

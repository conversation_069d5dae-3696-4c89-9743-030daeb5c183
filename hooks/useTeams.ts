"use client";

import { useEffect } from "react";
import { Player } from "@prisma/client";
import { useFantasyStore } from "@/store/useFantasyStore";

export function useTeamPoints(userId: string, round: string) {
  const {
    players,
    starters,
    subs,
    starterIds,
    subIds,
    removedPlayers,
    captainId,
    viceCaptainId,
    isLoading,
    error,
    hasChanges,
    userGWSheet,
    fetchPlayers,
    fetchTeam,
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
    handleRemove,
    handleAdd,
    saveTeam
  } = useFantasyStore();

  useEffect(() => {
    fetchPlayers();
  }, [fetchPlayers]);

  useEffect(() => {
    if (players.length > 0) {
      fetchTeam(userId, round);
    }
  }, [userId, round, players, fetchTeam]);

  const isStarter = (p: Player) => {
    return starters.some((s) => s.id === p.id);
  };

  const isSub = (p: Player) => {
    return subs.some((s) => s.id === p.id);
  };

  const isRemoved = (p: Player) => {
    return !isStarter(p) && !isSub(p);
  };

  const getSubsList = (p: Player | null): Player[] => {
    if (!p) return [];
    if (p.position === "GK") {
      return subs.filter((sub) => sub.position === "GK");
    } else {
      return subs.filter((sub) => sub.position !== "GK");
    }
  };

  const saveTeamWrapper = async (isCreate: boolean = false) => {
    return saveTeam(userId, round, isCreate);
  };

  const stats = {
    totalGWPoints: userGWSheet?.totalPoints ?? 0,
    avgPoints: 0,
    highestPoints: 0,
    roundRank: 0,
    transfersRemaining: 0,
    squadValue: [starters, subs]
      .flat()
      .reduce((sum, p) => sum + p.currentPrice, 0),
  };

  return {
    starters,
    subs,
    starterIds,
    subIds,
    stats,
    isLoading,
    error,
    hasChanges,
    captainId,
    viceCaptainId,
    removedPlayers,
    isStarter,
    isSub,
    isRemoved,
    getSubsList,
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
    handleRemove,
    handleAdd,
    saveTeam: saveTeamWrapper,
  };
}

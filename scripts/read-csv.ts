
import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse';
import { Team, Player } from '@prisma/client';

function readCSVFile<T>(filePath: string): Promise<T[]> {
  return new Promise((resolve, reject) => {
    const records: T[] = [];
    fs.createReadStream(filePath)
      .pipe(parse({
        delimiter: '|',
        columns: true, // assumes first line is header
        trim: true,
        skip_empty_lines: true,
      }))
      .on('data', (row) => {
        records.push(row);
      })
      .on('end', () => resolve(records))
      .on('error', (err) => reject(err));
  });
}

export async function main() : Promise<{ teams: Team[], players: Record<string, Player[]> }> {
  try {
    const teamsFile = path.resolve(__dirname, 'teams.csv');
    const ca_playersFile = path.resolve(__dirname, 'ca_players.csv');
    const ess_playersFile = path.resolve(__dirname, 'ess_players.csv');
    const css_playersFile = path.resolve(__dirname, 'css_players.csv');
    const usm_playersFile = path.resolve(__dirname, 'usm_players.csv');
    const est_playersFile = path.resolve(__dirname, 'est_players.csv');
    const st_playersFile = path.resolve(__dirname, 'st_players.csv');
    const esm_playersFile = path.resolve(__dirname, 'esm_players.csv');
    const cab_playersFile = path.resolve(__dirname, 'cab_players.csv');
    const ass_playersFile = path.resolve(__dirname, 'ass_players.csv');
    const usb_playersFile = path.resolve(__dirname, 'usbg_players.csv');
    const ob_playersFile = path.resolve(__dirname, 'ob_players.csv');
    const asg_playersFile = path.resolve(__dirname, 'asg_players.csv');
    const jso_playersFile = path.resolve(__dirname, 'jso_players.csv');
    const asm_playersFile = path.resolve(__dirname, 'asm_players.csv');
    const jsk_playersFile = path.resolve(__dirname, 'jsk_players.csv');
    const esz_playersFile = path.resolve(__dirname, 'esz_players.csv');

    const teams : Team[] = await readCSVFile<Team>(teamsFile);
    const ca_players = await readCSVFile<Player>(ca_playersFile);
    const ess_players = await readCSVFile<Player>(ess_playersFile);
    const css_players = await readCSVFile<Player>(css_playersFile);
    const usm_players = await readCSVFile<Player>(usm_playersFile);
    const est_players = await readCSVFile<Player>(est_playersFile);
    const st_players = await readCSVFile<Player>(st_playersFile);
    const esm_players = await readCSVFile<Player>(esm_playersFile);
    const cab_players = await readCSVFile<Player>(cab_playersFile);
    const ass_players = await readCSVFile<Player>(ass_playersFile);
    const usb_players = await readCSVFile<Player>(usb_playersFile);
    const ob_players = await readCSVFile<Player>(ob_playersFile);
    const asg_players = await readCSVFile<Player>(asg_playersFile);
    const jso_players = await readCSVFile<Player>(jso_playersFile);
    const asm_players = await readCSVFile<Player>(asm_playersFile);
    const jsk_players = await readCSVFile<Player>(jsk_playersFile);
    const esz_players = await readCSVFile<Player>(esz_playersFile);

    const players : Record<string, Player[]> = {
      ca_players,
      ess_players,
      css_players,
      usm_players,
      est_players,
      st_players,
      esm_players,
      cab_players,
      ass_players,
      usb_players,
      ob_players,
      asg_players,
      jso_players,
      esz_players,
      asm_players,
      jsk_players,
    };

    console.log('Teams:', teams);
    console.log('Players:', players);

    return { teams, players };
  } catch (error) {
    console.error('Error reading CSV files:', error);
    return { teams: [], players: {} };
  }
}

import { Position, PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Teams data from the selected code
const teamsData = [
  { id: '1', name: 'Club Africain', abbr: 'CA', logo: '/logos/ca.png', jersey: '/jerseys/ca.png' },
  { id: '2', name: 'Etoile Sportive du Sahel', abbr: 'ESS', logo: '/logos/ess.png', jersey: '/jerseys/ess.png' },
  { id: '3', name: 'Club Sportif Sfaxien', abbr: 'CSS', logo: '/logos/css.png', jersey: '/jerseys/css.png' },
  { id: '4', name: 'Union Sportive Monastirienne', abbr: 'USM', logo: '/logos/usm.png', jersey: '/jerseys/usm.png' },
  { id: '5', name: 'Espérance Sportive de Tunis', abbr: 'EST', logo: '/logos/est.png', jersey: '/jerseys/est.png' },
  { id: '6', name: 'Stade Tunisien', abbr: 'ST', logo: '/logos/st.png', jersey: '/jerseys/st.png' },
  { id: '7', name: 'ES Métlaoui', abbr: 'ESM', logo: '/logos/esm.png', jersey: '/jerseys/esm.png' },
  { id: '8', name: 'Club Athlétique Bizertin', abbr: 'CAB', logo: '/logos/cab.png', jersey: '/jerseys/cab.png' },
  { id: '9', name: 'AS Soliman', abbr: 'ASS', logo: '/logos/ass.png', jersey: '/jerseys/ass.png' },
  { id: '10', name: 'US Ben Guerdane', abbr: 'USBG', logo: '/logos/usb.png', jersey: '/jerseys/usb.png' },
  { id: '11', name: 'Olympique Béja', abbr: 'OB', logo: '/logos/ob.png', jersey: '/jerseys/ob.png' },
  { id: '12', name: 'AS Gabés', abbr: 'ASG', logo: '/logos/asg.png', jersey: '/jerseys/asg.png' },
  { id: '13', name: 'JS Omrane', abbr: 'JSO', logo: '/logos/jso.png', jersey: '/jerseys/jso.png' },
  { id: '14', name: 'AS Marsa', abbr: 'ASM', logo: '/logos/asm.png', jersey: '/jerseys/asm.png' },
  { id: '15', name: 'JS Kairouanaise', abbr: 'JSK', logo: '/logos/jsk.png', jersey: '/jerseys/jsk.png' },
  { id: '16', name: 'ES Zarzis', abbr: 'ESZ', logo: '/logos/esz.png', jersey: '/jerseys/esz.png' },
];

// Players data from the selected code (first batch - EST players)
const playersDataBatch1 = [
  { id: '1', name: 'Rodrigues', teamId: '5', currentPrice: 8, position: 'ATK' },
  { id: '2', name: 'Jabri', teamId: '5', currentPrice: 2, position: 'ATK' },
  { id: '3', name: 'Maacha', teamId: '5', currentPrice: 2, position: 'ATK' },
  { id: '4', name: 'Jbéli', teamId: '5', currentPrice: 8, position: 'ATK' },
  { id: '5', name: 'Sahli', teamId: '5', currentPrice: 8, position: 'ATK' },
  { id: '6', name: 'Kada', teamId: '5', currentPrice: 5, position: 'ATK' },
  { id: '7', name: 'Diakite', teamId: '5', currentPrice: 1, position: 'ATK' },
  { id: '8', name: 'Hamrouni', teamId: '5', currentPrice: 9, position: 'ATK' },
  { id: '9', name: 'Diarra', teamId: '5', currentPrice: 5, position: 'ATK' },
  { id: '10', name: 'Belaïli', teamId: '5', currentPrice: 1, position: 'ATK' },
  { id: '11', name: 'Sasse', teamId: '5', currentPrice: 7, position: 'MID' },
  { id: '12', name: 'Mokwana', teamId: '5', currentPrice: 3, position: 'MID' },
  { id: '13', name: 'Aholou', teamId: '5', currentPrice: 1, position: 'MID' },
  { id: '14', name: 'Konate', teamId: '5', currentPrice: 5, position: 'MID' },
  { id: '15', name: 'Mouhli', teamId: '5', currentPrice: 3, position: 'MID' },
  { id: '16', name: 'Derbali', teamId: '5', currentPrice: 9, position: 'MID' },
  { id: '17', name: 'Ayeb', teamId: '5', currentPrice: 6, position: 'MID' },
  { id: '18', name: 'Oumarou', teamId: '5', currentPrice: 0, position: 'MID' },
  { id: '19', name: 'Guenichi', teamId: '5', currentPrice: 4, position: 'MID' },
  { id: '20', name: 'Dhaou', teamId: '5', currentPrice: 7, position: 'MID' },
  { id: '21', name: 'Tougai', teamId: '5', currentPrice: 0, position: 'DEF' },
  { id: '22', name: 'Meriah', teamId: '5', currentPrice: 4, position: 'DEF' },
  { id: '23', name: 'B.Mohamed', teamId: '5', currentPrice: 2, position: 'DEF' },
  { id: '24', name: 'Bouzaiene', teamId: '5', currentPrice: 6, position: 'DEF' },
  { id: '25', name: 'Bouchniba', teamId: '5', currentPrice: 8, position: 'DEF' },
  { id: '26', name: 'Jelassi', teamId: '5', currentPrice: 6, position: 'DEF' },
  { id: '27', name: 'B.Ali', teamId: '5', currentPrice: 9, position: 'DEF' },
  { id: '28', name: 'Fadaa', teamId: '5', currentPrice: 7, position: 'DEF' },
  { id: '29', name: 'Smiri', teamId: '5', currentPrice: 5, position: 'DEF' },
  { id: '30', name: 'Kodhaii', teamId: '5', currentPrice: 2, position: 'DEF' },
  { id: '31', name: 'Memmiche', teamId: '5', currentPrice: 8, position: 'DEF' },
  { id: '32', name: 'B.Said', teamId: '5', currentPrice: 3, position: 'GK' },
  { id: '33', name: 'Debchi', teamId: '5', currentPrice: 0, position: 'GK' },
];

// Players data batch 2 - Club Africain players
const playersDataBatch2 = [
  { id: '34', name: 'Kinzumbi', teamId: '1', currentPrice: 0, position: 'ATK' },
  { id: '35', name: 'Labidi', teamId: '1', currentPrice: 5, position: 'ATK' },
  { id: '36', name: 'A.Malek', teamId: '1', currentPrice: 0, position: 'ATK' },
  { id: '37', name: 'Mesmary', teamId: '1', currentPrice: 5, position: 'ATK' },
  { id: '38', name: 'Kooh', teamId: '1', currentPrice: 5, position: 'ATK' },
  { id: '39', name: 'Garreb', teamId: '1', currentPrice: 2, position: 'ATK' },
  { id: '40', name: 'Srarfi', teamId: '1', currentPrice: 1, position: 'MID' },
  { id: '41', name: 'Khadraoui', teamId: '1', currentPrice: 9, position: 'MID' },
  { id: '42', name: 'Zemzemi', teamId: '1', currentPrice: 8, position: 'MID' },
  { id: '43', name: 'Kelaleche', teamId: '1', currentPrice: 3, position: 'MID' },
  { id: '44', name: 'Semakula', teamId: '1', currentPrice: 0, position: 'MID' },
  { id: '45', name: 'Khalil', teamId: '1', currentPrice: 6, position: 'MID' },
  { id: '46', name: 'Sghaier', teamId: '1', currentPrice: 9, position: 'MID' },
  { id: '47', name: 'Mohammed', teamId: '1', currentPrice: 4, position: 'MID' },
  { id: '48', name: 'Mahmoud', teamId: '1', currentPrice: 7, position: 'MID' },
  { id: '49', name: 'Ajimi', teamId: '1', currentPrice: 0, position: 'MID' },
  { id: '50', name: 'Yusuf', teamId: '1', currentPrice: 8, position: 'DEF' },
  { id: '51', name: 'Zaalouni', teamId: '1', currentPrice: 6, position: 'DEF' },
  { id: '52', name: 'Cherifi', teamId: '1', currentPrice: 9, position: 'DEF' },
  { id: '53', name: 'B.Abda', teamId: '1', currentPrice: 4, position: 'DEF' },
  { id: '54', name: 'Shili', teamId: '1', currentPrice: 7, position: 'DEF' },
  { id: '55', name: 'Tene', teamId: '1', currentPrice: 2, position: 'DEF' },
  { id: '56', name: 'Bouabid', teamId: '1', currentPrice: 0, position: 'DEF' },
  { id: '57', name: 'Bedoui', teamId: '1', currentPrice: 5, position: 'DEF' },
  { id: '58', name: 'Sghaier', teamId: '1', currentPrice: 8, position: 'DEF' },
  { id: '59', name: 'Ghrissi', teamId: '1', currentPrice: 3, position: 'DEF' },
  { id: '60', name: 'Hassen', teamId: '1', currentPrice: 1, position: 'DEF' },
  { id: '61', name: 'Arfaoui', teamId: '1', currentPrice: 9, position: 'GK' },
  { id: '62', name: 'Yeferni', teamId: '1', currentPrice: 6, position: 'GK' },
  { id: '63', name: 'Maghzaoui', teamId: '1', currentPrice: 2, position: 'GK' },
];

// Players data batch 3 - USM players
const playersDataBatch3 = [
  { id: '64', name: 'Mastouri', teamId: '4', currentPrice: 0, position: 'ATK' },
  { id: '65', name: 'Abdelli', teamId: '4', currentPrice: 5, position: 'ATK' },
  { id: '66', name: 'Elhmidi', teamId: '4', currentPrice: 0, position: 'ATK' },
  { id: '67', name: 'Bouatay', teamId: '4', currentPrice: 5, position: 'ATK' },
  { id: '68', name: 'Khalifa', teamId: '4', currentPrice: 2, position: 'ATK' },
  { id: '69', name: 'Michael', teamId: '4', currentPrice: 9, position: 'MID' },
  { id: '70', name: 'Dridi', teamId: '4', currentPrice: 8, position: 'MID' },
  { id: '71', name: 'Orkuma', teamId: '4', currentPrice: 5, position: 'MID' },
  { id: '72', name: 'B.H.Ali', teamId: '4', currentPrice: 3, position: 'MID' },
  { id: '73', name: 'Ganouni', teamId: '4', currentPrice: 1, position: 'MID' },
  { id: '74', name: 'Trayi', teamId: '4', currentPrice: 9, position: 'MID' },
  { id: '75', name: 'Harzi', teamId: '4', currentPrice: 6, position: 'MID' },
  { id: '76', name: 'Diané', teamId: '4', currentPrice: 4, position: 'MID' },
  { id: '77', name: 'Samb', teamId: '4', currentPrice: 7, position: 'MID' },
  { id: '78', name: 'Souissi', teamId: '4', currentPrice: 0, position: 'MID' },
  { id: '79', name: 'Slama', teamId: '4', currentPrice: 8, position: 'MID' },
  { id: '80', name: 'Ghorbel', teamId: '4', currentPrice: 6, position: 'DEF' },
  { id: '81', name: 'Jaziri', teamId: '4', currentPrice: 9, position: 'DEF' },
  { id: '82', name: 'Soltani', teamId: '4', currentPrice: 4, position: 'DEF' },
  { id: '83', name: 'Zeguei', teamId: '4', currentPrice: 7, position: 'DEF' },
  { id: '84', name: 'Salhi', teamId: '4', currentPrice: 2, position: 'DEF' },
  { id: '85', name: 'Miladi', teamId: '4', currentPrice: 0, position: 'DEF' },
  { id: '86', name: 'Azzouz', teamId: '4', currentPrice: 5, position: 'DEF' },
  { id: '87', name: 'Hallaoui', teamId: '4', currentPrice: 8, position: 'GK' },
  { id: '88', name: 'Besbes', teamId: '4', currentPrice: 3, position: 'GK' },
];

// Players data batch 4 - ESS players
const playersDataBatch4 = [
  { id: '89', name: 'Chaouat', teamId: '2', currentPrice: 5, position: 'ATK' },
  { id: '90', name: 'Aouani', teamId: '2', currentPrice: 2, position: 'ATK' },
  { id: '91', name: 'Kanté', teamId: '2', currentPrice: 9, position: 'ATK' },
  { id: '92', name: 'Chamakhi', teamId: '2', currentPrice: 1, position: 'ATK' },
  { id: '93', name: 'Smichi', teamId: '2', currentPrice: 5, position: 'ATK' },
  { id: '94', name: 'Souissi', teamId: '2', currentPrice: 0, position: 'ATK' },
  { id: '95', name: 'Anane', teamId: '2', currentPrice: 7, position: 'ATK' },
  { id: '96', name: 'Hassine', teamId: '2', currentPrice: 3, position: 'ATK' },
  { id: '97', name: 'Bousbaai', teamId: '2', currentPrice: 8, position: 'ATK' },
  { id: '98', name: 'Abid', teamId: '2', currentPrice: 5, position: 'MID' },
  { id: '99', name: 'B.Amor', teamId: '2', currentPrice: 0, position: 'MID' },
  { id: '100', name: 'B.Choug', teamId: '2', currentPrice: 8, position: 'MID' },
  { id: '101', name: 'Baayou', teamId: '2', currentPrice: 3, position: 'MID' },
  { id: '102', name: 'Karoui', teamId: '2', currentPrice: 6, position: 'MID' },
  { id: '103', name: 'Gbo', teamId: '2', currentPrice: 1, position: 'MID' },
  { id: '104', name: 'Chouchen', teamId: '2', currentPrice: 9, position: 'MID' },
  { id: '105', name: 'Ouertani', teamId: '2', currentPrice: 4, position: 'MID' },
  { id: '106', name: 'Jebali', teamId: '2', currentPrice: 7, position: 'MID' },
  { id: '107', name: 'Chihi', teamId: '2', currentPrice: 2, position: 'MID' },
  { id: '108', name: 'A.Jebali', teamId: '2', currentPrice: 9, position: 'MID' },
  { id: '109', name: 'Hnid', teamId: '2', currentPrice: 0, position: 'DEF' },
  { id: '110', name: 'C.Amar', teamId: '2', currentPrice: 8, position: 'DEF' },
  { id: '111', name: 'B.Ali', teamId: '2', currentPrice: 3, position: 'DEF' },
  { id: '112', name: 'Dagdoug', teamId: '2', currentPrice: 7, position: 'DEF' },
  { id: '113', name: 'Ghedamsi', teamId: '2', currentPrice: 6, position: 'DEF' },
  { id: '114', name: 'Boughattas', teamId: '2', currentPrice: 1, position: 'DEF' },
  { id: '115', name: 'Naouali', teamId: '2', currentPrice: 9, position: 'DEF' },
  { id: '116', name: 'B.Abdallah', teamId: '2', currentPrice: 4, position: 'DEF' },
  { id: '117', name: 'Khardani', teamId: '2', currentPrice: 8, position: 'GK' },
];

async function restoreDatabase() {
  try {
    console.log('🚀 Starting database restoration...');

    // Clear existing data (in reverse order of dependencies)
    console.log('🗑️  Clearing existing data...');
    await prisma.playerGWStats.deleteMany();
    await prisma.userGWSheet.deleteMany();
    await prisma.player.deleteMany();
    await prisma.fixture.deleteMany();
    await prisma.gameWeek.deleteMany();
    await prisma.team.deleteMany();

    console.log('✅ Existing data cleared');

    // Insert teams
    console.log('🏟️  Inserting teams...');
    for (const team of teamsData) {
      await prisma.team.create({
        data: {
          id: team.id,
          name: team.name,
          abbr: team.abbr,
          logo: team.logo,
          jersey: team.jersey,
        },
      });
    }
    console.log(`✅ Inserted ${teamsData.length} teams`);

    // Insert players (batch 1)
    console.log('⚽ Inserting players (batch 1)...');
    for (const player of playersDataBatch1) {
      await prisma.player.create({
        data: {
          id: player.id,
          name: player.name,
          teamId: player.teamId,
          currentPrice: player.currentPrice,
          position: player.position as Position,
        },
      });
    }
    console.log(`✅ Inserted ${playersDataBatch1.length} players (batch 1)`);

    // Insert players (batch 2)
    console.log('⚽ Inserting players (batch 2)...');
    for (const player of playersDataBatch2) {
      await prisma.player.create({
        data: {
          id: player.id,
          name: player.name,
          teamId: player.teamId,
          currentPrice: player.currentPrice,
          position: player.position as Position,
        },
      });
    }
    console.log(`✅ Inserted ${playersDataBatch2.length} players (batch 2)`);

    // Insert players (batch 3)
    console.log('⚽ Inserting players (batch 3)...');
    for (const player of playersDataBatch3) {
      await prisma.player.create({
        data: {
          id: player.id,
          name: player.name,
          teamId: player.teamId,
          currentPrice: player.currentPrice,
          position: player.position as Position,
        },
      });
    }
    console.log(`✅ Inserted ${playersDataBatch3.length} players (batch 3)`);

    // Insert players (batch 4)
    console.log('⚽ Inserting players (batch 4)...');
    for (const player of playersDataBatch4) {
      await prisma.player.create({
        data: {
          id: player.id,
          name: player.name,
          teamId: player.teamId,
          currentPrice: player.currentPrice,
          position: player.position as Position,
        },
      });
    }
    console.log(`✅ Inserted ${playersDataBatch4.length} players (batch 4)`);

    // Create GameWeeks for 2024-25 season
    console.log('📅 Creating GameWeeks...');
    const gameweeks = [];
    for (let i = 1; i <= 30; i++) {
      const gameweek = await prisma.gameWeek.create({
        data: {
          season: '2024-25',
          GW: i,
        },
      });
      gameweeks.push(gameweek);
    }
    console.log(`✅ Created ${gameweeks.length} GameWeeks`);

    // Default active gameweek to 1
    console.log('📅 Setting active gameweek...');
    await prisma.activeGameWeek.create({
      data: {
        id: '1',
        gameweekId: gameweeks[0].id,
      },
    });
    console.log(`✅ Set active gameweek to ${gameweeks[0].id}`);

    console.log('🎉 Database restoration completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - Teams: ${teamsData.length}`);
    console.log(`   - Players: ${playersDataBatch1.length + playersDataBatch2.length + playersDataBatch3.length + playersDataBatch4.length}`);
    console.log(`   - GameWeeks: ${gameweeks.length}`);

  } catch (error) {
    console.error('❌ Error during database restoration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the restoration
restoreDatabase()
  .then(() => {
    console.log('✅ Database restoration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Database restoration script failed:', error);
    process.exit(1);
  });

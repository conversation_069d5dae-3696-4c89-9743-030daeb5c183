import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedGameweeks() {
  try {
    console.log('Seeding GameWeeks...');

    const currentSeason = '2024-25';
    
    // Create GameWeeks 1-38 for the current season
    const gameweeks = [];
    for (let i = 1; i <= 38; i++) {
      gameweeks.push({
        season: currentSeason,
        GW: i
      });
    }

    // Use createMany to create all gameweeks at once
    const result = await prisma.gameWeek.createMany({
      data: gameweeks,
      skipDuplicates: true // Skip if already exists
    });

    console.log(`Created ${result.count} GameWeek records for season ${currentSeason}`);

    // Fetch and display the created gameweeks
    const createdGameweeks = await prisma.gameWeek.findMany({
      where: { season: currentSeason },
      orderBy: { GW: 'asc' }
    });

    console.log('GameWeek records:');
    createdGameweeks.forEach(gw => {
      console.log(`  GW ${gw.GW} (${gw.season}): ${gw.id}`);
    });

    return createdGameweeks;
  } catch (error) {
    console.error('Error seeding GameWeeks:', error);
    throw error;
  }
}

// Run the seed
seedGameweeks()
  .then((gameweeks) => {
    console.log(`Successfully seeded ${gameweeks.length} GameWeek records`);
  })
  .catch((error) => {
    console.error('Seeding failed:', error);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

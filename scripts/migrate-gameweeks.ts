import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateGameweeks() {
  try {
    console.log('Starting GameWeek migration...');

    // First, let's see what GW values exist in the current data
    const existingPlayerGWStats = await prisma.$queryRaw<Array<{ GW: number }>>`
      SELECT DISTINCT "GW" FROM "PlayerGWStats" ORDER BY "GW"
    `;

    const existingUserGWSheets = await prisma.$queryRaw<Array<{ GW: string }>>`
      SELECT DISTINCT "GW" FROM "UserGWSheet" ORDER BY "GW"
    `;

    console.log('Existing PlayerGWStats GWs:', existingPlayerGWStats);
    console.log('Existing UserGWSheet GWs:', existingUserGWSheets);

    // Create GameWeek records for the current season (2024-25)
    const currentSeason = '2024-25';
    const gameweeksToCreate = new Set<number>();

    // Add GWs from PlayerGWStats
    existingPlayerGWStats.forEach(stat => {
      if (typeof stat.GW === 'number') {
        gameweeksToCreate.add(stat.GW);
      }
    });

    // Add GWs from UserGWSheet (convert string to number if needed)
    existingUserGWSheets.forEach(sheet => {
      const gwNum = parseInt(sheet.GW);
      if (!isNaN(gwNum)) {
        gameweeksToCreate.add(gwNum);
      }
    });

    // If no existing data, create GWs 1-38 for the season
    if (gameweeksToCreate.size === 0) {
      for (let i = 1; i <= 30; i++) {
        gameweeksToCreate.add(i);
      }
    }

    console.log('Creating GameWeek records for GWs:', Array.from(gameweeksToCreate).sort());

    // Create GameWeek records
    const gameweekMap = new Map<number, string>();
    
    for (const gwNum of gameweeksToCreate) {
      const gameweek = await prisma.gameWeek.create({
        data: {
          season: currentSeason,
          GW: gwNum
        }
      });
      gameweekMap.set(gwNum, gameweek.id);
      console.log(`Created GameWeek ${gwNum} with ID: ${gameweek.id}`);
    }

    console.log('GameWeek migration completed successfully!');
    console.log('GameWeek ID mapping:', Object.fromEntries(gameweekMap));

    return gameweekMap;
  } catch (error) {
    console.error('Error during GameWeek migration:', error);
    throw error;
  }
}

// Run the migration
migrateGameweeks()
  .then(() => {
    console.log('Migration completed successfully');
    console.log('Next steps:');
    console.log('1. Update existing PlayerGWStats records to use GameWeek IDs');
    console.log('2. Update existing UserGWSheet records to use GameWeek IDs');
    console.log('3. Run prisma db push to apply schema changes');
  })
  .catch((error) => {
    console.error('Migration failed:', error);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

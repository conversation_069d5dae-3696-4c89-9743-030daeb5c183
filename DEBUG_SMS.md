# SMS API Debugging Guide

## Quick Debugging Steps

### 1. Test Twilio Configuration
Visit: `http://localhost:3000/api/sms/test`

This endpoint will check:
- ✅ Environment variables are set
- ✅ Twilio client can connect
- ✅ Verify service is accessible

### 2. Check Environment Variables
Ensure your `.env.local` file contains:

```env
TWILIO_ACCOUNT_SID="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TWILIO_AUTH_TOKEN="your_auth_token_here"
TWILIO_VERIFY_SERVICE_SID="VAxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
```

### 3. Common Issues & Solutions

#### Issue: "SMS service not configured"
**Solution:** Missing environment variables
- Check `.env.local` file exists
- Restart your dev server after adding env vars
- Verify variable names are exact (case-sensitive)

#### Issue: "Authentication failed"
**Solution:** Wrong credentials
- Double-check Account S<PERSON> and Auth Token from <PERSON>wi<PERSON> Console
- Make sure you're using the correct account (not subaccount)

#### Issue: "Service not found"
**Solution:** Wrong Verify Service SID
- Go to Twilio Console > Verify > Services
- Copy the correct Service SID (starts with "VA")

#### Issue: "Cannot send to unverified number" (Trial accounts)
**Solution:** Verify phone number in Twilio Console
- Go to Twilio Console > Phone Numbers > Verified Caller IDs
- Add and verify the phone number you're testing with

#### Issue: "Invalid phone number"
**Solution:** Phone number format
- App automatically adds +216 for Tunisia
- Enter number without country code: "12 345 678"
- App converts to: "+216********"

### 4. Testing with Console Logs

The current implementation logs:
- ✅ Phone number received
- ✅ Formatted phone number
- ✅ Twilio response
- ✅ Any errors

Check browser console and server logs for details.

### 5. Manual Testing

Test the API directly with curl:

```bash
curl -X POST http://localhost:3000/api/sms/send \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber":"********"}'
```

### 6. Twilio Console Logs

Check Twilio Console > Monitor > Logs for:
- SMS delivery status
- Error messages
- Rate limiting issues

## Next Steps

1. Run the test endpoint first
2. Check console logs in browser and server
3. Verify phone number format
4. Test with a verified number (for trial accounts)
5. Check Twilio Console logs

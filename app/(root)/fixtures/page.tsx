// app/fixtures/page.tsx

import { round1Fixtures, teams, players } from "@/lib/data/fixtures";
import { <PERSON>, CardH<PERSON>er, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import Image from "next/image";
import { format } from "date-fns";

function getTeamById(id: number) {
    return teams.find(t => t.id === id);
}

function getPlayerNameById(id: number) {
    return players.find(p => p.id === id)?.name || `#${id}`;
}

interface FixtureStatEntry {
    element: number;
    value: number;
}

interface FixtureStat {
    identifier: string;
    h: FixtureStatEntry[];
    a: FixtureStatEntry[];
}

interface Fixture {
    id: number;
    team_h: number;
    team_a: number;
    team_h_score: number;
    team_a_score: number;
    kickoff_time: string;
    stats: FixtureStat[];
}

function FixtureCard({ fixture }: { fixture: Fixture }) {
    const home = getTeamById(fixture.team_h);
    const away = getTeamById(fixture.team_a);

    return (
        <Card className="mb-4">
            <CardHeader>
                <CardTitle className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-2">
                        <Image src={home?.logo || ""} alt={home?.abbr || ""} width={32} height={32} />
                        <span>{home?.abbr}</span>
                        <span className="font-bold">{fixture.team_h_score}</span>
                        <span>:</span>
                        <span className="font-bold">{fixture.team_a_score}</span>
                        <span>{away?.abbr}</span>
                        <Image src={away?.logo || ""} alt={away?.abbr || ""} width={32} height={32} />
                    </div>
                    <span className="text-sm text-muted-foreground">
                        {format(new Date(fixture.kickoff_time), "PPpp")}
                    </span>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <Accordion type="single" collapsible>
                    <AccordionItem value="details">
                        <AccordionTrigger>Match Stats</AccordionTrigger>
                        <AccordionContent>
                            {fixture.stats.map((stat: FixtureStat) => (
                                <div key={stat.identifier} className="mb-2">
                                    <div className="font-semibold capitalize">{stat.identifier.replace(/_/g, " ")}</div>
                                    <ul className="ml-4 text-sm text-muted-foreground">
                                        {stat.h.map((entry: FixtureStatEntry) => (
                                            <li key={`h-${entry.element}`} className="flex items-center gap-2">
                                                <Image src={home?.logo || ""} alt={home?.abbr || ""} width={16} height={16} /> {getPlayerNameById(entry.element)}: {entry.value}
                                            </li>
                                        ))}
                                        {stat.a.map((entry: FixtureStatEntry) => (
                                            <li key={`a-${entry.element}`} className="flex items-center gap-2">
                                                <Image src={away?.logo || ""} alt={away?.abbr || ""} width={16} height={16} /> {getPlayerNameById(entry.element)}: {entry.value}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            ))}
                        </AccordionContent>
                    </AccordionItem>
                </Accordion>
            </CardContent>
        </Card>
    );
}

export default function FixturesPage() {
    return (
        <div className="max-w-3xl mx-auto px-4 py-8">
            <h1 className="text-2xl font-bold mb-6">Fixtures – Round 1</h1>
            {round1Fixtures.map((fixture) => (
                <FixtureCard key={fixture.id} fixture={fixture} />
            ))}
        </div>
    );
}

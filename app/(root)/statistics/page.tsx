"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Search, TrendingUp, Users, Target, Award } from "lucide-react";
import { PlayerInfo } from "@/types/types";
import { useFantasy } from "@/hooks/useFantasy";

export default function StatisticsPage() {
  const { players, isLoading } = useFantasy();
  const [filteredPlayers, setFilteredPlayers] = useState<PlayerInfo[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [positionFilter, setPositionFilter] = useState<string>("all");
  const [teamFilter, setTeamFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("price");

  // Filter and sort players
  useEffect(() => {
    const filtered = players.filter((player) => {
      const matchesSearch = player.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesPosition =
        positionFilter === "all" || player.position === positionFilter;
      const matchesTeam = teamFilter === "all" || player.teamId === teamFilter;

      return matchesSearch && matchesPosition && matchesTeam;
    });

    // Sort players
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "price":
          return Number(b.currentPrice) - Number(a.currentPrice);
        case "name":
          return a.name.localeCompare(b.name);
        case "position":
          return a.position.localeCompare(b.position);
        case "team":
          return a.teamId.localeCompare(b.teamId);
        default:
          return 0;
      }
    });

    setFilteredPlayers(filtered);
  }, [players, searchTerm, positionFilter, teamFilter, sortBy]);

  // Get unique teams for filter
  const uniqueTeams = Array.from(new Set(players.map((p) => p.teamId))).sort();

  // Calculate statistics
  const stats = {
    totalPlayers: players.length,
    avgPrice:
      players.length > 0
        ? players.reduce(
            (sum, p) => sum + Number(p.currentPrice),
            0 / players.length
          )
        : 0,
    mostExpensive:
      players.length > 0
        ? Math.max(...players.map((p) => Number(p.currentPrice)))
        : 0,
    positionCounts: {
      GK: players.filter((p) => p.position === "GK").length,
      DEF: players.filter((p) => p.position === "DEF").length,
      MID: players.filter((p) => p.position === "MID").length,
      ATK: players.filter((p) => p.position === "ATK").length,
    },
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Player Statistics</h1>
          <p className="text-muted-foreground">Loading player data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Player Statistics</h1>
        <p className="text-muted-foreground">
          Comprehensive statistics for all players in the Tunisian Fantasy
          League
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-5 w-5 text-primary" />
            </div>
            <div className="text-2xl font-bold">{stats.totalPlayers}</div>
            <p className="text-xs text-muted-foreground">Total Players</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-5 w-5 text-primary" />
            </div>
            <div className="text-2xl font-bold">{stats.avgPrice}M</div>
            <p className="text-xs text-muted-foreground">Average Price</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Award className="h-5 w-5 text-primary" />
            </div>
            <div className="text-2xl font-bold">{stats.mostExpensive}M</div>
            <p className="text-xs text-muted-foreground">Most Expensive</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Target className="h-5 w-5 text-primary" />
            </div>
            <div className="text-sm font-bold">
              GK:{stats.positionCounts.GK} DEF:{stats.positionCounts.DEF}
            </div>
            <div className="text-sm font-bold">
              MID:{stats.positionCounts.MID} ATK:{stats.positionCounts.ATK}
            </div>
            <p className="text-xs text-muted-foreground">By Position</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>All Players</CardTitle>
          <CardDescription>
            Browse and filter all players in the league
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search players..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={positionFilter} onValueChange={setPositionFilter}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue placeholder="Position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Positions</SelectItem>
                <SelectItem value="GK">Goalkeeper</SelectItem>
                <SelectItem value="DEF">Defender</SelectItem>
                <SelectItem value="MID">Midfielder</SelectItem>
                <SelectItem value="ATK">Attacker</SelectItem>
              </SelectContent>
            </Select>

            <Select value={teamFilter} onValueChange={setTeamFilter}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue placeholder="Team" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Teams</SelectItem>
                {uniqueTeams.map((team) => (
                  <SelectItem key={team} value={team}>
                    {team}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="price">Price</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="position">Position</SelectItem>
                <SelectItem value="team">Team</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Players Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Player</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Team</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPlayers.map((player) => (
                  <TableRow key={player.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={player.team.jersey} />
                        </Avatar>
                        <div>
                          <div className="font-medium">{player.name}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{player.position}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={`/teams/${player.teamId}.png`} />
                        </Avatar>
                        {player.teamId}
                      </div>
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {Number(player.currentPrice)}M
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredPlayers.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No players found matching your criteria
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

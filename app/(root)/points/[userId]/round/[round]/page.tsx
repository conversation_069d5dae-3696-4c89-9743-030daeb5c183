"use client";

import { ViewProvider } from "@/contexts/ViewContext";
import Header from "@/components/Header";
import StatsPanel from "@/components/StatsPanel";
import InfoPanel from "@/components/InfoPanel";
import TeamSection from "@/components/TeamSection";
import { useFantasy } from "@/hooks/useFantasy";
import { use } from "react";

function PointsPageContent({
  params,
}: {
  params: Promise<{ userId: string; round: string }>;
}) {
  const { userId, round } = use(params);
  const { stats, isLoading, subs, starters } = useFantasy(userId, round);

  if (isLoading) {
    return <div className="p-6">Loading team data...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <Header
        round={round}
        isDreamTeamVisible={false}
        isNextFixtureVisible={round !== "30"}
        isPreviousFixtureVisible={round !== "1"}
      />

      <StatsPanel
        avgPoints={stats.avgPoints}
        highestPoints={stats.highestPoints}
        totalGWPoints={stats.totalGWPoints}
        roundRank={stats.roundRank}
        transfersRemaining={stats.transfersRemaining}
      />

      <div className="flex flex-col lg:flex-row gap-6">
        <div className="w-full lg:w-2/3">
          <TeamSection
            isPointsPage
            starters={starters}
            subs={subs}
            isLoading={isLoading}
          />
        </div>
        <div className="w-full lg:w-1/3">
          <InfoPanel
            squadValue={stats.squadValue}
            transfersLeft={stats.transfersRemaining}
          />
        </div>
      </div>
    </div>
  );
}

// Main page component that sets up the context
export default function pointsPage({
  params,
}: {
  params: Promise<{ userId: string; round: string }>;
}) {
  return (
    <ViewProvider defaultView="pitch">
      <PointsPageContent params={params} />
    </ViewProvider>
  );
}

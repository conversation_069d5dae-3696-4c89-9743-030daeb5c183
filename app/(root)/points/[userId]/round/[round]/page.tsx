"use client";

import Header from "@/components/Header";
import StatsPanel from "@/components/StatsPanel";
import InfoPanel from "@/components/InfoPanel";
import { useFantasy } from "@/hooks/useFantasy";
import { use, useState } from "react";
import TeamTable from "@/components/TeamTable";
import PitchView from "@/components/PitchView";
import PlayerModal from "@/components/PlayerModal";
import { PlayerInfo } from "@/types/types";

export default function PointsPage({
  params,
}: {
  params: Promise<{ userId: string; round: string }>;
}) {
  const { userId, round } = use(params);
  const {
    stats,
    isLoading,
    subs,
    starters,
    view,
    captainId,
    viceCaptainId,
    setView,
  } = useFantasy(userId, round);
  const [selectedPlayer, setSelectedPlayer] = useState<PlayerInfo | null>(null);

  if (isLoading) {
    return <div className="p-6">Loading team data...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <StatsPanel
        avgPoints={stats.avgPoints}
        highestPoints={stats.highestPoints}
        totalGWPoints={stats.totalGWPoints}
        roundRank={stats.roundRank}
        transfersRemaining={stats.transfersRemaining}
      />

      <div className="flex flex-col lg:flex-row gap-6">
        <div className="w-full lg:w-2/3">
          <Header
            view={view}
            setView={setView}
            userId={userId}
            round={round}
            isDreamTeamVisible={false}
            isNextFixtureVisible={round !== "30"}
            isPreviousFixtureVisible={round !== "1"}
          />
          {view === "list" ? (
            <>
              <TeamTable players={starters} onSelect={setSelectedPlayer} />
              <h3 className="text-xl font-semibold text-foreground mt-6">
                Substitutes
              </h3>
              <TeamTable players={subs} onSelect={setSelectedPlayer} />
            </>
          ) : (
            <PitchView
              starters={starters}
              subs={subs}
              onSelect={setSelectedPlayer}
              captainId={captainId}
              viceCaptainId={viceCaptainId}
              showPoints
              showRole
            />
          )}

          <PlayerModal
            player={selectedPlayer}
            onClose={() => setSelectedPlayer(null)}
            isEditable={false}
          />
        </div>
        <div className="w-full lg:w-1/3">
          <InfoPanel
            squadValue={stats.squadValue}
            transfersLeft={stats.transfersRemaining}
          />
        </div>
      </div>
    </div>
  );
}

import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Trophy, 
  Users, 
  Target, 
  Heart,
  Mail,
  MapPin,
  Calendar,
  Star,
  Award,
  Zap
} from "lucide-react";
import Link from "next/link";

export default function AboutPage() {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      description: "Passionate football fan and tech entrepreneur",
      icon: Trophy
    },
    {
      name: "<PERSON><PERSON>",
      role: "Head of Product",
      description: "Expert in user experience and game design",
      icon: Target
    },
    {
      name: "<PERSON>",
      role: "Lead Developer",
      description: "Full-stack developer with sports tech expertise",
      icon: Zap
    },
    {
      name: "<PERSON><PERSON>",
      role: "Community Manager",
      description: "Building and nurturing our fantasy football community",
      icon: Users
    }
  ];

  const milestones = [
    {
      year: "2023",
      title: "TFL Founded",
      description: "Started with a vision to bring fantasy football to Tunisia"
    },
    {
      year: "2024",
      title: "Alpha Launch",
      description: "Launched alpha version with core fantasy football features"
    },
    {
      year: "2024",
      title: "Community Growth",
      description: "Reached 1,000+ active fantasy managers"
    },
    {
      year: "2025",
      title: "Full Launch",
      description: "Planning comprehensive launch with prizes and leagues"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="bg-gradient-to-r from-yellow-500 to-orange-600 p-4 rounded-full">
              <Trophy className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-3xl sm:text-4xl font-bold">About TFL</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            🚧 Preview Version - The premier fantasy football platform for Tunisian football fans
          </p>
          <Badge variant="secondary" className="text-lg px-4 py-2">
            App Under Construction
          </Badge>
        </div>

        {/* Mission Statement */}
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
          <CardContent className="p-8 text-center">
            <Heart className="h-12 w-12 mx-auto mb-4 text-primary" />
            <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              To bring the excitement of fantasy football to Tunisia, connecting fans through 
              their shared passion for the game while celebrating local talent and teams.
            </p>
          </CardContent>
        </Card>

        {/* Our Story */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Our Story</CardTitle>
            <CardDescription>How TFL came to life</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              TFL was born from a simple idea: Tunisian football fans deserved their own fantasy 
              football platform that celebrated local teams, players, and the unique passion of 
              Tunisian football culture.
            </p>
            <p className="text-muted-foreground">
              Founded by a group of football enthusiasts and tech professionals, we recognized 
              that while international fantasy platforms existed, none truly captured the spirit 
              and excitement of Tunisian football. We set out to change that.
            </p>
            <p className="text-muted-foreground">
              Today, TFL is more than just a fantasy platform – it&#39;s a community where fans 
              come together to test their football knowledge, compete for prizes, and celebrate 
              the teams and players they love.
            </p>
          </CardContent>
        </Card>

        {/* What Makes Us Different */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-center">What Makes TFL Special</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="bg-blue-100 dark:bg-blue-900/20 p-3 rounded-full w-fit mx-auto mb-4">
                  <MapPin className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Local Focus</h3>
                <p className="text-muted-foreground text-sm">
                  Dedicated exclusively to Tunisian football, featuring all local teams and players 
                  with detailed statistics and insights.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <div className="bg-green-100 dark:bg-green-900/20 p-3 rounded-full w-fit mx-auto mb-4">
                  <Users className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Community Driven</h3>
                <p className="text-muted-foreground text-sm">
                  Built by fans, for fans. Our platform reflects the needs and desires of the 
                  Tunisian football community.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <div className="bg-purple-100 dark:bg-purple-900/20 p-3 rounded-full w-fit mx-auto mb-4">
                  <Award className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Real Rewards</h3>
                <p className="text-muted-foreground text-sm">
                  Compete for real prizes and recognition, with weekly and seasonal competitions 
                  that celebrate the best fantasy managers.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Team */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-center">Meet Our Team</h2>
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="bg-primary/10 p-3 rounded-full w-fit mx-auto mb-4">
                    <member.icon className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="font-semibold text-lg mb-1">{member.name}</h3>
                  <Badge variant="secondary" className="mb-3">{member.role}</Badge>
                  <p className="text-muted-foreground text-sm">{member.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Timeline */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-center">Our Journey</h2>
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {milestones.map((milestone, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Calendar className="h-4 w-4 text-primary" />
                    <Badge variant="outline">{milestone.year}</Badge>
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{milestone.title}</h3>
                  <p className="text-muted-foreground text-sm">{milestone.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Values */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl text-center">Our Values</CardTitle>
            <CardDescription className="text-center">
              The principles that guide everything we do
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Star className="h-5 w-5 text-yellow-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Excellence</h4>
                    <p className="text-sm text-muted-foreground">
                      We strive for excellence in every aspect of our platform and service.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Heart className="h-5 w-5 text-red-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Passion</h4>
                    <p className="text-sm text-muted-foreground">
                      Our love for football drives everything we create and deliver.
                    </p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Users className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Community</h4>
                    <p className="text-sm text-muted-foreground">
                      We believe in the power of community and bringing fans together.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Trophy className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Fair Play</h4>
                    <p className="text-sm text-muted-foreground">
                      We promote fair competition and sportsmanship in all our activities.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact CTA */}
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
          <CardContent className="p-8 text-center">
            <Mail className="h-12 w-12 mx-auto mb-4 text-primary" />
            <h2 className="text-2xl font-bold mb-4">Get in Touch</h2>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Have questions about TFL? Want to partner with us? We&#39;d love to hear from you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <Button>
                  Contact Us
                </Button>
              </Link>
              <Button variant="outline">
                Join Our Community
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

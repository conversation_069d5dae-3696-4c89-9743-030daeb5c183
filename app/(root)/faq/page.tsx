import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { 
  HelpCircle, 
  Search, 
  Users, 
  Trophy, 
  Target,
  MessageCircle,
} from "lucide-react";
import Link from "next/link";

export default function FAQPage() {
  const faqCategories = [
    {
      title: "Getting Started",
      icon: HelpCircle,
      color: "blue",
      faqs: [
        {
          question: "How do I create my first team?",
          answer: "To create your first team, sign up for an account and you'll be guided through the onboarding process. You'll need to select 15 players: 11 starters and 4 substitutes, staying within the 100M budget."
        },
        {
          question: "What is the budget for my team?",
          answer: "Every manager starts with a budget of 100 million. You must select 15 players (11 starters + 4 subs) without exceeding this budget."
        },
        {
          question: "How many players can I have from the same team?",
          answer: "You can select a maximum of 5 players from any single team in your squad of 15 players."
        }
      ]
    },
    {
      title: "Team Management",
      icon: Users,
      color: "green",
      faqs: [
        {
          question: "How do transfers work?",
          answer: "You can make transfers to improve your team. Each transfer costs 4 points as a penalty. You get a limited number of free transfers each gameweek."
        },
        {
          question: "What is a wildcard?",
          answer: "A wildcard allows you to make unlimited transfers without any point penalties. You typically get 2 wildcards per season - use them wisely!"
        },
        {
          question: "Can I change my captain after the deadline?",
          answer: "No, once the gameweek deadline passes, you cannot change your captain or make any other changes to your team until the next gameweek."
        }
      ]
    },
    {
      title: "Scoring System",
      icon: Target,
      color: "purple",
      faqs: [
        {
          question: "How are points calculated?",
          answer: "Players earn points based on their real-life performance: goals, assists, clean sheets, saves, and more. Your captain's points are doubled."
        },
        {
          question: "What happens if my player doesn't play?",
          answer: "If a player in your starting XI doesn't play, they'll be automatically substituted by the first eligible player on your bench."
        },
        {
          question: "How do bonus points work?",
          answer: "The best performing players in each match can earn 1-3 bonus points based on their overall contribution to the game."
        }
      ]
    },
    {
      title: "Leagues & Competition",
      icon: Trophy,
      color: "yellow",
      faqs: [
        {
          question: "How do I join a league?",
          answer: "You can join public leagues automatically or create/join private leagues using a league code shared by friends."
        },
        {
          question: "What are the different types of leagues?",
          answer: "There are public leagues (open to all), private leagues (invitation only), and head-to-head leagues where you play against one manager each week."
        },
        {
          question: "When are prizes awarded?",
          answer: "Prizes are awarded at the end of each gameweek for weekly competitions and at the end of the season for overall competitions."
        }
      ]
    }
  ];

  const popularQuestions = [
    "How do I make my first transfer?",
    "What is a wildcard and when should I use it?",
    "How are player prices determined?",
    "Can I change my team name?",
    "What happens if a player gets injured?",
    "How do I create a private league?"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-4 rounded-full">
              <HelpCircle className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-3xl sm:text-4xl font-bold">Frequently Asked Questions</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            🚧 Preview Version - FAQ content for the upcoming fantasy football app
          </p>
          <Badge variant="secondary" className="text-lg px-4 py-2">
            App Under Construction
          </Badge>
        </div>

        {/* Search Bar */}
        <Card className="max-w-2xl mx-auto">
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search FAQ..."
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Popular Questions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Most Popular Questions
            </CardTitle>
            <CardDescription>
              Quick access to the most frequently asked questions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid sm:grid-cols-2 gap-3">
              {popularQuestions.map((question, index) => (
                <div key={index} className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <Badge variant="outline" className="text-xs">
                    {index + 1}
                  </Badge>
                  <span className="text-sm">{question}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* FAQ Categories */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-center">Browse by Category</h2>
          
          {faqCategories.map((category) => (
            <Card key={category.title}>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`bg-${category.color}-100 dark:bg-${category.color}-900/20 p-2 rounded-lg`}>
                    <category.icon className={`h-6 w-6 text-${category.color}-600 dark:text-${category.color}-400`} />
                  </div>
                  <div>
                    <CardTitle className="text-xl">{category.title}</CardTitle>
                    <CardDescription>{category.faqs.length} questions</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  {category.faqs.map((faq, index) => (
                    <AccordionItem key={index} value={`${category.title}-${index}`}>
                      <AccordionTrigger className="text-left">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="text-muted-foreground">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Links */}
        <div className="grid sm:grid-cols-3 gap-4">
          <Link href="/help">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-6 text-center">
                <HelpCircle className="h-8 w-8 mx-auto mb-3 text-blue-500" />
                <h3 className="font-semibold mb-2">Help Center</h3>
                <p className="text-sm text-muted-foreground">Browse all help articles</p>
              </CardContent>
            </Card>
          </Link>
          
          <Link href="/contact">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-6 text-center">
                <MessageCircle className="h-8 w-8 mx-auto mb-3 text-green-500" />
                <h3 className="font-semibold mb-2">Contact Support</h3>
                <p className="text-sm text-muted-foreground">Get personalized help</p>
              </CardContent>
            </Card>
          </Link>
          
          <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 mx-auto mb-3 text-purple-500" />
              <h3 className="font-semibold mb-2">Community</h3>
              <p className="text-sm text-muted-foreground">Join the discussion</p>
            </CardContent>
          </Card>
        </div>

        {/* Still Need Help */}
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
          <CardContent className="p-8 text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 text-primary" />
            <h2 className="text-2xl font-bold mb-4">Still Have Questions?</h2>
            <p className="text-muted-foreground mb-6">
              Can&#39;t find the answer you&#39;re looking for? Our support team is ready to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <Button>
                  Contact Support
                </Button>
              </Link>
              <Link href="/help">
                <Button variant="outline">
                  Browse Help Center
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// app/(root)/transfers/TransferPageContent.tsx
"use client";

import { useState } from "react";
import Header from "@/components/Header";
import TransferFilters from "@/components/TransferFilters";
import { Player } from "@prisma/client";
import TeamTable from "@/components/TeamTable";
import { useFantasy } from "@/hooks/useFantasy";
import PitchView from "@/components/PitchView";
import PlayerModal from "@/components/PlayerModal";

export default function TransferPageContent({ userId }: { userId: string }) {
  const LATEST_ROUND = "1";
  const round = LATEST_ROUND;

  const {
    view,
    setView,
    isLoading,
    subs,
    starters,
    hasChanges,
    removedPlayers,
    handleRemove,
    handleAdd,
    saveTeam,
    filteredMarket,
    filterPosition,
    setFilterPosition,
    filterTeam,
    setFilterTeam,
    filterPrice,
    setFilterPrice,
    priceRangeMax,
    availableTeams,
    handleRestore,
  } = useFantasy(userId, round);

  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);

  // Handle player removal and update filter
  const onRemovePlayer = (player: Player | null = selectedPlayer) => {
    if (!player) return;
    const position = handleRemove(player);
    if (position) setFilterPosition(position);
    setSelectedPlayer(null);
  };

  // Handle player addition
  const onAddPlayer = (player: Player) => {
    if (!player || !selectedPlayer || !removedPlayers.length) return;
    const success = handleAdd(player);
    if (success) setSelectedPlayer(null);
  };
  // Handle player restoration
  const onRestorePlayer = (player: Player) => {
    if (!player) return;
    handleRestore(player);
    setSelectedPlayer(null);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="w-full lg:w-2/3">
          <Header
            view={view}
            setView={setView}
            round={round}
            isDreamTeamVisible={false}
            isPreviousFixtureVisible={round !== "1"}
            isTransfersPage
            hasChanges={hasChanges}
            isLoading={isLoading}
            onSave={() => saveTeam(userId, round)}
            title="Transfers"
          />
          {view === "list" ? (
            <TeamTable
              players={[...starters, ...subs]}
              onSelect={setSelectedPlayer}
            />
          ) : (
            <PitchView
              starters={[...starters, ...subs]}
              removedPlayers={removedPlayers}
              onSelect={setSelectedPlayer}
              onRemove={onRemovePlayer}
              showPrice
              showRemove
            />
          )}
          <PlayerModal
            player={selectedPlayer}
            removedPlayers={removedPlayers}
            starters={starters}
            subs={subs}
            onClose={() => setSelectedPlayer(null)}
            isEditable
            onRemove={onRemovePlayer}
            onAdd={onAddPlayer}
            onRestore={onRestorePlayer}
          />
        </div>
        <div className="w-full lg:w-1/3">
          <TransferFilters
            allPlayers={filteredMarket}
            selectedPosition={filterPosition}
            selectedTeam={filterTeam}
            selectedPrice={filterPrice}
            maxPossiblePrice={priceRangeMax}
            availableTeams={availableTeams}
            onPositionChange={setFilterPosition}
            onTeamChange={setFilterTeam}
            onPriceChange={setFilterPrice}
          />
          <TeamTable players={filteredMarket} onSelect={setSelectedPlayer} />
        </div>
      </div>
    </div>
  );
}

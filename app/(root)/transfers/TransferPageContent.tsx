// app/(root)/transfers/TransferPageContent.tsx
"use client";

import { useState } from "react";
import Header from "@/components/Header";
import TransferFilters from "@/components/TransferFilters";
import { Player } from "@prisma/client";
import TeamTable from "@/components/TeamTable";
import { useFantasy } from "@/hooks/useFantasy";
import PitchView from "@/components/PitchView";
import PlayerModal from "@/components/PlayerModal";
import TransferConfirmationDialog from "@/components/TransferConfirmationDialog";
import { validateTeamTransfers } from "@/lib/utils";

export default function TransferPageContent({ userId }: { userId: string }) {
  const LATEST_ROUND = "1";
  const round = LATEST_ROUND;

  const {
    view,
    setView,
    isLoading,
    subs,
    starters,
    hasChanges,
    removedPlayers,
    handleRemove,
    handleAdd,
    saveTeam,
    filteredMarket,
    filterPosition,
    setFilterPosition,
    filterTeam,
    setFilterTeam,
    filterPrice,
    setFilterPrice,
    priceRangeMax,
    availableTeams,
    handleRestore,
    originalStarters,
    originalSubs,
  } = useFantasy(userId, round);

  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // Validate team before enabling save
  const validation = validateTeamTransfers(starters, subs);
  console.log("Validation", validation);
  const isSaveEnabled = hasChanges && validation.isValid;

  // Handle player removal and update filter
  const onRemovePlayer = (player: Player | null = selectedPlayer) => {
    if (!player) return;
    const position = handleRemove(player);
    if (position) setFilterPosition(position);
    setSelectedPlayer(null);
  };

  // Handle player addition
  const onAddPlayer = (player: Player) => {
    if (!player || !selectedPlayer || !removedPlayers.length) return;
    const success = handleAdd(player);
    if (success) setSelectedPlayer(null);
  };
  // Handle player restoration
  const onRestorePlayer = (player: Player) => {
    if (!player) return;
    handleRestore(player);
    setSelectedPlayer(null);
  };

  // Handle save button click - show confirmation dialog
  const handleSaveClick = () => {
    if (isSaveEnabled) {
      setShowConfirmDialog(true);
    }
  };

  // Handle confirmed save
  const handleConfirmedSave = async () => {
    const success = await saveTeam(userId, round);
    if (success) {
      setShowConfirmDialog(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-4 sm:space-y-6">
        {/* Mobile-first responsive layout */}
        <div className="flex flex-col xl:flex-row gap-4 sm:gap-6">
          {/* Main content area */}
          <div className="w-full xl:w-2/3 space-y-4 sm:space-y-6">
            <Header
              view={view}
              setView={setView}
              userId={userId}
              round={round}
              isDreamTeamVisible={false}
              isPreviousFixtureVisible={round !== "1"}
              hasChanges={hasChanges}
              isLoading={isLoading}
              onSave={handleSaveClick}
              title="Transfers"
              isSaveDisabled={!isSaveEnabled}
            />

            {/* Team display */}
            <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-3 sm:p-4 lg:p-6">
              {view === "list" ? (
                <div>
                  <h3 className="text-lg sm:text-xl font-semibold text-foreground mb-3 sm:mb-4">
                    Your Squad
                  </h3>
                  <TeamTable
                    players={[...starters, ...subs]}
                    onSelect={setSelectedPlayer}
                  />
                </div>
              ) : (
                <PitchView
                  starters={starters}
                  subs={subs}
                  removedPlayers={removedPlayers}
                  onSelect={setSelectedPlayer}
                  onRemove={onRemovePlayer}
                  showPrice
                  showRemove
                  isBuildingTeam
                />
              )}
            </div>

            <PlayerModal
              player={selectedPlayer}
              removedPlayers={removedPlayers}
              starters={starters}
              subs={subs}
              onClose={() => setSelectedPlayer(null)}
              isEditable
              onRemove={onRemovePlayer}
              onAdd={onAddPlayer}
              onRestore={onRestorePlayer}
            />
          </div>

          {/* Sidebar - Filters and Market */}
          <div className="w-full xl:w-1/3 space-y-4 sm:space-y-6">
            <div className="sticky top-4 space-y-4">
              <TransferFilters
                allPlayers={filteredMarket}
                selectedPosition={filterPosition}
                selectedTeam={filterTeam}
                selectedPrice={filterPrice}
                maxPossiblePrice={priceRangeMax}
                availableTeams={availableTeams}
                onPositionChange={setFilterPosition}
                onTeamChange={setFilterTeam}
                onPriceChange={setFilterPrice}
              />

              <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-3 sm:p-4">
                <h3 className="text-lg font-semibold text-foreground mb-3 sm:mb-4">
                  Transfer Market
                </h3>
                <div className="max-h-[400px] sm:max-h-[500px] overflow-y-auto">
                  <TeamTable players={filteredMarket.slice(0, 20)} onSelect={setSelectedPlayer} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Transfer Confirmation Dialog */}
      <TransferConfirmationDialog
        isOpen={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={handleConfirmedSave}
        currentStarters={starters}
        currentSubs={subs}
        originalStarters={originalStarters}
        originalSubs={originalSubs}
        isLoading={isLoading}
      />
    </div>
  );
}

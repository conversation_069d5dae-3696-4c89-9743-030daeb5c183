import { getCurrentUser } from "@/lib/auth";
import { ViewProvider } from "@/contexts/ViewContext";
import TransferPageContent from "./TransferPageContent";

export default async function TeamPage() {
  const user = await getCurrentUser();

  if (!user) return <div className="p-6">Loading team data...</div>;

  return (
    <ViewProvider defaultView="pitch">
      <TransferPageContent userId={user.id} />
    </ViewProvider>
  );
}

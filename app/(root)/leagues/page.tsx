"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Plus,
  Crown,
  UserPlus,
  UserMinus,
  Search,
  Trophy,
  Settings,
  Copy,
  Check,
} from "lucide-react";
import { toast } from "sonner";

interface League {
  id: string;
  name: string;
  admins: User[];
  members: User[];
  _count: {
    members: number;
  };
}

interface User {
  id: string;
  name: string;
  email: string;
}

export default function LeaguesPage() {
  const { user } = useUser();
  const [leagues, setLeagues] = useState<League[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [newLeagueName, setNewLeagueName] = useState("");
  const [joinCode, setJoinCode] = useState("");
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [joinDialogOpen, setJoinDialogOpen] = useState(false);
  const [copiedLeagueId, setCopiedLeagueId] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchLeagues();
    }
  }, [user]);

  const fetchLeagues = async () => {
    try {
      const response = await fetch("/api/leagues");
      if (response.ok) {
        const data = await response.json();
        setLeagues(data);
      }
    } catch (error) {
      console.error("Error fetching leagues:", error);
      toast.error("Failed to load leagues");
    } finally {
      setLoading(false);
    }
  };

  const createLeague = async () => {
    if (!newLeagueName.trim()) {
      toast.error("Please enter a league name");
      return;
    }

    try {
      const response = await fetch("/api/leagues", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: newLeagueName }),
      });

      if (response.ok) {
        const newLeague = await response.json();
        setLeagues([...leagues, newLeague]);
        setNewLeagueName("");
        setCreateDialogOpen(false);
        toast.success("League created successfully!");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to create league");
      }
    } catch (error) {
      console.error("Error creating league:", error);
      toast.error("Failed to create league");
    }
  };

  const joinLeague = async () => {
    if (!joinCode.trim()) {
      toast.error("Please enter a league code");
      return;
    }

    try {
      const response = await fetch(`/api/leagues/${joinCode}/join`, {
        method: "POST",
      });

      if (response.ok) {
        const updatedLeague = await response.json();
        setLeagues(leagues.map(l => l.id === updatedLeague.id ? updatedLeague : l));
        setJoinCode("");
        setJoinDialogOpen(false);
        toast.success("Joined league successfully!");
        fetchLeagues(); // Refresh to get updated data
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to join league");
      }
    } catch (error) {
      console.error("Error joining league:", error);
      toast.error("Failed to join league");
    }
  };

  const leaveLeague = async (leagueId: string) => {
    if (!confirm("Are you sure you want to leave this league?")) return;

    try {
      const response = await fetch(`/api/leagues/${leagueId}/leave`, {
        method: "POST",
      });

      if (response.ok) {
        setLeagues(leagues.filter(l => l.id !== leagueId));
        toast.success("Left league successfully!");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to leave league");
      }
    } catch (error) {
      console.error("Error leaving league:", error);
      toast.error("Failed to leave league");
    }
  };

  const copyLeagueCode = async (leagueId: string) => {
    try {
      await navigator.clipboard.writeText(leagueId);
      setCopiedLeagueId(leagueId);
      toast.success("League code copied to clipboard!");
      setTimeout(() => setCopiedLeagueId(null), 2000);
    } catch {
      toast.error("Failed to copy league code");
    }
  };

  const isUserAdmin = (league: League) => {
    return league.admins.some(admin => admin.id === user?.id);
  };

  const isUserMember = (league: League) => {
    return league.members.some(member => member.id === user?.id);
  };

  const filteredLeagues = leagues.filter(league =>
    league.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Loading leagues...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold flex items-center gap-2">
            <Trophy className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
            Leagues
          </h1>
          <p className="text-muted-foreground">
            Join leagues to compete with friends and other players
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Create League
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New League</DialogTitle>
                <DialogDescription>
                  Create a private league to compete with friends
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="leagueName">League Name</Label>
                  <Input
                    id="leagueName"
                    placeholder="Enter league name"
                    value={newLeagueName}
                    onChange={(e) => setNewLeagueName(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={createLeague}>Create League</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={joinDialogOpen} onOpenChange={setJoinDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto">
                <UserPlus className="h-4 w-4 mr-2" />
                Join League
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Join League</DialogTitle>
                <DialogDescription>
                  Enter the league code to join an existing league
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="joinCode">League Code</Label>
                  <Input
                    id="joinCode"
                    placeholder="Enter league code"
                    value={joinCode}
                    onChange={(e) => setJoinCode(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setJoinDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={joinLeague}>Join League</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search leagues..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Leagues Grid */}
      {filteredLeagues.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No leagues found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? "No leagues match your search." : "You haven't joined any leagues yet."}
            </p>
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Button onClick={() => setCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create League
              </Button>
              <Button variant="outline" onClick={() => setJoinDialogOpen(true)}>
                <UserPlus className="h-4 w-4 mr-2" />
                Join League
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredLeagues.map((league) => (
            <Card key={league.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="flex items-center gap-2 truncate">
                      {isUserAdmin(league) && (
                        <Crown className="h-4 w-4 text-yellow-500 flex-shrink-0" />
                      )}
                      <span className="truncate">{league.name}</span>
                    </CardTitle>
                    <CardDescription className="flex items-center gap-2 mt-1">
                      <Users className="h-4 w-4" />
                      {league?._count?.members || 0} members
                    </CardDescription>
                  </div>
                  {isUserAdmin(league) && (
                    <Badge variant="secondary" className="ml-2">
                      Admin
                    </Badge>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium">League Code</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="bg-muted px-2 py-1 rounded text-sm flex-1 truncate">
                        {league.id}
                      </code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyLeagueCode(league.id)}
                        className="flex-shrink-0"
                      >
                        {copiedLeagueId === league.id ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {league.admins.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium">Admins</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {league.admins.slice(0, 3).map((admin) => (
                          <Badge key={admin.id} variant="outline" className="text-xs">
                            {admin.name || admin.email}
                          </Badge>
                        ))}
                        {league.admins.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{league.admins.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>

              <CardFooter className="flex gap-2">
                {isUserAdmin(league) ? (
                  <Button size="sm" className="flex-1">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage
                  </Button>
                ) : isUserMember(league) ? (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => leaveLeague(league.id)}
                    className="flex-1"
                  >
                    <UserMinus className="h-4 w-4 mr-2" />
                    Leave
                  </Button>
                ) : (
                  <Button
                    size="sm"
                    onClick={() => {
                      setJoinCode(league.id);
                      joinLeague();
                    }}
                    className="flex-1"
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Join
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

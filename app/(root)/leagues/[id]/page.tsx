"use client";

import { useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Crown,
  Trophy,
  Users,
  Settings,
  Copy,
  Check,
} from "lucide-react";
import { useLeague } from "@/hooks/useLeague";

export default function LeagueDetailPage({ params }: { params: { id: string } }) {
  const { user } = useUser();
  const router = useRouter();
  const {
    currentLeague,
    loading,
    fetchLeague,
    isUserAdmin,
    isUserMember,
    copyLeagueCode,
    copiedLeagueId,
  } = useLeague();

  useEffect(() => {
    if (user) {
      fetchLeague(params.id);
    }
  }, [user, params.id, fetchLeague]);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Loading league...</div>
      </div>
    );
  }

  if (!currentLeague) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">League not found</h1>
          <Button onClick={() => router.push("/leagues")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Leagues
          </Button>
        </div>
      </div>
    );
  }

  if (!isUserMember(currentLeague) && !isUserAdmin(currentLeague)) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don&#39;t have access to this league.
          </p>
          <Button onClick={() => router.push("/leagues")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Leagues
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Button
            variant="ghost"
            onClick={() => router.push("/leagues")}
            className="mb-2 p-0 h-auto"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Leagues
          </Button>
          <h1 className="text-2xl sm:text-3xl font-bold flex items-center gap-2">
            <Trophy className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
            {currentLeague.name}
          </h1>
          <p className="text-muted-foreground">
            League standings and player statistics
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          {isUserAdmin(currentLeague) && (
            <Button
              onClick={() => router.push(`/leagues/${params.id}/manage`)}
              className="w-full sm:w-auto"
            >
              <Settings className="h-4 w-4 mr-2" />
              Manage League
            </Button>
          )}
        </div>
      </div>

      {/* League Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>League Information</span>
            {isUserAdmin(currentLeague) && (
              <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300">
                <Crown className="h-3 w-3 mr-1" />
                Admin
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <div className="text-sm text-muted-foreground">Total Members</div>
              <div className="text-2xl font-bold">{currentLeague.members.length}</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">League Code</div>
              <div className="flex items-center gap-2">
                <code className="bg-muted px-2 py-1 rounded text-sm">
                  {currentLeague.id}
                </code>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyLeagueCode(currentLeague.id)}
                >
                  {copiedLeagueId === currentLeague.id ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different views */}
      <Tabs defaultValue="standings" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="standings">User Standings</TabsTrigger>
          <TabsTrigger value="players">Top Players</TabsTrigger>
        </TabsList>

        {/* User Standings */}
        <TabsContent value="standings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Standings
              </CardTitle>
              <CardDescription>
                League members ranked by total points
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">Rank</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Players</TableHead>
                    <TableHead className="text-right">Total Points</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentLeague.members.map((member, index) => (
                    <TableRow key={member.id}>
                      <TableCell className="font-medium">{index + 1}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              {(member.name || member.email).charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">
                              {member.name || "Unknown"}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {member.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">0</Badge>
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {member.totalPoints || 0} pts
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

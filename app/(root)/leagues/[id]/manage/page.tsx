"use client";

import { useEffect, useState } from "react";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  <PERSON>,
  UserMinus,
  MoreVertical,
  Copy,
  Check,
  Edit,
  Trash2,
  ArrowLeft,
  Shield,
  ShieldOff,
} from "lucide-react";
import { useLeague } from "@/hooks/useLeague";

export default function LeagueManagePage({ params }: { params: { id: string } }) {
  const { user } = useUser();
  const router = useRouter();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [newLeagueName, setNewLeagueName] = useState("");
  
  const {
    currentLeague,
    loading,
    fetchLeague,
    updateLeague,
    deleteLeague,
    removeMember,
    toggleAdmin,
    isUserAdmin,
    copyLeagueCode,
    copiedLeagueId,
  } = useLeague();

  useEffect(() => {
    if (user) {
      fetchLeague(params.id);
    }
  }, [user, params.id, fetchLeague]);

  useEffect(() => {
    if (currentLeague) {
      setNewLeagueName(currentLeague.name);
    }
  }, [currentLeague]);

  const handleUpdateLeague = async () => {
    const success = await updateLeague(params.id, { name: newLeagueName });
    if (success) {
      setEditDialogOpen(false);
    }
  };

  const handleDeleteLeague = async () => {
    const success = await deleteLeague(params.id);
    if (success) {
      router.push("/leagues");
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!confirm("Are you sure you want to remove this member?")) return;
    await removeMember(params.id, memberId);
  };

  const handleToggleAdmin = async (memberId: string, isCurrentlyAdmin: boolean) => {
    await toggleAdmin(params.id, memberId, isCurrentlyAdmin);
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Loading league...</div>
      </div>
    );
  }

  if (!currentLeague) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">League not found</div>
      </div>
    );
  }

  if (!isUserAdmin(currentLeague)) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don&#39;t have permission to manage this league.
          </p>
          <Button onClick={() => router.push("/leagues")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Leagues
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Button
            variant="ghost"
            onClick={() => router.push("/leagues")}
            className="mb-2 p-0 h-auto"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Leagues
          </Button>
          <h1 className="text-2xl sm:text-3xl font-bold flex items-center gap-2">
            <Crown className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500" />
            Manage League
          </h1>
          <p className="text-muted-foreground">
            Manage members and settings for {currentLeague.name}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto">
                <Edit className="h-4 w-4 mr-2" />
                Edit League
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit League</DialogTitle>
                <DialogDescription>
                  Update the league name and settings
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="leagueName">League Name</Label>
                  <Input
                    id="leagueName"
                    value={newLeagueName}
                    onChange={(e) => setNewLeagueName(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleUpdateLeague}>Save Changes</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="destructive" className="w-full sm:w-auto">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete League
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete League</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this league? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button variant="destructive" onClick={handleDeleteLeague}>
                  Delete League
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* League Info */}
      <Card>
        <CardHeader>
          <CardTitle>{currentLeague.name}</CardTitle>
          <CardDescription>
            League information and invite code
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">League Code</Label>
              <div className="flex items-center gap-2 mt-1">
                <code className="bg-muted px-3 py-2 rounded text-sm flex-1">
                  {currentLeague.id}
                </code>
                <Button
                  variant="outline"
                  onClick={() => copyLeagueCode(currentLeague.id)}
                  className="flex-shrink-0"
                >
                  {copiedLeagueId === currentLeague.id ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Share this code with others to invite them to your league
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Total Members</Label>
                <p className="text-2xl font-bold">{currentLeague.members.length}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Admins</Label>
                <p className="text-2xl font-bold">{currentLeague.admins.length}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Members Table */}
      <Card>
        <CardHeader>
          <CardTitle>Members ({currentLeague.members.length})</CardTitle>
          <CardDescription>
            Manage league members and their permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Member</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Points</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentLeague.members.map((member) => {
                const isAdmin = currentLeague.admins.some(admin => admin.id === member.id);
                const isCurrentUser = member.id === user?.id;
                
                return (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            {(member.name || member.email).charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {member.name || "Unknown"}
                            {isCurrentUser && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                You
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {member.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {isAdmin ? (
                        <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300">
                          <Crown className="h-3 w-3 mr-1" />
                          Admin
                        </Badge>
                      ) : (
                        <Badge variant="outline">Member</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {member.totalPoints || 0} pts
                    </TableCell>
                    <TableCell className="text-right">
                      {!isCurrentUser && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => handleToggleAdmin(member.id, isAdmin)}
                            >
                              {isAdmin ? (
                                <>
                                  <ShieldOff className="h-4 w-4 mr-2" />
                                  Remove Admin
                                </>
                              ) : (
                                <>
                                  <Shield className="h-4 w-4 mr-2" />
                                  Make Admin
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleRemoveMember(member.id)}
                              className="text-destructive"
                            >
                              <UserMinus className="h-4 w-4 mr-2" />
                              Remove Member
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

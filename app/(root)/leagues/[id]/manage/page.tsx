"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  <PERSON>,
  User<PERSON>inus,
  MoreVertical,
  Co<PERSON>,
  Check,
  Edit,
  Trash2,
  ArrowLeft,
  Shield,
  ShieldOff,
} from "lucide-react";
import { toast } from "sonner";

interface League {
  id: string;
  name: string;
  admins: User[];
  members: User[];
}

interface User {
  id: string;
  name: string;
  email: string;
  totalPoints?: number;
}

export default function LeagueManagePage({ params }: { params: { id: string } }) {
  const { user } = useUser();
  const router = useRouter();
  const [league, setLeague] = useState<League | null>(null);
  const [loading, setLoading] = useState(true);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [newLeagueName, setNewLeagueName] = useState("");
  const [copiedCode, setCopiedCode] = useState(false);

  useEffect(() => {
    const fetchLeague = async () => {
      try {
        const response = await fetch(`/api/leagues/${params.id}`);
        if (response.ok) {
          const data = await response.json();
          setLeague(data);
          setNewLeagueName(data.name);
        } else if (response.status === 404) {
          toast.error("League not found");
          router.push("/leagues");
        } else {
          toast.error("Failed to load league");
        }
      } catch (error) {
        console.error("Error fetching league:", error);
        toast.error("Failed to load league");
      } finally {
        setLoading(false);
      }
    };
    if (user) {
      fetchLeague();
    }
  }, [user, params.id, router]);


  const updateLeague = async () => {
    if (!newLeagueName.trim()) {
      toast.error("Please enter a league name");
      return;
    }

    try {
      const response = await fetch(`/api/leagues/${params.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: newLeagueName }),
      });

      if (response.ok) {
        const updatedLeague = await response.json();
        setLeague(updatedLeague);
        setEditDialogOpen(false);
        toast.success("League updated successfully!");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update league");
      }
    } catch (error) {
      console.error("Error updating league:", error);
      toast.error("Failed to update league");
    }
  };

  const deleteLeague = async () => {
    try {
      const response = await fetch(`/api/leagues/${params.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("League deleted successfully!");
        router.push("/leagues");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete league");
      }
    } catch (error) {
      console.error("Error deleting league:", error);
      toast.error("Failed to delete league");
    }
  };

  const removeMember = async (memberId: string) => {
    if (!confirm("Are you sure you want to remove this member?")) return;

    try {
      const response = await fetch(`/api/leagues/${params.id}/members/${memberId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setLeague(prev => prev ? {
          ...prev,
          members: prev.members.filter(m => m.id !== memberId),
          admins: prev.admins.filter(a => a.id !== memberId)
        } : null);
        toast.success("Member removed successfully!");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to remove member");
      }
    } catch (error) {
      console.error("Error removing member:", error);
      toast.error("Failed to remove member");
    }
  };

  const toggleAdmin = async (memberId: string, isCurrentlyAdmin: boolean) => {
    try {
      const response = await fetch(`/api/leagues/${params.id}/admins/${memberId}`, {
        method: isCurrentlyAdmin ? "DELETE" : "POST",
      });

      if (response.ok) {
        const updatedLeague = await response.json();
        setLeague(updatedLeague);
        toast.success(
          isCurrentlyAdmin 
            ? "Admin rights removed successfully!" 
            : "Admin rights granted successfully!"
        );
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update admin rights");
      }
    } catch (error) {
      console.error("Error updating admin rights:", error);
      toast.error("Failed to update admin rights");
    }
  };

  const copyLeagueCode = async () => {
    try {
      await navigator.clipboard.writeText(params.id);
      setCopiedCode(true);
      toast.success("League code copied to clipboard!");
      setTimeout(() => setCopiedCode(false), 2000);
    } catch {
      toast.error("Failed to copy league code");
    }
  };

  const isUserAdmin = () => {
    return league?.admins.some(admin => admin.id === user?.id) || false;
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Loading league...</div>
      </div>
    );
  }

  if (!league) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">League not found</div>
      </div>
    );
  }

  if (!isUserAdmin()) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don&#39;t have permission to manage this league.
          </p>
          <Button onClick={() => router.push("/leagues")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Leagues
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Button
            variant="ghost"
            onClick={() => router.push("/leagues")}
            className="mb-2 p-0 h-auto"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Leagues
          </Button>
          <h1 className="text-2xl sm:text-3xl font-bold flex items-center gap-2">
            <Crown className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500" />
            Manage League
          </h1>
          <p className="text-muted-foreground">
            Manage members and settings for {league.name}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto">
                <Edit className="h-4 w-4 mr-2" />
                Edit League
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit League</DialogTitle>
                <DialogDescription>
                  Update the league name and settings
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="leagueName">League Name</Label>
                  <Input
                    id="leagueName"
                    value={newLeagueName}
                    onChange={(e) => setNewLeagueName(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={updateLeague}>Save Changes</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="destructive" className="w-full sm:w-auto">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete League
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete League</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this league? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button variant="destructive" onClick={deleteLeague}>
                  Delete League
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* League Info */}
      <Card>
        <CardHeader>
          <CardTitle>{league.name}</CardTitle>
          <CardDescription>
            League information and invite code
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">League Code</Label>
              <div className="flex items-center gap-2 mt-1">
                <code className="bg-muted px-3 py-2 rounded text-sm flex-1">
                  {league.id}
                </code>
                <Button
                  variant="outline"
                  onClick={copyLeagueCode}
                  className="flex-shrink-0"
                >
                  {copiedCode ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Share this code with others to invite them to your league
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Total Members</Label>
                <p className="text-2xl font-bold">{league.members.length}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Admins</Label>
                <p className="text-2xl font-bold">{league.admins.length}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Members Table */}
      <Card>
        <CardHeader>
          <CardTitle>Members ({league.members.length})</CardTitle>
          <CardDescription>
            Manage league members and their permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Member</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Points</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {league.members.map((member) => {
                const isAdmin = league.admins.some(admin => admin.id === member.id);
                const isCurrentUser = member.id === user?.id;
                
                return (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            {(member.name || member.email).charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {member.name || "Unknown"}
                            {isCurrentUser && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                You
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {member.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {isAdmin ? (
                        <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300">
                          <Crown className="h-3 w-3 mr-1" />
                          Admin
                        </Badge>
                      ) : (
                        <Badge variant="outline">Member</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {member.totalPoints || 0} pts
                    </TableCell>
                    <TableCell className="text-right">
                      {!isCurrentUser && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => toggleAdmin(member.id, isAdmin)}
                            >
                              {isAdmin ? (
                                <>
                                  <ShieldOff className="h-4 w-4 mr-2" />
                                  Remove Admin
                                </>
                              ) : (
                                <>
                                  <Shield className="h-4 w-4 mr-2" />
                                  Make Admin
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => removeMember(member.id)}
                              className="text-destructive"
                            >
                              <UserMinus className="h-4 w-4 mr-2" />
                              Remove Member
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

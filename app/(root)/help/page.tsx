import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  HelpCircle, 
  Search, 
  Users, 
  RefreshCw, 
  Trophy, 
  Target,
  MessageCircle,
  Book,
  Video,
  Mail
} from "lucide-react";
import Link from "next/link";

export default function HelpCenterPage() {
  const helpCategories = [
    {
      title: "Getting Started",
      icon: Book,
      description: "Learn the basics of TFL",
      articles: [
        "How to create your first team",
        "Understanding player positions",
        "Setting up your captain and vice-captain",
        "Navigating the interface"
      ]
    },
    {
      title: "Team Management",
      icon: Users,
      description: "Managing your fantasy team",
      articles: [
        "Making transfers",
        "Using wildcards effectively",
        "Formation strategies",
        "Player selection tips"
      ]
    },
    {
      title: "Scoring System",
      icon: Target,
      description: "How points are calculated",
      articles: [
        "Player scoring breakdown",
        "Bonus points system",
        "Captain scoring rules",
        "Clean sheet points"
      ]
    },
    {
      title: "Leagues & Competition",
      icon: Trophy,
      description: "Competing with others",
      articles: [
        "Joining public leagues",
        "Creating private leagues",
        "League scoring rules",
        "Prize information"
      ]
    }
  ];

  const popularArticles = [
    "How do I make my first transfer?",
    "What is a wildcard and when should I use it?",
    "How are player prices determined?",
    "Can I change my team name?",
    "What happens if a player doesn't play?",
    "How do I join a private league?"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-full">
              <HelpCircle className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-3xl sm:text-4xl font-bold">Help Center</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Find answers to your questions and learn how to master TFL
          </p>
        </div>

        {/* Search Bar */}
        <Card className="max-w-2xl mx-auto">
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search for help articles..."
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-4xl mx-auto">
          <Link href="/contact">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-6 text-center">
                <MessageCircle className="h-8 w-8 mx-auto mb-3 text-blue-500" />
                <h3 className="font-semibold mb-2">Contact Support</h3>
                <p className="text-sm text-muted-foreground">Get help from our team</p>
              </CardContent>
            </Card>
          </Link>
          
          <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
            <CardContent className="p-6 text-center">
              <Video className="h-8 w-8 mx-auto mb-3 text-green-500" />
              <h3 className="font-semibold mb-2">Video Tutorials</h3>
              <p className="text-sm text-muted-foreground">Watch step-by-step guides</p>
            </CardContent>
          </Card>
          
          <Link href="/faq">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-6 text-center">
                <HelpCircle className="h-8 w-8 mx-auto mb-3 text-purple-500" />
                <h3 className="font-semibold mb-2">FAQ</h3>
                <p className="text-sm text-muted-foreground">Common questions</p>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Help Categories */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-center">Browse by Category</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {helpCategories.map((category) => (
              <Card key={category.title} className="hover:shadow-md transition-all duration-200">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/10 p-2 rounded-lg">
                      <category.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{category.title}</CardTitle>
                      <CardDescription>{category.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {category.articles.map((article, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        <span className="text-sm">{article}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Popular Articles */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Popular Articles
            </CardTitle>
            <CardDescription>
              Most frequently accessed help articles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid sm:grid-cols-2 gap-3">
              {popularArticles.map((article, index) => (
                <div key={index} className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <Badge variant="outline" className="text-xs">
                    {index + 1}
                  </Badge>
                  <span className="text-sm">{article}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Still Need Help */}
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
          <CardContent className="p-8 text-center">
            <Mail className="h-12 w-12 mx-auto mb-4 text-primary" />
            <h2 className="text-2xl font-bold mb-4">Still Need Help?</h2>
            <p className="text-muted-foreground mb-6">
              Can't find what you're looking for? Our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <Button>
                  Contact Support
                </Button>
              </Link>
              <Button variant="outline">
                Join Community
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

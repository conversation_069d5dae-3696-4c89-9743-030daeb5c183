"use client";

import Header from "@/components/Header";
import InfoPanel from "@/components/InfoPanel";
import { useFantasy } from "@/hooks/useFantasy";
import TeamTable from "@/components/TeamTable";
import PitchView from "@/components/PitchView";
import PlayerModal from "@/components/PlayerModal";
import { useState } from "react";
import { Player } from "@prisma/client";

export default function TeamPageContent({ userId }: { userId: string }) {
  const LATEST_ROUND = "1";
  const round = LATEST_ROUND;

  const {
    isLoading,
    subs,
    starters,
    hasChanges,
    view,
    setView,
    saveTeam,
    getSubsList,
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
    stats,
  } = useFantasy(userId, round);

  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);

  if (isLoading) {
    return <div className="p-6">Loading team data...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="w-full lg:w-2/3">
          <Header
            view={view}
            setView={setView}
            userId={userId}
            round={round}
            isDreamTeamVisible={false}
            isPreviousFixtureVisible={round !== "1"}
            hasChanges={hasChanges}
            isLoading={isLoading}
            onSave={() => saveTeam(userId, round)}
            title="Your Team"
          />
          {view === "list" ? (
            <>
              <TeamTable players={starters} onSelect={setSelectedPlayer} />
              <h3 className="text-xl font-semibold text-foreground mt-6">
                Substitutes
              </h3>
              <TeamTable players={subs} onSelect={setSelectedPlayer} />
            </>
          ) : (
            <PitchView
              starters={starters}
              subs={subs}
              onSelect={setSelectedPlayer}
              showRole
              showNextFixture
            />
          )}

          <PlayerModal
            player={selectedPlayer}
            starters={starters}
            subs={subs}
            subsList={getSubsList?.(selectedPlayer) ?? []}
            onClose={() => setSelectedPlayer(null)}
            onMakeCaptain={handleCaptainChange}
            onMakeViceCaptain={handleViceCaptainChange}
            onSubstitute={handleSubstitute}
            isEditable
          />
        </div>
        <div className="w-full lg:w-1/3">
          <InfoPanel
            squadValue={stats.squadValue}
            transfersLeft={stats.transfersRemaining}
          />
        </div>
      </div>
    </div>
  );
}

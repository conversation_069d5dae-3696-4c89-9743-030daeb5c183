"use client";

import Header from "@/components/Header";
import StatsPanel from "@/components/StatsPanel";
import InfoPanel from "@/components/InfoPanel";
import TeamSection from "@/components/TeamSection";
import { useFantasy } from "@/hooks/useFantasy";

export default function TeamPageContent({ userId }: { userId: string }) {
  const LATEST_ROUND = "1";
  const round = LATEST_ROUND;

  const {
    stats,
    isLoading,
    subs,
    starters,
    hasChanges,
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
    saveTeam,
  } = useFantasy(userId, LATEST_ROUND);

  if (isLoading) {
    return <div className="p-6">Loading team data...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <Header
        round={round}
        isDreamTeamVisible={false}
        isPreviousFixtureVisible={round !== "1"}
      />
      <StatsPanel
        avgPoints={stats.avgPoints}
        highestPoints={stats.highestPoints}
        totalGWPoints={stats.totalGWPoints}
        roundRank={stats.roundRank}
        transfersRemaining={stats.transfersRemaining}
      />
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="w-full lg:w-2/3">
          <TeamSection
            starters={starters}
            subs={subs}
            hasChanges={hasChanges}
            onCaptainChange={handleCaptainChange}
            onViceCaptainChange={handleViceCaptainChange}
            onSubstitute={handleSubstitute}
            onSave={saveTeam}
            isLoading={isLoading}
            isTeamPage
          />
        </div>
        <div className="w-full lg:w-1/3">
          <InfoPanel
            squadValue={stats.squadValue}
            transfersLeft={stats.transfersRemaining}
          />
        </div>
      </div>
    </div>
  );
}

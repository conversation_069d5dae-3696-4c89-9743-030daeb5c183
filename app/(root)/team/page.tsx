import { getCurrentUser } from "@/lib/auth";
import TeamPageContent from "./TeamPageContent";
import { ViewProvider } from "@/contexts/ViewContext";

export default async function TeamPage() {
  const user = await getCurrentUser();

  if (!user) return <div className="p-6">Loading team data...</div>;

  return (
    <ViewProvider defaultView="pitch">
      <TeamPageContent userId={user.id} />
    </ViewProvider>
  );
}

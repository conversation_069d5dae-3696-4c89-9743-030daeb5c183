"use client";

import Header from "@/components/Header";
import InfoPanel from "@/components/InfoPanel";
import { useFantasy } from "@/hooks/useFantasy";
import TeamTable from "@/components/TeamTable";
import PitchView from "@/components/PitchView";
import PlayerModal from "@/components/PlayerModal";
import { useState } from "react";
import { validateTeamComposition } from "@/lib/utils";
import { PlayerInfo } from "@/types/types";
import { useUser } from "@clerk/nextjs";

export default function Page() {
  const { user } = useUser();
  const userId = user?.id;
  const {
    activeGameweek,
    isLoading,
    subs,
    starters,
    captainId,
    viceCaptainId,
    hasChanges,
    view,
    setView,
    saveTeam,
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
    stats,
  } = useFantasy();

  const [selectedPlayer, setSelectedPlayer] = useState<PlayerInfo | null>(null);
  // Validate team before enabling save
  const validation = validateTeamComposition(starters, subs);
  const isSaveEnabled = hasChanges && validation.isValid;

  if (isLoading || !userId) {
    return <div className="p-6">Loading team data...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-4 sm:space-y-6">
        {/* Mobile-first responsive layout */}
        <div className="flex flex-col xl:flex-row gap-4 sm:gap-6">
          {/* Main content area */}
          <div className="w-full xl:w-2/3 space-y-4 sm:space-y-6">
            <Header
              view={view}
              setView={setView}
              userId={userId}
              isDreamTeamVisible={false}
              hasChanges={hasChanges}
              isLoading={isLoading}
              onSave={() => saveTeam(userId)}
              title="Your Team"
              isSaveDisabled={!isSaveEnabled}
            />

            {/* Team display */}
            <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-3 sm:p-4 lg:p-6">
              {view === "list" ? (
                <div className="space-y-4 sm:space-y-6">
                  <div>
                    <h3 className="text-lg sm:text-xl font-semibold text-foreground mb-3 sm:mb-4">
                      Starting XI
                    </h3>
                    <TeamTable
                      players={starters}
                      onSelect={setSelectedPlayer}
                    />
                  </div>
                  <div>
                    <h3 className="text-lg sm:text-xl font-semibold text-foreground mb-3 sm:mb-4">
                      Substitutes
                    </h3>
                    <TeamTable players={subs} onSelect={setSelectedPlayer} />
                  </div>
                </div>
              ) : (
                <PitchView
                  round={activeGameweek?.id || ""}
                  starters={starters}
                  subs={subs}
                  onSelect={setSelectedPlayer}
                  captainId={captainId}
                  viceCaptainId={viceCaptainId}
                  showRole
                  showNextFixture
                />
              )}
            </div>

            <PlayerModal
              player={selectedPlayer}
              starters={starters}
              subs={subs}
              onClose={() => setSelectedPlayer(null)}
              onMakeCaptain={handleCaptainChange}
              onMakeViceCaptain={handleViceCaptainChange}
              onSubstitute={handleSubstitute}
              isEditable
            />
          </div>

          {/* Sidebar */}
          <div className="w-full xl:w-1/3">
            <div className="sticky top-4">
              <InfoPanel
                squadValue={stats.squadValue}
                transfersLeft={stats.transfersRemaining}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

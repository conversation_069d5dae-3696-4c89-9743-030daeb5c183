import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Trophy, 
  Medal, 
  Gift, 
  Calendar, 
  Star, 
  Crown,
  Zap,
  Target
} from "lucide-react";

export default function PrizesPage() {
  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-4 rounded-full">
            <Trophy className="h-12 w-12 text-white" />
          </div>
        </div>
        <h1 className="text-4xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
          Prizes & Rewards
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Compete for amazing prizes and rewards in the Tunisian Fantasy League. 
          Weekly prizes to be announced soon!
        </p>
      </div>

      {/* Coming Soon Banner */}
      <Card className="border-2 border-dashed border-primary/50 bg-primary/5">
        <CardContent className="p-8 text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-primary/10 p-3 rounded-full">
              <Calendar className="h-8 w-8 text-primary" />
            </div>
          </div>
          <h2 className="text-2xl font-bold mb-2">Weekly Prizes Coming Soon!</h2>
          <p className="text-muted-foreground mb-4">
            We&#39;re preparing exciting weekly prizes for our fantasy league participants. 
            Stay tuned for announcements about cash prizes, merchandise, and exclusive rewards.
          </p>
          <Badge variant="secondary" className="text-sm">
            Announcements Expected Soon
          </Badge>
        </CardContent>
      </Card>

      {/* Prize Categories */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-yellow-400 to-transparent opacity-20"></div>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="bg-yellow-100 dark:bg-yellow-900/20 p-2 rounded-lg">
                <Crown className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <CardTitle className="text-lg">Weekly Champion</CardTitle>
                <CardDescription>Highest scoring manager each week</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Prize Pool</span>
                <Badge variant="outline">TBA</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                The manager with the highest points in each gameweek will be crowned weekly champion.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-blue-400 to-transparent opacity-20"></div>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="bg-blue-100 dark:bg-blue-900/20 p-2 rounded-lg">
                <Medal className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-lg">Monthly Rewards</CardTitle>
                <CardDescription>Top performers each month</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Top 3 Managers</span>
                <Badge variant="outline">TBA</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Monthly leaderboard winners will receive special recognition and prizes.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-green-400 to-transparent opacity-20"></div>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="bg-green-100 dark:bg-green-900/20 p-2 rounded-lg">
                <Target className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <CardTitle className="text-lg">Season Grand Prize</CardTitle>
                <CardDescription>Ultimate fantasy champion</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Grand Champion</span>
                <Badge variant="outline">TBA</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                The overall season winner will receive the grand prize and eternal glory.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-purple-400 to-transparent opacity-20"></div>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="bg-purple-100 dark:bg-purple-900/20 p-2 rounded-lg">
                <Zap className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <CardTitle className="text-lg">Special Achievements</CardTitle>
                <CardDescription>Milestone rewards</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Various Milestones</span>
                <Badge variant="outline">TBA</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Special rewards for achieving specific milestones and records.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-red-400 to-transparent opacity-20"></div>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="bg-red-100 dark:bg-red-900/20 p-2 rounded-lg">
                <Gift className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <CardTitle className="text-lg">Merchandise</CardTitle>
                <CardDescription>Exclusive TFL items</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Branded Items</span>
                <Badge variant="outline">TBA</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Exclusive TFL merchandise for top performers and special events.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-indigo-400 to-transparent opacity-20"></div>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="bg-indigo-100 dark:bg-indigo-900/20 p-2 rounded-lg">
                <Star className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <CardTitle className="text-lg">League Prizes</CardTitle>
                <CardDescription>Private league rewards</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">League Winners</span>
                <Badge variant="outline">TBA</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Special prizes for winners of private leagues and competitions.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* How to Win Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            How to Win Prizes
          </CardTitle>
          <CardDescription>
            Tips to maximize your chances of winning rewards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold">Weekly Competitions</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Score the highest points in a gameweek</li>
                <li>• Make smart captain choices</li>
                <li>• Use your transfers wisely</li>
                <li>• Stay active and engaged</li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-semibold">Season-Long Success</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Maintain consistent performance</li>
                <li>• Join and win private leagues</li>
                <li>• Achieve special milestones</li>
                <li>• Participate in community events</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stay Updated */}
      <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
        <CardContent className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Stay Updated</h2>
          <p className="text-muted-foreground mb-6">
            Be the first to know when prizes are announced. Follow our updates and join our community.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="default">
              Join Community
            </Button>
            <Button variant="outline">
              Follow Updates
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

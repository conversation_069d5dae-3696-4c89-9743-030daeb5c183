"use client";

import { useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import {
  Phone,
  MapPin,
  Users,
  ArrowRight,
  ArrowLeft,
  Check,
} from "lucide-react";
import { useFantasy } from "@/hooks/useFantasy";
import PitchView from "@/components/PitchView";
import TeamTable from "@/components/TeamTable";
import TransferFilters from "@/components/TransferFilters";
import PlayerModal from "@/components/PlayerModal";
import TeamViewToggle from "@/components/TeamViewToggle";
import { sortPlayers } from "@/lib/utils";
import { PlayerInfo } from "@/types/types";
import { Avatar } from "@radix-ui/react-avatar";
import { AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Team } from "@prisma/client";

export default function OnboardingPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [phone, setPhone] = useState("");
  const [country, setCountry] = useState("Tunisia");
  const [favoriteClub, setFavoriteClub] = useState("");
  const [fullName, setFullName] = useState("");

  // Player selection state
  const [selectedPlayer, setSelectedPlayer] = useState<PlayerInfo | null>(null);

  // Fantasy hook for player management (only initialize if we have a user)
  const {
    view,
    setView,
    starters,
    subs,
    captainId,
    viceCaptainId,
    hasChanges,
    removedPlayers,
    teams,
    handleRemove,
    handleAdd,
    saveTeam,
    filteredMarket,
    filterPosition,
    setFilterPosition,
    filterTeam,
    setFilterTeam,
    filterPrice,
    setFilterPrice,
    priceRangeMax,
    availableTeams,
    handleRestore,
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
  } = useFantasy(null, null, true);

  // Player selection handlers
  const onRemovePlayer = (player: PlayerInfo | null = selectedPlayer) => {
    if (!player) return;
    const position = handleRemove(player);
    if (position) setFilterPosition(position);
    setSelectedPlayer(null);
  };

  const onAddPlayer = (player: PlayerInfo) => {
    if (!player || !selectedPlayer || !removedPlayers.length) return;
    const success = handleAdd(player);
    if (success) setSelectedPlayer(null);
  };

  const onRestorePlayer = (player: PlayerInfo) => {
    if (!player) return;
    handleRestore(player);
    setSelectedPlayer(null);
  };

  const handleNextStep = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isSaveEnabled = useMemo(() => {
    if (currentStep === 1) {
      return !!phone && !!fullName;
    }

    if (currentStep === 2) {
      return !!country;
    }

    if (currentStep === 3) {
      return !!favoriteClub;
    }

    if (currentStep === 4) {
      return (
        starters.length === 11 &&
        subs.length === 4 &&
        removedPlayers.length === 0
      );
    }

    if (currentStep === 5) {
      return !!captainId && !!viceCaptainId;
    }

    return false;
  }, [
    currentStep,
    phone,
    fullName,
    country,
    favoriteClub,
    starters,
    subs,
    removedPlayers,
    captainId,
    viceCaptainId,
  ]);

  const handleSubmit = async () => {
    if (!user) return;

    setIsSubmitting(true);

    try {
      // First, create the user profile
      const userResponse = await fetch("/api/user", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: user.id,
          email: user.emailAddresses[0]?.emailAddress,
          name: fullName,
          phone,
          country,
          favoriteClub,
        }),
      });

      if (!userResponse.ok) {
        throw new Error("Failed to add user profile");
      }

      // Then, save the team if there are any changes
      if (hasChanges && (starters.length === 11 || subs.length === 4)) {
        await saveTeam(user.id, "1", true);
      }

      toast.success("Profile and team setup completed successfully!");
      router.push("/team");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to complete setup. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isLoaded || !user) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div
      className={`mx-auto p-8 space-y-6 ${
        currentStep === 4 ? "max-w-6xl" : "max-w-md"
      }`}
    >
      <h1 className="text-2xl font-bold text-center">Welcome to TFL!</h1>
      <div className="flex justify-center mb-6">
        <div className="flex items-center space-x-2">
          {[1, 2, 3, 4, 5].map((step) => (
            <div key={step} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  currentStep === step
                    ? "bg-primary text-primary-foreground"
                    : currentStep > step
                    ? "bg-primary/20 text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                {currentStep > step ? <Check className="w-4 h-4" /> : step}
              </div>
              {step < 5 && (
                <div
                  className={`w-10 h-1 ${
                    currentStep > step ? "bg-primary/50" : "bg-muted"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {currentStep === 1 && "Contact Information"}
            {currentStep === 2 && "Location"}
            {currentStep === 3 && "Team Preference"}
            {currentStep === 4 && "Choose Your Players"}
            {currentStep === 5 && "choose roles"}
          </CardTitle>
          <CardDescription>
            {currentStep === 1 &&
              "Add your phone number to complete your profile"}
            {currentStep === 2 && "Tell us where you're from"}
            {currentStep === 3 && "Select your favorite team"}
            {currentStep === 4 &&
              "Build your fantasy team by selecting players"}
            {currentStep === 5 && "Choose your captain and vice-captain"}
          </CardDescription>
        </CardHeader>

        <CardContent>
          {currentStep === 1 && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <div className="flex items-center space-x-2">
                  <Phone className="w-4 h-4 text-muted-foreground" />
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="+216 XX XXX XXX"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="fullName">Full Name</Label>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-muted-foreground" />
                  <Input
                    id="fullName"
                    type="text"
                    placeholder="John Doe"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <Select value={country} onValueChange={setCountry}>
                    <SelectTrigger id="country">
                      <SelectValue placeholder="Select your country" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Tunisia">Tunisia</SelectItem>
                      <SelectItem value="Algeria">Algeria</SelectItem>
                      <SelectItem value="Morocco">Morocco</SelectItem>
                      <SelectItem value="Libya">Libya</SelectItem>
                      <SelectItem value="Egypt">Egypt</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="team">Favorite Team</Label>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-muted-foreground" />
                  <Select value={favoriteClub} onValueChange={setFavoriteClub}>
                    <SelectTrigger id="team">
                      <SelectValue placeholder="Select your favorite team" />
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((team: Team) => (
                        <SelectItem key={team.id} value={team.id}>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={team.jersey} />
                              <AvatarFallback>{team.abbr}</AvatarFallback>
                            </Avatar>
                            <span>{team.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {currentStep === 4 && (
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Your Team</h3>
                <TeamViewToggle view={view} setView={setView} />
              </div>

              <div className="flex flex-col lg:flex-row gap-4">
                <div className="w-full lg:w-2/3">
                  {view === "list" ? (
                    <TeamTable
                      players={[...starters, ...subs]}
                      onSelect={setSelectedPlayer}
                    />
                  ) : (
                    <PitchView
                      starters={[...starters, ...subs]}
                      removedPlayers={removedPlayers}
                      onSelect={setSelectedPlayer}
                      onRemove={onRemovePlayer}
                      showPrice
                      isBuildingTeam
                    />
                  )}
                </div>

                <div className="w-full lg:w-1/3">
                  <TransferFilters
                    allPlayers={filteredMarket}
                    selectedPosition={filterPosition}
                    selectedTeam={filterTeam}
                    selectedPrice={filterPrice}
                    maxPossiblePrice={priceRangeMax}
                    availableTeams={availableTeams}
                    onPositionChange={setFilterPosition}
                    onTeamChange={setFilterTeam}
                    onPriceChange={setFilterPrice}
                  />
                  <div className="mt-4 max-h-96 overflow-y-auto">
                    <TeamTable
                      players={sortPlayers(filteredMarket, "price")}
                      onSelect={setSelectedPlayer}
                    />
                  </div>
                </div>
              </div>

              <PlayerModal
                starters={starters}
                subs={subs}
                player={selectedPlayer}
                removedPlayers={removedPlayers}
                onClose={() => setSelectedPlayer(null)}
                isBuildingTeam
                isEditable
                onRemove={onRemovePlayer}
                onAdd={onAddPlayer}
                onRestore={onRestorePlayer}
              />
            </div>
          )}
          {currentStep === 5 && (
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Your Team</h3>
                <TeamViewToggle view={view} setView={setView} />
              </div>

              <div className="flex flex-col lg:flex-row gap-4">
                <div className="w-full lg:w-2/3">
                  {view === "list" ? (
                    <>
                      <h3 className="text-lg font-semibold">Starters</h3>
                      <TeamTable
                        players={starters}
                        onSelect={setSelectedPlayer}
                      />
                      <h3 className="text-lg font-semibold">Substitutes</h3>
                      <TeamTable players={subs} onSelect={setSelectedPlayer} />
                    </>
                  ) : (
                    <PitchView
                      starters={starters}
                      subs={subs}
                      removedPlayers={removedPlayers}
                      onSelect={setSelectedPlayer}
                      showRole
                    />
                  )}
                </div>
              </div>

              <PlayerModal
                starters={starters}
                subs={subs}
                player={selectedPlayer}
                removedPlayers={removedPlayers}
                onClose={() => setSelectedPlayer(null)}
                isEditable
                onMakeCaptain={handleCaptainChange}
                onMakeViceCaptain={handleViceCaptainChange}
                onSubstitute={handleSubstitute}
              />
            </div>
          )}
        </CardContent>

        <CardFooter className="flex justify-between">
          {currentStep > 1 ? (
            <Button variant="outline" onClick={handlePrevStep}>
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
          ) : (
            <div></div>
          )}

          {currentStep < 5 ? (
            <Button onClick={handleNextStep} disabled={!isSaveEnabled}>
              Next <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={!isSaveEnabled}>
              {isSubmitting ? "Saving..." : "Complete Setup"}
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}

"use client";

import { use<PERSON>emo, useState } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import {
  Phone,
  MapPin,
  Users,
  ArrowRight,
  ArrowLeft,
  Check,
  Lock,
} from "lucide-react";
import { useFantasy } from "@/hooks/useFantasy";
import PitchView from "@/components/PitchView";
import TeamTable from "@/components/TeamTable";
import TransferFilters from "@/components/TransferFilters";
import PlayerModal from "@/components/PlayerModal";
import TeamViewToggle from "@/components/TeamViewToggle";
import { sortPlayers } from "@/lib/utils";
import { PlayerInfo } from "@/types/types";
import { Avatar } from "@radix-ui/react-avatar";
import { AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Team } from "@prisma/client";

export default function OnboardingPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [phone, setPhone] = useState("");
  const [country, setCountry] = useState("Tunisia");
  const [favoriteClub, setFavoriteClub] = useState("");
  const [fullName, setFullName] = useState("");

  // SMS verification state
  const [verificationCode, setVerificationCode] = useState("");
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [isVerifyingCode, setIsVerifyingCode] = useState(false);
  const [canResendCode, setCanResendCode] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);

  // Player selection state
  const [selectedPlayer, setSelectedPlayer] = useState<PlayerInfo | null>(null);

  // Fantasy hook for player management (only initialize if we have a user)
  const {
    activeGameweek,
    view,
    setView,
    starters,
    subs,
    captainId,
    viceCaptainId,
    hasChanges,
    removedPlayers,
    teams,
    handleRemove,
    handleAdd,
    saveTeam,
    filteredMarket,
    filterPosition,
    setFilterPosition,
    filterTeam,
    setFilterTeam,
    filterPrice,
    setFilterPrice,
    priceRangeMax,
    availableTeams,
    handleRestore,
    handleCaptainChange,
    handleViceCaptainChange,
    handleSubstitute,
  } = useFantasy(null, true);

  // Debug function to test Twilio configuration
  const testTwilioConfig = async () => {
    try {
      const response = await fetch('/api/sms/test');
      const data = await response.json();
      console.log("Twilio test result:", data);

      if (data.status === 'success') {
        toast.success("Twilio configuration is working!");
      } else {
        toast.error(`Twilio test failed: ${data.message}`);
        console.error("Twilio test details:", data);
      }
    } catch (error) {
      console.error("Failed to test Twilio config:", error);
      toast.error("Failed to test Twilio configuration");
    }
  };

  // SMS verification functions
  const sendVerificationCode = async () => {
    if (!phone.trim()) {
      toast.error("Please enter a phone number");
      return;
    }

    setIsSendingCode(true);
    try {
      console.log("Sending verification code to:", phone);
      const response = await fetch('/api/sms/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber: phone }),
      });
      const data = await response.json();
      console.log("Response:", data);

      if (response.ok) {
        toast.success("Verification code sent to your phone");
        setCurrentStep(2);
        startResendTimer();
      } else {
        console.error("SMS send failed:", data);
        toast.error(data.error || "Failed to send verification code");
        if (data.details) {
          console.error("Error details:", data.details);
        }
      }
    } catch {
      toast.error("Failed to send verification code");
    } finally {
      setIsSendingCode(false);
    }
  };

  const verifyCode = async () => {
    if (!verificationCode.trim()) {
      toast.error("Please enter the verification code");
      return;
    }

    setIsVerifyingCode(true);
    try {
      const response = await fetch('/api/sms/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: phone,
          code: verificationCode
        }),
      });

      const data = await response.json();

      if (response.ok && data.verified) {
        toast.success("Phone number verified successfully");
        setIsPhoneVerified(true);
        setCurrentStep(3);
      } else {
        toast.error(data.message || "Invalid verification code");
      }
    } catch {
      toast.error("Failed to verify code");
    } finally {
      setIsVerifyingCode(false);
    }
  };

  const startResendTimer = () => {
    setCanResendCode(false);
    setResendTimer(60);
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          setCanResendCode(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Player selection handlers
  const onRemovePlayer = (player: PlayerInfo | null = selectedPlayer) => {
    if (!player) return;
    const position = handleRemove(player);
    if (position) setFilterPosition(position);
    setSelectedPlayer(null);
  };

  const onAddPlayer = (player: PlayerInfo) => {
    if (!player || !selectedPlayer || !removedPlayers.length) return;
    const success = handleAdd(player);
    if (success) setSelectedPlayer(null);
  };

  const onRestorePlayer = (player: PlayerInfo) => {
    if (!player) return;
    handleRestore(player);
    setSelectedPlayer(null);
  };

  const handleNextStep = () => {
    if (currentStep === 1) {
      // Send verification code instead of going to next step
      sendVerificationCode();
      return;
    }
    if (currentStep === 2) {
      // Verify code instead of going to next step
      verifyCode();
      return;
    }
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isSaveEnabled = useMemo(() => {
    if (currentStep === 1) {
      return !!phone.trim() && !isSendingCode;
    }
    if (currentStep === 2) {
      return !!verificationCode.trim() && !isVerifyingCode;
    }

    if (currentStep === 3) {
      return (
        starters.length === 11 &&
        subs.length === 4 &&
        removedPlayers.length === 0
      );
    }
    if (currentStep === 4) {
      return !!captainId && !!viceCaptainId;
    }
    if (currentStep === 5) {
      return !!fullName && !!favoriteClub;
    }

    return false;
  }, [
    currentStep,
    phone,
    fullName,
    favoriteClub,
    starters,
    subs,
    removedPlayers,
    captainId,
    viceCaptainId,
    verificationCode,
    isSendingCode,
    isVerifyingCode,
  ]);

  const handleSubmit = async () => {
    if (!user || !isPhoneVerified) {
      toast.error("Please complete phone verification first");
      return;
    }

    setIsSubmitting(true);

    try {
      // First, create the user profile
      const userResponse = await fetch("/api/user", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: user.id,
          email: user.emailAddresses[0]?.emailAddress,
          name: fullName,
          phone: `+216${phone.replace(/\s/g, '')}`, // Store with country code
          country,
          favoriteClub,
          isPhoneVerified: true,
        }),
      });

      if (!userResponse.ok) {
        throw new Error("Failed to add user profile");
      }

      // Then, save the team if there are any changes
      if (hasChanges && (starters.length === 11 || subs.length === 4)) {
        await saveTeam(user.id, activeGameweek?.gameWeek.id, true);
      }
      toast.success("Profile and team setup completed successfully!");
      router.push(`/points/${user.id}/round/${activeGameweek?.gameWeek.id}`);
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to complete setup. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isLoaded || !user) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div
      className={`mx-auto p-4 sm:p-6 lg:p-8 space-y-6 ${
        currentStep === 3 || currentStep === 4 ? "max-w-7xl w-full" : "max-w-md"
      }`}
    >
      <h1 className="text-2xl font-bold text-center">Welcome to TFL!</h1>
      <div className="flex justify-center mb-6">
        <div className="flex items-center space-x-2">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  currentStep === step
                    ? "bg-primary text-primary-foreground"
                    : currentStep > step
                    ? "bg-primary/20 text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                {currentStep > step ? <Check className="w-4 h-4" /> : step}
              </div>
              {step < 4 && (
                <div
                  className={`w-10 h-1 ${
                    currentStep > step ? "bg-primary/50" : "bg-muted"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {currentStep === 1 && "Contact Information"}
            {currentStep === 2 && "Verify Your Number"}
            {currentStep === 3 && "Choose Your Team"}
            {currentStep === 4 && "choose roles"}
            {currentStep === 5 && "Team Preference"}
          </CardTitle>
          <CardDescription>
            {currentStep === 1 &&
              "Add your phone number to complete your profile"}
              {currentStep === 2 &&
              "Verify your number to complete your profile"}
            {currentStep === 3 &&
              "Build your fantasy team by selecting players"}
            {currentStep === 4 &&
              "Choose your captain, vice-captain, and subs"}
            {currentStep === 5 &&
              "Select your favorite team and give your team a name"}
          </CardDescription>
        </CardHeader>

        <CardContent>
          {currentStep === 1 && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <Select value={country} onValueChange={setCountry} disabled>
                    <SelectTrigger id="country">
                      <SelectValue placeholder="Select your country" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Tunisia">Tunisia</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <div className="flex items-center space-x-2">
                  <Phone className="w-4 h-4 text-muted-foreground" />
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="XX XXX XXX"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                  />
                </div>
              </div>

              {/* Debug button - remove in production */}
              <div className="mt-4">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={testTwilioConfig}
                  className="text-xs"
                >
                  🔧 Test SMS Configuration
                </Button>
              </div>
            </div>
          )}
          {currentStep === 2 && (
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground mb-4">
                We sent a verification code to +216 {phone}
              </div>
              <div className="space-y-2">
                <Label htmlFor="verificationCode">Verification Code</Label>
                <div className="flex items-center space-x-2">
                  <Lock className="w-4 h-4 text-muted-foreground" />
                  <Input
                    id="verificationCode"
                    type="text"
                    placeholder="Enter 6-digit code"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    maxLength={6}
                  />
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Didn&apos;t receive the code?
                </span>
                <Button
                  variant="link"
                  size="sm"
                  onClick={sendVerificationCode}
                  disabled={!canResendCode || isSendingCode}
                  className="p-0 h-auto"
                >
                  {!canResendCode && resendTimer > 0
                    ? `Resend in ${resendTimer}s`
                    : isSendingCode
                    ? "Sending..."
                    : "Resend Code"}
                </Button>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-4 w-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Your Team</h3>
                <TeamViewToggle view={view} setView={setView} />
              </div>

              <div className="flex flex-col xl:flex-row gap-6 w-full">
                <div className="w-full xl:w-2/3 min-w-0">
                  {view === "list" ? (
                    <TeamTable
                      players={[...starters, ...subs]}
                      onSelect={setSelectedPlayer}
                    />
                  ) : (
                    <div className="w-full flex justify-center">
                      <PitchView
                        round={activeGameweek?.gameWeek.id || ""}
                        starters={[...starters, ...subs]}
                        removedPlayers={removedPlayers}
                        onSelect={setSelectedPlayer}
                        onRemove={onRemovePlayer}
                        showPrice
                        isBuildingTeam
                      />
                    </div>
                  )}
                </div>

                <div className="w-full xl:w-1/3 min-w-0">
                  <TransferFilters
                    allPlayers={filteredMarket}
                    selectedPosition={filterPosition}
                    selectedTeam={filterTeam}
                    selectedPrice={filterPrice}
                    maxPossiblePrice={priceRangeMax}
                    availableTeams={availableTeams}
                    onPositionChange={setFilterPosition}
                    onTeamChange={setFilterTeam}
                    onPriceChange={setFilterPrice}
                  />
                  <div className="mt-4 max-h-96 overflow-y-auto">
                    <TeamTable
                      players={sortPlayers(filteredMarket, "price")}
                      onSelect={setSelectedPlayer}
                    />
                  </div>
                </div>
              </div>

              <PlayerModal
                starters={starters}
                subs={subs}
                player={selectedPlayer}
                removedPlayers={removedPlayers}
                onClose={() => setSelectedPlayer(null)}
                isBuildingTeam
                isEditable
                onRemove={onRemovePlayer}
                onAdd={onAddPlayer}
                onRestore={onRestorePlayer}
              />
            </div>
          )}
          {currentStep === 4 && (
            <div className="space-y-4 w-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Your Team</h3>
                <TeamViewToggle view={view} setView={setView} />
              </div>

              <div className="flex flex-col gap-6 w-full">
                <div className="w-full min-w-0">
                  {view === "list" ? (
                    <>
                      <h3 className="text-lg font-semibold">Starters</h3>
                      <TeamTable
                        players={starters}
                        onSelect={setSelectedPlayer}
                      />
                      <h3 className="text-lg font-semibold">Substitutes</h3>
                      <TeamTable players={subs} onSelect={setSelectedPlayer} />
                    </>
                  ) : (
                    <PitchView
                      round={activeGameweek?.gameWeek.id || ""}
                      starters={starters}
                      subs={subs}
                      removedPlayers={removedPlayers}
                      onSelect={setSelectedPlayer}
                      showRole
                    />
                  )}
                </div>
              </div>
              <PlayerModal
                starters={starters}
                subs={subs}
                player={selectedPlayer}
                removedPlayers={removedPlayers}
                onClose={() => setSelectedPlayer(null)}
                isEditable
                onMakeCaptain={handleCaptainChange}
                onMakeViceCaptain={handleViceCaptainChange}
                onSubstitute={handleSubstitute}
              />
            </div>
          )}

          {currentStep === 5 && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="fullName">Team Name</Label>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-muted-foreground" />
                  <Input
                    id="fullName"
                    type="text"
                    placeholder="Team name"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="team">Favorite Team</Label>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-muted-foreground" />
                  <Select value={favoriteClub} onValueChange={setFavoriteClub}>
                    <SelectTrigger id="team">
                      <SelectValue placeholder="Select your favorite team" />
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((team: Team) => (
                        <SelectItem key={team.id} value={team.id}>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={team.logo} />
                              <AvatarFallback>{team.abbr}</AvatarFallback>
                            </Avatar>
                            <span>{team.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex justify-between">
          {currentStep > 1 ? (
            <Button variant="outline" onClick={handlePrevStep}>
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
          ) : (
            <div></div>
          )}

          {currentStep < 5 ? (
            <Button onClick={handleNextStep} disabled={!isSaveEnabled}>
              {currentStep === 1 && (isSendingCode ? "Sending..." : "Send Code")}
              {currentStep === 2 && (isVerifyingCode ? "Verifying..." : "Verify Code")}
              {currentStep > 2 && (
                <>
                  Next <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={!isSaveEnabled}>
              {isSubmitting ? "Saving..." : "Complete Setup"}
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}

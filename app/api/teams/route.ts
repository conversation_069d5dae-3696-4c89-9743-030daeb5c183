// app/api/teams/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function GET() {
  try {
    const teams = await prisma.team.findMany({
      include: {
        players: {
          include: {
            gwStats: true,
            totalStats: true,
          },
        },
        homeFixtures: {
          include: {
            teamA: true,
            teamH: true,
          },
        },
        awayFixtures: {
          include: {
            teamA: true,
            teamH: true,
          },
        },
      },
    });
    return NextResponse.json(teams);
  } catch (error) {
    console.error("Error fetching teams:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    // Check authentication and admin status
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { name, abbr, logo, jersey } = body;

    // Validate required fields
    if (!name || !abbr || !logo || !jersey) {
      return NextResponse.json(
        { error: "Missing required fields: name, abbr, logo, jersey" },
        { status: 400 }
      );
    }

    // Check if team with same name or abbreviation already exists
    const existingTeam = await prisma.team.findFirst({
      where: {
        OR: [{ name }, { abbr }],
      },
    });

    if (existingTeam) {
      return NextResponse.json(
        { error: "Team with this name or abbreviation already exists" },
        { status: 409 }
      );
    }

    const newTeam = await prisma.team.create({
      data: {
        name,
        abbr,
        logo,
        jersey,
      },
      include: {
        players: true,
      },
    });
    return NextResponse.json(newTeam, { status: 201 });
  } catch (error) {
    console.error("Error creating team:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

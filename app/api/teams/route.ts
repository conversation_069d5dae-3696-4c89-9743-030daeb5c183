// app/api/teams/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const teams = await prisma.team.findMany({
      include: {
        players: {
          include: {
            gwStats: true,
            totalStats: true,
          },
        },
        homeFixtures: {
          include: {
            teamA: true,
            teamH: true,
          },
        },
        awayFixtures: {
          include: {
            teamA: true,
            teamH: true,
          },
        },
      },
    });
    return NextResponse.json(teams);
  } catch (error) {
    console.error("Error fetching teams:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const body = await req.json();
    const { name, abbr, logo, jersey } = body;

    // Validate required fields
    if (!name || !abbr || !logo || !jersey) {
      return NextResponse.json(
        { error: "Missing required fields: name, abbr, logo, jersey" },
        { status: 400 }
      );
    }

    // Check if team with same name or abbreviation already exists
    const existingTeam = await prisma.team.findFirst({
      where: {
        OR: [{ name }, { abbr }],
      },
    });

    if (existingTeam) {
      return NextResponse.json(
        { error: "Team with this name or abbreviation already exists" },
        { status: 409 }
      );
    }

    const newTeam = await prisma.team.create({
      data: {
        name,
        abbr,
        logo,
        jersey,
      },
      include: {
        players: true,
      },
    });
    return NextResponse.json(newTeam, { status: 201 });
  } catch (error) {
    console.error("Error creating team:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

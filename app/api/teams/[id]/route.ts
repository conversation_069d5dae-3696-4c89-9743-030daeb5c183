// app/api/teams/[id]/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const team = await prisma.team.findUnique({
      where: { id },
      include: {
        players: {
          include: {
            gwStats: true,
            totalStats: true,
          },
        },
        homeFixtures: {
          include: {
            teamA: true,
            teamH: true,
            stats: true,
          },
        },
        awayFixtures: {
          include: {
            teamA: true,
            teamH: true,
            stats: true,
          },
        },
      },
    });

    if (!team) {
      return NextResponse.json({ error: "Team not found" }, { status: 404 });
    }

    return NextResponse.json(team);
  } catch (error) {
    console.error("Error fetching team:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and admin status
    const { id } = await params;
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { name, abbr, logo } = body;

    // Check if team exists
    const existingTeam = await prisma.team.findUnique({
      where: { id },
    });

    if (!existingTeam) {
      return NextResponse.json({ error: "Team not found" }, { status: 404 });
    }

    // Check for conflicts with other teams
    if (name || abbr) {
      const conflictingTeam = await prisma.team.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [...(name ? [{ name }] : []), ...(abbr ? [{ abbr }] : [])],
            },
          ],
        },
      });

      if (conflictingTeam) {
        return NextResponse.json(
          { error: "Team with this name or abbreviation already exists" },
          { status: 409 }
        );
      }
    }

    const updatedTeam = await prisma.team.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(abbr && { abbr }),
        ...(logo && { logo }),
      },
      include: {
        players: true,
      },
    });

    return NextResponse.json(updatedTeam);
  } catch (error) {
    console.error("Error updating team:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and admin status
    const { id } = await params;
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Check if team exists
    const existingTeam = await prisma.team.findUnique({
      where: { id },
      include: {
        players: true,
        homeFixtures: true,
        awayFixtures: true,
      },
    });

    if (!existingTeam) {
      return NextResponse.json({ error: "Team not found" }, { status: 404 });
    }

    // Check if team has associated data
    if (existingTeam.players.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete team with associated players" },
        { status: 409 }
      );
    }

    if (
      existingTeam.homeFixtures.length > 0 ||
      existingTeam.awayFixtures.length > 0
    ) {
      return NextResponse.json(
        { error: "Cannot delete team with associated fixtures" },
        { status: 409 }
      );
    }

    await prisma.team.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting team:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

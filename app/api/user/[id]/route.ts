// app/api/user/[id]/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { userId: currentUserId } = await auth();
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: currentUserId === id, // Only show email to the user themselves
        totalPoints: true,
        favoriteClub: true,
        createdAt: true,
        gwSheets: {
          select: {
            GW: true,
            totalPoints: true,
            createdAt: true,
          },
          orderBy: { GW: "desc" },
          take: 10,
        },
        leaguesAsMember: {
          select: {
            id: true,
            name: true,
          },
        },
        // Only show admin status and sensitive info to the user themselves or other admins
        ...(currentUserId === id && {
          phone: true,
          country: true,
          isAdmin: true,
          transfersLeft: true,
          moneyLeft: true,
        }),
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // If current user is admin, show additional info
    if (currentUserId && currentUserId !== id) {
      const currentUser = await prisma.user.findUnique({
        where: { id: currentUserId },
        select: { isAdmin: true },
      });

      if (currentUser?.isAdmin) {
        const adminUser = await prisma.user.findUnique({
          where: { id },
          include: {
            gwSheets: {
              orderBy: { GW: "desc" },
              take: 10,
            },
            leaguesAsAdmin: true,
            leaguesAsMember: true,
          },
        });
        return NextResponse.json(adminUser);
      }
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { userId: currentUserId } = await auth();
    // Users can only update their own profile, unless they're admin
    if (currentUserId !== id) {
      const currentUser = await prisma.user.findUnique({
        where: { id: currentUserId || "" },
        select: { isAdmin: true },
      });

      if (!currentUser?.isAdmin) {
        return NextResponse.json(
          { error: "Forbidden: You can only update your own profile" },
          { status: 403 }
        );
      }
    }

    const body = await req.json();
    const {
      name,
      phone,
      country,
      favoriteClub,
      totalPoints,
      transfersLeft,
      moneyLeft,
      isAdmin,
    } = body;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Only admins can update admin status and game-related fields
    const isCurrentUserAdmin =
      currentUserId &&
      (await prisma.user
        .findUnique({
          where: { id: currentUserId },
          select: { isAdmin: true },
        })
        .then((user) => user?.isAdmin));

    const updateData: {
      name?: string;
      phone?: string;
      country?: string;
      favoriteClub?: string;
      totalPoints?: number;
      transfersLeft?: number;
      moneyLeft?: number;
      isAdmin?: boolean;
    } = {};

    // Fields any user can update about themselves
    if (name !== undefined) updateData.name = name;
    if (phone !== undefined) updateData.phone = phone;
    if (country !== undefined) updateData.country = country;
    if (favoriteClub !== undefined) updateData.favoriteClub = favoriteClub;

    // Fields only admins can update
    if (isCurrentUserAdmin) {
      if (totalPoints !== undefined)
        updateData.totalPoints = parseInt(totalPoints);
      if (transfersLeft !== undefined)
        updateData.transfersLeft = parseInt(transfersLeft);
      if (moneyLeft !== undefined) updateData.moneyLeft = parseFloat(moneyLeft);
      if (isAdmin !== undefined) updateData.isAdmin = Boolean(isAdmin);
    }

    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        country: true,
        favoriteClub: true,
        totalPoints: true,
        transfersLeft: true,
        moneyLeft: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { userId: currentUserId } = await auth();

    // Only admins can delete users (except themselves)
    if (!currentUserId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: currentUserId },
      select: { isAdmin: true },
    });

    if (!currentUser?.isAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Prevent admin from deleting themselves
    if (currentUserId === id) {
      return NextResponse.json(
        { error: "Cannot delete your own account" },
        { status: 409 }
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
      include: {
        gwSheets: true,
        leaguesAsAdmin: true,
      },
    });

    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user is admin of any leagues
    if (existingUser.leaguesAsAdmin.length > 0) {
      return NextResponse.json(
        {
          error:
            "Cannot delete user who is admin of leagues. Transfer admin rights first.",
        },
        { status: 409 }
      );
    }

    // Delete user (cascade will handle related records)
    await prisma.user.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

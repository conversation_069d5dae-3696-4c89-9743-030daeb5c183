import { Webhook } from "svix";
import { headers } from "next/headers";
import { WebhookEvent } from "@clerk/nextjs/server";
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  // Get the headers
  const headerPayload = await headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error: Missing svix headers", {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your webhook secret
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET || "");

  let evt: WebhookEvent;

  // Verify the webhook
  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error("Error verifying webhook:", err);
    return new Response("Error verifying webhook", {
      status: 400,
    });
  }

  // Handle the webhook
  const eventType = evt.type;

  if (eventType === "user.created") {
    const { id, email_addresses, first_name, last_name, phone_numbers } =
      evt.data;

    // Extract the primary email
    const email = email_addresses?.[0]?.email_address;

    // Extract phone if available
    const phone = phone_numbers?.[0]?.phone_number;

    // Create a name from first and last name
    const name = [first_name, last_name].filter(Boolean).join(" ");

    try {
      // Create the user in your database
      const newUser = await prisma.user.create({
        data: {
          id,
          email: email || "",
          name: name || null,
          phone: phone || null,
          country: null,
          isAdmin: false,
          totalPoints: 0,
          transfersLeft: 2, // Default number of transfers
          moneyLeft: 100.0, // Default budget
        },
      });

      console.log(`User created: ${newUser.id}`);
      return NextResponse.json({ success: true, userId: newUser.id });
    } catch (error) {
      console.error("Error creating user:", error);
      return NextResponse.json(
        { error: "Error creating user in database" },
        { status: 500 }
      );
    }
  }
  if (eventType === "user.updated") {
    const { id, email_addresses, first_name, last_name, phone_numbers } =
      evt.data;

    // Extract the primary email
    const email = email_addresses?.[0]?.email_address;

    // Extract phone if available
    const phone = phone_numbers?.[0]?.phone_number;

    // Create a name from first and last name
    const name = [first_name, last_name].filter(Boolean).join(" ");

    try {
      // Update the user in your database
      const updatedUser = await prisma.user.update({
        where: { id },
        data: {
          email: email || "",
          name: name || null,
          phone: phone || null,
        },
      });

      console.log(`User updated: ${updatedUser.id}`);
      return NextResponse.json({ success: true, userId: updatedUser.id });
    } catch (error) {
      console.error("Error updating user:", error);
      return NextResponse.json(
        { error: "Error updating user in database" },
        { status: 500 }
      );
    }
  }
  if (eventType === "user.deleted") {
    const { id } = evt.data;

    try {
      // Delete the user from your database
      await prisma.user.delete({
        where: { id },
      });

      console.log(`User deleted: ${id}`);
      return NextResponse.json({ success: true });
    } catch (error) {
      console.error("Error deleting user:", error);
      return NextResponse.json(
        { error: "Error deleting user from database" },
        { status: 500 }
      );
    }
  }

  return NextResponse.json({ success: true });
}

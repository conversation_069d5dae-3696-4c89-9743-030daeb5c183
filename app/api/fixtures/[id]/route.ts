// app/api/fixtures/[id]/route.ts
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const fixture = await prisma.fixture.findUnique({
      where: { id },
      include: {
        teamH: true,
        teamA: true,
        stats: true,
      },
    });

    if (!fixture) {
      return NextResponse.json({ error: "Fixture not found" }, { status: 404 });
    }

    return NextResponse.json(fixture);
  } catch (error) {
    console.error("Error fetching fixture:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side
    const { id } = await params;

    // Check if fixture exists
    const existingFixture = await prisma.fixture.findUnique({
      where: { id },
    });

    if (!existingFixture) {
      return NextResponse.json({ error: "Fixture not found" }, { status: 404 });
    }

    const body = await req.json();
    const { round, kickoffTime, teamHId, teamAId, teamHScore, teamAScore } =
      body;

    // Validate teams if provided
    if (teamHId || teamAId) {
      const teamIds = [teamHId, teamAId].filter(Boolean);
      const teams = await prisma.team.findMany({
        where: { id: { in: teamIds } },
      });

      if (teams.length !== teamIds.length) {
        return NextResponse.json(
          { error: "One or more teams not found" },
          { status: 404 }
        );
      }
    }

    const updatedFixture = await prisma.fixture.update({
      where: { id },
      data: {
        ...(round !== undefined && { round: parseInt(round) }),
        ...(kickoffTime && { kickoffTime: new Date(kickoffTime) }),
        ...(teamHId && { teamHId }),
        ...(teamAId && { teamAId }),
        ...(teamHScore !== undefined && { teamHScore: parseInt(teamHScore) }),
        ...(teamAScore !== undefined && { teamAScore: parseInt(teamAScore) }),
      },
      include: {
        teamH: true,
        teamA: true,
        stats: true,
      },
    });

    return NextResponse.json(updatedFixture);
  } catch (error) {
    console.error("Error updating fixture:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side
    const { id } = await params;

    // Check if fixture exists
    const existingFixture = await prisma.fixture.findUnique({
      where: { id },
      include: {
        stats: true,
      },
    });

    if (!existingFixture) {
      return NextResponse.json({ error: "Fixture not found" }, { status: 404 });
    }

    // Delete associated stats first
    if (existingFixture.stats.length > 0) {
      await prisma.fixtureStat.deleteMany({
        where: { fixtureId: id },
      });
    }

    // Delete fixture
    await prisma.fixture.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting fixture:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

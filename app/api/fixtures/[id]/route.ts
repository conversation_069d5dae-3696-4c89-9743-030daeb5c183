// app/api/fixtures/[id]/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const fixture = await prisma.fixture.findUnique({
      where: { id: params.id },
      include: {
        teamH: true,
        teamA: true,
        stats: true,
      },
    });

    if (!fixture) {
      return NextResponse.json(
        { error: "Fixture not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(fixture);
  } catch (error) {
    console.error("Error fetching fixture:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and admin status
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Check if fixture exists
    const existingFixture = await prisma.fixture.findUnique({
      where: { id: params.id },
    });

    if (!existingFixture) {
      return NextResponse.json(
        { error: "Fixture not found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const { round, kickoffTime, teamHId, teamAId, teamHScore, teamAScore } = body;

    // Validate teams if provided
    if (teamHId || teamAId) {
      const teamIds = [teamHId, teamAId].filter(Boolean);
      const teams = await prisma.team.findMany({
        where: { id: { in: teamIds } },
      });

      if (teams.length !== teamIds.length) {
        return NextResponse.json(
          { error: "One or more teams not found" },
          { status: 404 }
        );
      }
    }

    const updatedFixture = await prisma.fixture.update({
      where: { id: params.id },
      data: {
        ...(round !== undefined && { round: parseInt(round) }),
        ...(kickoffTime && { kickoffTime: new Date(kickoffTime) }),
        ...(teamHId && { teamHId }),
        ...(teamAId && { teamAId }),
        ...(teamHScore !== undefined && { teamHScore: parseInt(teamHScore) }),
        ...(teamAScore !== undefined && { teamAScore: parseInt(teamAScore) }),
      },
      include: {
        teamH: true,
        teamA: true,
        stats: true,
      },
    });

    return NextResponse.json(updatedFixture);
  } catch (error) {
    console.error("Error updating fixture:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and admin status
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Check if fixture exists
    const existingFixture = await prisma.fixture.findUnique({
      where: { id: params.id },
      include: {
        stats: true,
      },
    });

    if (!existingFixture) {
      return NextResponse.json(
        { error: "Fixture not found" },
        { status: 404 }
      );
    }

    // Delete associated stats first
    if (existingFixture.stats.length > 0) {
      await prisma.fixtureStat.deleteMany({
        where: { fixtureId: params.id },
      });
    }

    // Delete fixture
    await prisma.fixture.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting fixture:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

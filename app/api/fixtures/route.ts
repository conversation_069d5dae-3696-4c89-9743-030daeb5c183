// app/api/fixtures/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const round = searchParams.get("round");
    const teamId = searchParams.get("teamId");

    const whereClause: Record<string, unknown> = {};

    if (round) {
      whereClause.round = parseInt(round);
    }

    if (teamId) {
      whereClause.OR = [{ teamHId: teamId }, { teamAId: teamId }];
    }

    const fixtures = await prisma.fixture.findMany({
      where: whereClause,
      include: {
        teamH: true,
        teamA: true,
        stats: true,
      },
      orderBy: [{ round: "asc" }, { kickoffTime: "asc" }],
    });

    return NextResponse.json(fixtures);
  } catch (error) {
    console.error("Error fetching fixtures:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const body = await req.json();
    const { round, kickoffTime, teamHId, teamAId, teamHScore, teamAScore } =
      body;

    // Validate required fields
    if (!round || !kickoffTime || !teamHId || !teamAId) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: round, kickoffTime, teamHId, teamAId",
        },
        { status: 400 }
      );
    }

    // Validate teams exist
    const [teamH, teamA] = await Promise.all([
      prisma.team.findUnique({ where: { id: teamHId } }),
      prisma.team.findUnique({ where: { id: teamAId } }),
    ]);

    if (!teamH || !teamA) {
      return NextResponse.json(
        { error: "One or both teams not found" },
        { status: 404 }
      );
    }

    // Check if fixture already exists for these teams in this round
    const existingFixture = await prisma.fixture.findFirst({
      where: {
        round: parseInt(round),
        OR: [
          { teamHId, teamAId },
          { teamHId: teamAId, teamAId: teamHId },
        ],
      },
    });

    if (existingFixture) {
      return NextResponse.json(
        { error: "Fixture already exists for these teams in this round" },
        { status: 409 }
      );
    }

    const newFixture = await prisma.fixture.create({
      data: {
        round: parseInt(round),
        kickoffTime: new Date(kickoffTime),
        teamHId,
        teamAId,
        ...(teamHScore !== undefined && { teamHScore: parseInt(teamHScore) }),
        ...(teamAScore !== undefined && { teamAScore: parseInt(teamAScore) }),
      },
      include: {
        teamH: true,
        teamA: true,
        stats: true,
      },
    });

    return NextResponse.json(newFixture, { status: 201 });
  } catch (error) {
    console.error("Error creating fixture:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

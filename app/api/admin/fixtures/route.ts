import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// GET /api/admin/fixtures - Get all fixtures with filtering
export async function GET(request: NextRequest) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const { searchParams } = new URL(request.url);
    const round = searchParams.get("round");
    const teamId = searchParams.get("teamId");
    const status = searchParams.get("status");
    const limit = searchParams.get("limit");

    // Build where clause
    const where: import("@prisma/client").Prisma.FixtureWhereInput = {};

    if (round && round !== "all") {
      where.round = parseInt(round);
    }

    if (teamId && teamId !== "all") {
      where.OR = [
        { teamHId: teamId },
        { teamAId: teamId },
      ];
    }

    if (status && status !== "all") {
      const now = new Date();
      if (status === "completed") {
        where.AND = [
          { teamHScore: { not: null } },
          { teamAScore: { not: null } },
        ];
      } else if (status === "live") {
        where.AND = [
          { kickoffTime: { lt: now } },
          {
            OR: [
              { teamHScore: null },
              { teamAScore: null },
            ],
          },
        ];
      } else if (status === "scheduled") {
        where.kickoffTime = { gt: now };
      }
    }

    const fixtures = await prisma.fixture.findMany({
      where,
      include: {
        teamH: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        teamA: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        stats: {
          select: {
            id: true,
            identifier: true,
            h: true,
            a: true,
          },
        },
      },
      orderBy: [
        { round: "asc" },
        { kickoffTime: "asc" },
      ],
      take: limit ? parseInt(limit) : undefined,
    });

    return NextResponse.json(fixtures);
  } catch (error) {
    console.error("Error fetching fixtures:", error);
    return NextResponse.json(
      { error: "Failed to fetch fixtures" },
      { status: 500 }
    );
  }
}

// POST /api/admin/fixtures - Create new fixture
export async function POST(request: NextRequest) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const body = await request.json();
    const { round, kickoffTime, teamHId, teamAId } = body;

    // Validate required fields
    if (!round || !kickoffTime || !teamHId || !teamAId) {
      return NextResponse.json(
        { error: "Round, kickoff time, and both teams are required" },
        { status: 400 }
      );
    }

    // Validate teams are different
    if (teamHId === teamAId) {
      return NextResponse.json(
        { error: "Home and away teams must be different" },
        { status: 400 }
      );
    }

    // Check if teams exist
    const [teamH, teamA] = await Promise.all([
      prisma.team.findUnique({ where: { id: teamHId } }),
      prisma.team.findUnique({ where: { id: teamAId } }),
    ]);

    if (!teamH || !teamA) {
      return NextResponse.json(
        { error: "One or both teams not found" },
        { status: 400 }
      );
    }

    // Check for duplicate fixture
    const existingFixture = await prisma.fixture.findFirst({
      where: {
        round: parseInt(round),
        teamHId,
        teamAId,
      },
    });

    if (existingFixture) {
      return NextResponse.json(
        { error: "Fixture between these teams in this round already exists" },
        { status: 400 }
      );
    }

    // Create fixture and PlayerGWStats in a transaction
    const fixture = await prisma.$transaction(async (tx) => {
      // Create the fixture
      const newFixture = await tx.fixture.create({
        data: {
          round: parseInt(round),
          kickoffTime: new Date(kickoffTime),
          teamHId,
          teamAId,
        },
        include: {
          teamH: {
            select: {
              id: true,
              name: true,
              abbr: true,
              logo: true,
            },
          },
          teamA: {
            select: {
              id: true,
              name: true,
              abbr: true,
              logo: true,
            },
          },
          stats: {
            select: {
              id: true,
              identifier: true,
              h: true,
              a: true,
            },
          },
        },
      });

      // Get all players from both teams
      const [homeTeamPlayers, awayTeamPlayers] = await Promise.all([
        tx.player.findMany({
          where: { teamId: teamHId },
          select: { id: true, currentPrice: true },
        }),
        tx.player.findMany({
          where: { teamId: teamAId },
          select: { id: true, currentPrice: true },
        }),
      ]);

      // Create PlayerGWStats for all players in both teams
      const allPlayers = [...homeTeamPlayers, ...awayTeamPlayers];
      const playerGWStatsData = allPlayers.map(({ id: playerId, currentPrice: price }) => ({
        playerId,
        GW: parseInt(round),
        price: typeof price === "number" ? price : Number(price),
        points: 0,
        transfersIn: 0,
        transfersOut: 0,
        owners: 0,
        minutesPlayed: 0,
        goalsScored: 0,
        assists: 0,
        cleanSheets: 0,
        goalsConceded: 0,
        ownGoals: 0,
        penaltiesSaved: 0,
        penaltiesMissed: 0,
        yellowCards: 0,
        redCards: 0,
        saves: 0,
        bonus: 0,
        bps: 0,
      }));

      // Create PlayerGWStats entries (skip if they already exist)
      for (const statsData of playerGWStatsData) {
        try {
          await tx.playerGWStats.create({
            data: statsData,
          });
        } catch {
          // Skip if already exists (unique constraint violation)
          console.log(`PlayerGWStats already exists for player ${statsData.playerId} in GW ${statsData.GW}`);
        }
      }

      return newFixture;
    });

    return NextResponse.json(fixture, { status: 201 });
  } catch (error) {
    console.error("Error creating fixture:", error);
    return NextResponse.json(
      { error: "Failed to create fixture" },
      { status: 500 }
    );
  }
}

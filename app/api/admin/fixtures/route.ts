import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/prisma";

// GET /api/admin/fixtures - Get all fixtures with filtering
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const round = searchParams.get("round");
    const teamId = searchParams.get("teamId");
    const status = searchParams.get("status");
    const limit = searchParams.get("limit");

    // Build where clause
    const where: any = {};

    if (round && round !== "all") {
      where.round = parseInt(round);
    }

    if (teamId && teamId !== "all") {
      where.OR = [
        { teamHId: teamId },
        { teamAId: teamId },
      ];
    }

    if (status && status !== "all") {
      const now = new Date();
      if (status === "completed") {
        where.AND = [
          { teamHScore: { not: null } },
          { teamAScore: { not: null } },
        ];
      } else if (status === "live") {
        where.AND = [
          { kickoffTime: { lt: now } },
          {
            OR: [
              { teamHScore: null },
              { teamAScore: null },
            ],
          },
        ];
      } else if (status === "scheduled") {
        where.kickoffTime = { gt: now };
      }
    }

    const fixtures = await prisma.fixture.findMany({
      where,
      include: {
        teamH: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        teamA: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        stats: {
          select: {
            id: true,
            identifier: true,
            h: true,
            a: true,
          },
        },
      },
      orderBy: [
        { round: "asc" },
        { kickoffTime: "asc" },
      ],
      take: limit ? parseInt(limit) : undefined,
    });

    return NextResponse.json(fixtures);
  } catch (error) {
    console.error("Error fetching fixtures:", error);
    return NextResponse.json(
      { error: "Failed to fetch fixtures" },
      { status: 500 }
    );
  }
}

// POST /api/admin/fixtures - Create new fixture
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const body = await request.json();
    const { round, kickoffTime, teamHId, teamAId } = body;

    // Validate required fields
    if (!round || !kickoffTime || !teamHId || !teamAId) {
      return NextResponse.json(
        { error: "Round, kickoff time, and both teams are required" },
        { status: 400 }
      );
    }

    // Validate teams are different
    if (teamHId === teamAId) {
      return NextResponse.json(
        { error: "Home and away teams must be different" },
        { status: 400 }
      );
    }

    // Check if teams exist
    const [teamH, teamA] = await Promise.all([
      prisma.team.findUnique({ where: { id: teamHId } }),
      prisma.team.findUnique({ where: { id: teamAId } }),
    ]);

    if (!teamH || !teamA) {
      return NextResponse.json(
        { error: "One or both teams not found" },
        { status: 400 }
      );
    }

    // Check for duplicate fixture
    const existingFixture = await prisma.fixture.findFirst({
      where: {
        round: parseInt(round),
        OR: [
          { AND: [{ teamHId }, { teamAId }] },
          { AND: [{ teamHId: teamAId }, { teamAId: teamHId }] },
        ],
      },
    });

    if (existingFixture) {
      return NextResponse.json(
        { error: "Fixture between these teams in this round already exists" },
        { status: 400 }
      );
    }

    const fixture = await prisma.fixture.create({
      data: {
        round: parseInt(round),
        kickoffTime: new Date(kickoffTime),
        teamHId,
        teamAId,
      },
      include: {
        teamH: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        teamA: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        stats: {
          select: {
            id: true,
            identifier: true,
            h: true,
            a: true,
          },
        },
      },
    });

    return NextResponse.json(fixture, { status: 201 });
  } catch (error) {
    console.error("Error creating fixture:", error);
    return NextResponse.json(
      { error: "Failed to create fixture" },
      { status: 500 }
    );
  }
}

// This would be a separate route at /api/admin/fixtures/bulk
// POST /api/admin/fixtures/bulk - Create multiple fixtures
/* export async function POST_BULK(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const body = await request.json();
    const { fixtures } = body;

    if (!Array.isArray(fixtures) || fixtures.length === 0) {
      return NextResponse.json(
        { error: "Fixtures array is required" },
        { status: 400 }
      );
    }

    // Validate each fixture
    for (const fixture of fixtures) {
      if (!fixture.round || !fixture.kickoffTime || !fixture.teamHId || !fixture.teamAId) {
        return NextResponse.json(
          { error: "All fixtures must have round, kickoff time, and both teams" },
          { status: 400 }
        );
      }

      if (fixture.teamHId === fixture.teamAId) {
        return NextResponse.json(
          { error: "Home and away teams must be different in all fixtures" },
          { status: 400 }
        );
      }
    }

    // Create fixtures in transaction
    const createdFixtures = await prisma.$transaction(
      fixtures.map((fixture: any) =>
        prisma.fixture.create({
          data: {
            round: parseInt(fixture.round),
            kickoffTime: new Date(fixture.kickoffTime),
            teamHId: fixture.teamHId,
            teamAId: fixture.teamAId,
          },
          include: {
            teamH: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
            teamA: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
          },
        })
      )
    );

    return NextResponse.json(createdFixtures, { status: 201 });
  } catch (error) {
    console.error("Error creating bulk fixtures:", error);
    return NextResponse.json(
      { error: "Failed to create fixtures" },
      { status: 500 }
    );
  }
} */

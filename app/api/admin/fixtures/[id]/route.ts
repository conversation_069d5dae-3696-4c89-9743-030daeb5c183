import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/prisma";

// GET /api/admin/fixtures/[id] - Get single fixture with details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { id } = await params;

    const fixture = await prisma.fixture.findUnique({
      where: { id },
      include: {
        teamH: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
            jersey: true,
          },
        },
        teamA: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
            jersey: true,
          },
        },
        stats: {
          select: {
            id: true,
            identifier: true,
            h: true,
            a: true,
          },
          orderBy: {
            identifier: "asc",
          },
        },
      },
    });

    if (!fixture) {
      return NextResponse.json({ error: "Fixture not found" }, { status: 404 });
    }

    return NextResponse.json(fixture);
  } catch (error) {
    console.error("Error fetching fixture:", error);
    return NextResponse.json(
      { error: "Failed to fetch fixture" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/fixtures/[id] - Update fixture
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { round, kickoffTime, teamHId, teamAId, teamHScore, teamAScore } = body;

    // Check if fixture exists
    const existingFixture = await prisma.fixture.findUnique({
      where: { id },
    });

    if (!existingFixture) {
      return NextResponse.json({ error: "Fixture not found" }, { status: 404 });
    }

    // Validate required fields
    if (!round || !kickoffTime || !teamHId || !teamAId) {
      return NextResponse.json(
        { error: "Round, kickoff time, and both teams are required" },
        { status: 400 }
      );
    }

    // Validate teams are different
    if (teamHId === teamAId) {
      return NextResponse.json(
        { error: "Home and away teams must be different" },
        { status: 400 }
      );
    }

    // Check if teams exist
    const [teamH, teamA] = await Promise.all([
      prisma.team.findUnique({ where: { id: teamHId } }),
      prisma.team.findUnique({ where: { id: teamAId } }),
    ]);

    if (!teamH || !teamA) {
      return NextResponse.json(
        { error: "One or both teams not found" },
        { status: 400 }
      );
    }

    // Check for duplicate fixture (excluding current fixture)
    const duplicateFixture = await prisma.fixture.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          { round: parseInt(round) },
          {
            OR: [
              { AND: [{ teamHId }, { teamAId }] },
              { AND: [{ teamHId: teamAId }, { teamAId: teamHId }] },
            ],
          },
        ],
      },
    });

    if (duplicateFixture) {
      return NextResponse.json(
        { error: "Another fixture between these teams in this round already exists" },
        { status: 400 }
      );
    }

    // Validate scores if provided
    if (teamHScore !== undefined && teamHScore !== null && teamHScore < 0) {
      return NextResponse.json(
        { error: "Home team score cannot be negative" },
        { status: 400 }
      );
    }

    if (teamAScore !== undefined && teamAScore !== null && teamAScore < 0) {
      return NextResponse.json(
        { error: "Away team score cannot be negative" },
        { status: 400 }
      );
    }

    const updatedFixture = await prisma.fixture.update({
      where: { id },
      data: {
        round: parseInt(round),
        kickoffTime: new Date(kickoffTime),
        teamHId,
        teamAId,
        teamHScore: teamHScore !== undefined ? (teamHScore === null ? null : parseInt(teamHScore)) : existingFixture.teamHScore,
        teamAScore: teamAScore !== undefined ? (teamAScore === null ? null : parseInt(teamAScore)) : existingFixture.teamAScore,
      },
      include: {
        teamH: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        teamA: {
          select: {
            id: true,
            name: true,
            abbr: true,
            logo: true,
          },
        },
        stats: {
          select: {
            id: true,
            identifier: true,
            h: true,
            a: true,
          },
        },
      },
    });

    return NextResponse.json(updatedFixture);
  } catch (error) {
    console.error("Error updating fixture:", error);
    return NextResponse.json(
      { error: "Failed to update fixture" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/fixtures/[id] - Delete fixture
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { id } = await params;

    // Check if fixture exists
    const fixture = await prisma.fixture.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            stats: true,
          },
        },
      },
    });

    if (!fixture) {
      return NextResponse.json({ error: "Fixture not found" }, { status: 404 });
    }

    // Delete fixture and related stats in transaction
    await prisma.$transaction([
      // Delete fixture stats first
      prisma.fixtureStat.deleteMany({
        where: { fixtureId: id },
      }),
      // Then delete fixture
      prisma.fixture.delete({
        where: { id },
      }),
    ]);

    return NextResponse.json({ message: "Fixture deleted successfully" });
  } catch (error) {
    console.error("Error deleting fixture:", error);
    return NextResponse.json(
      { error: "Failed to delete fixture" },
      { status: 500 }
    );
  }
}

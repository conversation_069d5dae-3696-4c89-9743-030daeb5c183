import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// POST /api/admin/fixtures/bulk - Create multiple fixtures
export async function POST(request: NextRequest) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const body = await request.json();
    const { fixtures } = body;

    if (!Array.isArray(fixtures) || fixtures.length === 0) {
      return NextResponse.json(
        { error: "Fixtures array is required" },
        { status: 400 }
      );
    }

    // Validate each fixture
    for (const fixture of fixtures) {
      if (!fixture.round || !fixture.kickoffTime || !fixture.teamHId || !fixture.teamAId) {
        return NextResponse.json(
          { error: "All fixtures must have round, kickoff time, and both teams" },
          { status: 400 }
        );
      }

      if (fixture.teamHId === fixture.teamAId) {
        return NextResponse.json(
          { error: "Home and away teams must be different in all fixtures" },
          { status: 400 }
        );
      }
    }

    // Get all teams to validate they exist
    const teamIds = [...new Set(fixtures.flatMap((f: any) => [f.teamHId, f.teamAId]))];
    const teams = await prisma.team.findMany({
      where: { id: { in: teamIds } },
      select: { id: true },
    });

    if (teams.length !== teamIds.length) {
      return NextResponse.json(
        { error: "One or more teams not found" },
        { status: 400 }
      );
    }

    // Create fixtures in transaction
    const createdFixtures = await prisma.$transaction(
      fixtures.map((fixture: any) =>
        prisma.fixture.create({
          data: {
            round: parseInt(fixture.round),
            kickoffTime: new Date(fixture.kickoffTime),
            teamHId: fixture.teamHId,
            teamAId: fixture.teamAId,
          },
          include: {
            teamH: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
            teamA: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
          },
        })
      )
    );

    return NextResponse.json(createdFixtures, { status: 201 });
  } catch (error) {
    console.error("Error creating bulk fixtures:", error);
    return NextResponse.json(
      { error: "Failed to create fixtures" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/fixtures/bulk - Delete multiple fixtures
export async function DELETE(request: NextRequest) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const body = await request.json();
    const { fixtureIds } = body;

    if (!Array.isArray(fixtureIds) || fixtureIds.length === 0) {
      return NextResponse.json(
        { error: "Fixture IDs array is required" },
        { status: 400 }
      );
    }

    // Delete fixtures and their stats in transaction
    await prisma.$transaction([
      // Delete fixture stats first
      prisma.fixtureStat.deleteMany({
        where: { fixtureId: { in: fixtureIds } },
      }),
      // Then delete fixtures
      prisma.fixture.deleteMany({
        where: { id: { in: fixtureIds } },
      }),
    ]);

    return NextResponse.json({ 
      message: `${fixtureIds.length} fixture(s) deleted successfully` 
    });
  } catch (error) {
    console.error("Error deleting bulk fixtures:", error);
    return NextResponse.json(
      { error: "Failed to delete fixtures" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/fixtures/bulk - Update multiple fixtures
export async function PUT(request: NextRequest) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const body = await request.json();
    const { fixtures } = body;

    if (!Array.isArray(fixtures) || fixtures.length === 0) {
      return NextResponse.json(
        { error: "Fixtures array is required" },
        { status: 400 }
      );
    }

    // Validate each fixture has an ID
    for (const fixture of fixtures) {
      if (!fixture.id) {
        return NextResponse.json(
          { error: "All fixtures must have an ID for bulk update" },
          { status: 400 }
        );
      }
    }

    // Update fixtures in transaction
    const updatedFixtures = await prisma.$transaction(
      fixtures.map((fixture: any) =>
        prisma.fixture.update({
          where: { id: fixture.id },
          data: {
            ...(fixture.round && { round: parseInt(fixture.round) }),
            ...(fixture.kickoffTime && { kickoffTime: new Date(fixture.kickoffTime) }),
            ...(fixture.teamHId && { teamHId: fixture.teamHId }),
            ...(fixture.teamAId && { teamAId: fixture.teamAId }),
            ...(fixture.teamHScore !== undefined && { 
              teamHScore: fixture.teamHScore === null ? null : parseInt(fixture.teamHScore) 
            }),
            ...(fixture.teamAScore !== undefined && { 
              teamAScore: fixture.teamAScore === null ? null : parseInt(fixture.teamAScore) 
            }),
          },
          include: {
            teamH: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
            teamA: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
          },
        })
      )
    );

    return NextResponse.json(updatedFixtures);
  } catch (error) {
    console.error("Error updating bulk fixtures:", error);
    return NextResponse.json(
      { error: "Failed to update fixtures" },
      { status: 500 }
    );
  }
}

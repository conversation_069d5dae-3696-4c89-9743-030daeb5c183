import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/prisma";

// POST /api/admin/fixtures/generate - Generate fixtures for a round-robin tournament
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const body = await request.json();
    const { 
      startRound = 1, 
      startDate, 
      matchesPerRound = 2, 
      daysBetweenRounds = 7,
      timeSlots = ["15:00", "17:30"],
      doubleRoundRobin = true 
    } = body;

    if (!startDate) {
      return NextResponse.json(
        { error: "Start date is required" },
        { status: 400 }
      );
    }

    // Get all teams
    const teams = await prisma.team.findMany({
      select: { id: true, name: true },
      orderBy: { name: "asc" },
    });

    if (teams.length < 2) {
      return NextResponse.json(
        { error: "At least 2 teams are required to generate fixtures" },
        { status: 400 }
      );
    }

    // Generate round-robin fixtures
    const fixtures: any[] = [];
    const numTeams = teams.length;
    const isOdd = numTeams % 2 === 1;
    
    // If odd number of teams, add a "bye" team
    const teamsForFixtures = isOdd ? [...teams, { id: "BYE", name: "BYE" }] : teams;
    const totalTeams = teamsForFixtures.length;
    const roundsInSingleRR = totalTeams - 1;
    const totalRounds = doubleRoundRobin ? roundsInSingleRR * 2 : roundsInSingleRR;

    let currentRound = startRound;
    let currentDate = new Date(startDate);

    for (let round = 0; round < totalRounds; round++) {
      const roundFixtures: any[] = [];
      
      // Generate fixtures for this round using round-robin algorithm
      for (let match = 0; match < totalTeams / 2; match++) {
        let home, away;
        
        if (match === 0) {
          // First match: team 0 vs team at position based on round
          home = 0;
          away = totalTeams - 1 - round % (totalTeams - 1);
        } else {
          // Other matches: calculate positions
          const pos1 = (match + round) % (totalTeams - 1);
          const pos2 = (totalTeams - 1 - match + round) % (totalTeams - 1);
          home = pos1 + 1;
          away = pos2 + 1;
        }

        // Ensure we don't have BYE teams in actual fixtures
        const homeTeam = teamsForFixtures[home];
        const awayTeam = teamsForFixtures[away];
        
        if (homeTeam.id !== "BYE" && awayTeam.id !== "BYE") {
          // For second half of double round-robin, swap home/away
          const isSecondHalf = doubleRoundRobin && round >= roundsInSingleRR;
          const finalHome = isSecondHalf ? awayTeam : homeTeam;
          const finalAway = isSecondHalf ? homeTeam : awayTeam;

          roundFixtures.push({
            teamHId: finalHome.id,
            teamAId: finalAway.id,
            round: currentRound,
          });
        }
      }

      // Assign dates and times to fixtures in this round
      let fixturesInRound = 0;
      for (const fixture of roundFixtures) {
        const timeSlotIndex = fixturesInRound % timeSlots.length;
        const daysOffset = Math.floor(fixturesInRound / timeSlots.length);
        
        const fixtureDate = new Date(currentDate);
        fixtureDate.setDate(fixtureDate.getDate() + daysOffset);
        
        const [hours, minutes] = timeSlots[timeSlotIndex].split(':');
        fixtureDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

        fixtures.push({
          ...fixture,
          kickoffTime: fixtureDate.toISOString(),
        });

        fixturesInRound++;
      }

      currentRound++;
      currentDate.setDate(currentDate.getDate() + daysBetweenRounds);
    }

    // Create fixtures in database
    const createdFixtures = await prisma.$transaction(
      fixtures.map((fixture) =>
        prisma.fixture.create({
          data: {
            round: fixture.round,
            kickoffTime: new Date(fixture.kickoffTime),
            teamHId: fixture.teamHId,
            teamAId: fixture.teamAId,
          },
          include: {
            teamH: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
            teamA: {
              select: {
                id: true,
                name: true,
                abbr: true,
                logo: true,
              },
            },
          },
        })
      )
    );

    return NextResponse.json({
      message: `Generated ${createdFixtures.length} fixtures for ${totalRounds} rounds`,
      fixtures: createdFixtures,
      summary: {
        totalFixtures: createdFixtures.length,
        totalRounds,
        teamsCount: teams.length,
        doubleRoundRobin,
        startRound,
        endRound: currentRound - 1,
      },
    }, { status: 201 });

  } catch (error) {
    console.error("Error generating fixtures:", error);
    return NextResponse.json(
      { error: "Failed to generate fixtures" },
      { status: 500 }
    );
  }
}

// GET /api/admin/fixtures/generate/preview - Preview fixtures without creating them
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const startRound = parseInt(searchParams.get("startRound") || "1");
    const startDate = searchParams.get("startDate");
    const matchesPerRound = parseInt(searchParams.get("matchesPerRound") || "2");
    const daysBetweenRounds = parseInt(searchParams.get("daysBetweenRounds") || "7");
    const timeSlots = searchParams.get("timeSlots")?.split(",") || ["15:00", "17:30"];
    const doubleRoundRobin = searchParams.get("doubleRoundRobin") === "true";

    if (!startDate) {
      return NextResponse.json(
        { error: "Start date is required" },
        { status: 400 }
      );
    }

    // Get all teams
    const teams = await prisma.team.findMany({
      select: { id: true, name: true, abbr: true },
      orderBy: { name: "asc" },
    });

    if (teams.length < 2) {
      return NextResponse.json(
        { error: "At least 2 teams are required" },
        { status: 400 }
      );
    }

    const numTeams = teams.length;
    const roundsInSingleRR = numTeams % 2 === 0 ? numTeams - 1 : numTeams;
    const totalRounds = doubleRoundRobin ? roundsInSingleRR * 2 : roundsInSingleRR;
    const fixturesPerRound = Math.floor(numTeams / 2);
    const totalFixtures = totalRounds * fixturesPerRound;

    return NextResponse.json({
      preview: {
        teamsCount: teams.length,
        totalRounds,
        fixturesPerRound,
        totalFixtures,
        doubleRoundRobin,
        startRound,
        endRound: startRound + totalRounds - 1,
        estimatedEndDate: new Date(
          new Date(startDate).getTime() + 
          (totalRounds - 1) * daysBetweenRounds * 24 * 60 * 60 * 1000
        ).toISOString().split('T')[0],
      },
      teams: teams.map(t => ({ id: t.id, name: t.name, abbr: t.abbr })),
    });

  } catch (error) {
    console.error("Error previewing fixtures:", error);
    return NextResponse.json(
      { error: "Failed to preview fixtures" },
      { status: 500 }
    );
  }
}

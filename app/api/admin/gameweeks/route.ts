import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const gameweeks = await prisma.gameWeek.findMany({
      orderBy: [
        { season: "desc" },
        { GW: "asc" }
      ]
    });

    return NextResponse.json(gameweeks);
  } catch (error) {
    console.error("Error fetching gameweeks:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    const body = await req.json();
    const { season, GW } = body;

    if (!season || GW === undefined) {
      return NextResponse.json(
        { error: "Missing required fields: season, GW" },
        { status: 400 }
      );
    }

    // Check if gameweek already exists
    const existingGW = await prisma.gameWeek.findFirst({
      where: {
        season,
        GW: parseInt(GW)
      }
    });

    if (existingGW) {
      return NextResponse.json(
        { error: "GameWeek already exists for this season" },
        { status: 409 }
      );
    }

    const gameweek = await prisma.gameWeek.create({
      data: {
        season,
        GW: parseInt(GW)
      }
    });

    return NextResponse.json(gameweek, { status: 201 });
  } catch (error) {
    console.error("Error creating gameweek:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

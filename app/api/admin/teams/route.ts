import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/prisma";

// GET /api/admin/teams - Get all teams with player counts
export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const teams = await prisma.team.findMany({
      include: {
        players: {
          select: {
            id: true,
            name: true,
            position: true,
            currentPrice: true,
          },
        },
        _count: {
          select: {
            players: true,
            homeFixtures: true,
            awayFixtures: true,
          },
        },
      },
      orderBy: { name: "asc" },
    });

    return NextResponse.json(teams);
  } catch (error) {
    console.error("Error fetching teams:", error);
    return NextResponse.json(
      { error: "Failed to fetch teams" },
      { status: 500 }
    );
  }
}

// POST /api/admin/teams - Create new team
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const body = await request.json();
    const { name, abbr, logo, jersey } = body;

    // Validate required fields
    if (!name || !abbr) {
      return NextResponse.json(
        { error: "Name and abbreviation are required" },
        { status: 400 }
      );
    }

    // Check if team with same name or abbreviation already exists
    const existingTeam = await prisma.team.findFirst({
      where: {
        OR: [
          { name: { equals: name, mode: "insensitive" } },
          { abbr: { equals: abbr, mode: "insensitive" } },
        ],
      },
    });

    if (existingTeam) {
      return NextResponse.json(
        { error: "Team with this name or abbreviation already exists" },
        { status: 400 }
      );
    }

    const team = await prisma.team.create({
      data: {
        name,
        abbr: abbr.toUpperCase(),
        logo: logo || "/logos/default.png",
        jersey: jersey || "/jerseys/default.png",
      },
      include: {
        players: {
          select: {
            id: true,
            name: true,
            position: true,
            currentPrice: true,
          },
        },
        _count: {
          select: {
            players: true,
            homeFixtures: true,
            awayFixtures: true,
          },
        },
      },
    });

    return NextResponse.json(team, { status: 201 });
  } catch (error) {
    console.error("Error creating team:", error);
    return NextResponse.json(
      { error: "Failed to create team" },
      { status: 500 }
    );
  }
}

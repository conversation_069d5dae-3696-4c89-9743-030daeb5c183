import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// GET /api/admin/teams - Get all teams with player counts
export async function GET() {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const teams = await prisma.team.findMany({
      include: {
        players: {
          select: {
            id: true,
            name: true,
            position: true,
            currentPrice: true,
          },
        },
        _count: {
          select: {
            players: true,
            homeFixtures: true,
            awayFixtures: true,
          },
        },
      },
      orderBy: { name: "asc" },
    });

    return NextResponse.json(teams);
  } catch (error) {
    console.error("Error fetching teams:", error);
    return NextResponse.json(
      { error: "Failed to fetch teams" },
      { status: 500 }
    );
  }
}

// POST /api/admin/teams - Create new team
export async function POST(request: NextRequest) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side

    const body = await request.json();
    const { name, abbr, logo, jersey } = body;

    // Validate required fields
    if (!name || !abbr) {
      return NextResponse.json(
        { error: "Name and abbreviation are required" },
        { status: 400 }
      );
    }

    // Check if team with same name or abbreviation already exists
    const existingTeam = await prisma.team.findFirst({
      where: {
        OR: [
          { name: { equals: name, mode: "insensitive" } },
          { abbr: { equals: abbr, mode: "insensitive" } },
        ],
      },
    });

    if (existingTeam) {
      return NextResponse.json(
        { error: "Team with this name or abbreviation already exists" },
        { status: 400 }
      );
    }

    const team = await prisma.team.create({
      data: {
        name,
        abbr: abbr.toUpperCase(),
        logo: logo || "/logos/default.png",
        jersey: jersey || "/jerseys/default.png",
      },
      include: {
        players: {
          select: {
            id: true,
            name: true,
            position: true,
            currentPrice: true,
          },
        },
        _count: {
          select: {
            players: true,
            homeFixtures: true,
            awayFixtures: true,
          },
        },
      },
    });

    return NextResponse.json(team, { status: 201 });
  } catch (error) {
    console.error("Error creating team:", error);
    return NextResponse.json(
      { error: "Failed to create team" },
      { status: 500 }
    );
  }
}

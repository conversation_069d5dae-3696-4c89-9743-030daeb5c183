import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const gameweeks = await prisma.gameWeek.findMany({
      orderBy: [
        { season: 'desc' },
        { GW: 'asc' }
      ]
    });
    return NextResponse.json(gameweeks);
  } catch (error) {
    console.error("Error fetching gameweeks:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

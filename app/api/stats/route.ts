// app/api/stats/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getActiveGameWeekNumber } from "@/lib/activeGameweek";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const type = searchParams.get("type");
    const gw = searchParams.get("gw");

    // If no GW is specified, use the active gameweek
    const activeGW = gw ? parseInt(gw) : await getActiveGameWeekNumber();

    switch (type) {
      case "overview":
        return getOverviewStats();
      case "players":
        return getPlayerStats(activeGW);
      case "teams":
        return getTeamStats();
      case "transfers":
        return getTransferStats(activeGW);
      case "dream-team":
        return getDreamTeam(activeGW);
      default:
        return getOverviewStats();
    }
  } catch (error) {
    console.error("Error fetching stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function getOverviewStats() {
  const [
    totalUsers,
    totalPlayers,
    totalTeams,
    totalFixtures,
    activeUsers,
  ] = await Promise.all([
    prisma.user.count(),
    prisma.player.count(),
    prisma.team.count(),
    prisma.fixture.count(),
    prisma.user.count({
      where: {
        gwSheets: {
          some: {},
        },
      },
    }),
  ]);

  return NextResponse.json({
    totalUsers,
    totalPlayers,
    totalTeams,
    totalFixtures,
    activeUsers,
  });
}

async function getPlayerStats(gw?: number | null) {
  const whereClause = gw ? { GW: gw } : {};

  const [
    topScorers,
    mostTransferredIn,
    mostTransferredOut,
    mostOwned,
  ] = await Promise.all([
    // Top scorers
    prisma.playerGWStats.findMany({
      where: whereClause,
      orderBy: { points: 'desc' },
      take: 10,
      include: {
        player: {
          include: { team: true },
        },
      },
    }),
    // Most transferred in
    prisma.playerGWStats.findMany({
      where: whereClause,
      orderBy: { transfersIn: 'desc' },
      take: 10,
      include: {
        player: {
          include: { team: true },
        },
      },
    }),
    // Most transferred out
    prisma.playerGWStats.findMany({
      where: whereClause,
      orderBy: { transfersOut: 'desc' },
      take: 10,
      include: {
        player: {
          include: { team: true },
        },
      },
    }),
    // Most owned
    prisma.playerGWStats.findMany({
      where: whereClause,
      orderBy: { owners: 'desc' },
      take: 10,
      include: {
        player: {
          include: { team: true },
        },
      },
    }),
  ]);

  return NextResponse.json({
    topScorers,
    mostTransferredIn,
    mostTransferredOut,
    mostOwned,
  });
}

async function getTeamStats() {
  const teams = await prisma.team.findMany({
    include: {
      players: {
        include: {
          totalStats: true,
        },
      },
      _count: {
        select: {
          players: true,
        },
      },
    },
  });

  const teamStats = teams.map(team => {
    const totalPoints = team.players.reduce(
      (sum, player) => sum + (player.totalStats?.points || 0),
      0
    );
    const avgPoints = team.players.length > 0 ? totalPoints / team.players.length : 0;

    return {
      id: team.id,
      name: team.name,
      abbr: team.abbr,
      logo: team.logo,
      playerCount: team._count.players,
      totalPoints,
      avgPoints: Math.round(avgPoints * 100) / 100,
    };
  });

  return NextResponse.json(teamStats.sort((a, b) => b.totalPoints - a.totalPoints));
}

async function getTransferStats(gw?: number | null) {
  const whereClause = gw ? { GW: gw } : {};

  const transferStats = await prisma.playerGWStats.aggregate({
    where: whereClause,
    _sum: {
      transfersIn: true,
      transfersOut: true,
    },
    _avg: {
      transfersIn: true,
      transfersOut: true,
    },
  });

  const topTransfers = await prisma.playerGWStats.findMany({
    where: whereClause,
    orderBy: [
      { transfersIn: 'desc' },
      { transfersOut: 'desc' },
    ],
    take: 20,
    include: {
      player: {
        include: { team: true },
      },
    },
  });

  return NextResponse.json({
    summary: {
      totalTransfersIn: transferStats._sum.transfersIn || 0,
      totalTransfersOut: transferStats._sum.transfersOut || 0,
      avgTransfersIn: Math.round((transferStats._avg.transfersIn || 0) * 100) / 100,
      avgTransfersOut: Math.round((transferStats._avg.transfersOut || 0) * 100) / 100,
    },
    topTransfers,
  });
}

async function getDreamTeam(gw?: number | null) {
  if (!gw) {
    return NextResponse.json(
      { error: "No active gameweek found for dream team" },
      { status: 400 }
    );
  }

  // Get top players by position for the gameweek
  const [gk, def, mid, att] = await Promise.all([
    // Goalkeeper (top 1)
    prisma.playerGWStats.findMany({
      where: {
        GW: gw,
        player: { position: 'GK' },
      },
      orderBy: { points: 'desc' },
      take: 1,
      include: {
        player: {
          include: { team: true },
        },
      },
    }),
    // Defenders (top 4)
    prisma.playerGWStats.findMany({
      where: {
        GW: gw,
        player: { position: 'DEF' },
      },
      orderBy: { points: 'desc' },
      take: 4,
      include: {
        player: {
          include: { team: true },
        },
      },
    }),
    // Midfielders (top 4)
    prisma.playerGWStats.findMany({
      where: {
        GW: gw,
        player: { position: 'MID' },
      },
      orderBy: { points: 'desc' },
      take: 4,
      include: {
        player: {
          include: { team: true },
        },
      },
    }),
    // Attackers (top 2)
    prisma.playerGWStats.findMany({
      where: {
        GW: gw,
        player: { position: 'ATK' },
      },
      orderBy: { points: 'desc' },
      take: 2,
      include: {
        player: {
          include: { team: true },
        },
      },
    }),
  ]);

  const dreamTeam = [...gk, ...def, ...mid, ...att];
  const totalPoints = dreamTeam.reduce((sum, player) => sum + player.points, 0);

  return NextResponse.json({
    gameweek: gw,
    players: dreamTeam,
    totalPoints,
    formation: "1-4-4-2",
  });
}

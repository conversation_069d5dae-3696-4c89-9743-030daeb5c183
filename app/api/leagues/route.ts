// app/api/leagues/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function GET() {
  try {
    const { userId } = await auth();
    
    // If user is authenticated, return leagues they're part of
    if (userId) {
      const leagues = await prisma.league.findMany({
        where: {
          OR: [
            { admins: { some: { id: userId } } },
            { members: { some: { id: userId } } },
          ],
        },
        include: {
          admins: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          members: {
            select: {
              id: true,
              name: true,
              email: true,
              totalPoints: true,
            },
          },
        },
      });
      return NextResponse.json(leagues);
    }

    // If not authenticated, return public leagues (if any)
    const publicLeagues = await prisma.league.findMany({
      include: {
        admins: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            members: true,
          },
        },
      },
    });

    return NextResponse.json(publicLeagues);
  } catch (error) {
    console.error("Error fetching leagues:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { name } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: "Missing required field: name" },
        { status: 400 }
      );
    }

    // Check if league with same name already exists
    const existingLeague = await prisma.league.findFirst({
      where: { name },
    });

    if (existingLeague) {
      return NextResponse.json(
        { error: "League with this name already exists" },
        { status: 409 }
      );
    }

    // Create league with the user as admin and member
    const newLeague = await prisma.league.create({
      data: {
        name,
        admins: {
          connect: { id: userId },
        },
        members: {
          connect: { id: userId },
        },
      },
      include: {
        admins: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            totalPoints: true,
          },
        },
      },
    });

    return NextResponse.json(newLeague, { status: 201 });
  } catch (error) {
    console.error("Error creating league:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/prisma";

// POST /api/leagues/[id]/leave - Leave a league
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    const { id: leagueId } = await params;
    
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if league exists
    const league = await prisma.league.findUnique({
      where: { id: leagueId },
      include: {
        admins: {
          select: { id: true }
        },
        members: {
          select: { id: true }
        }
      }
    });

    if (!league) {
      return NextResponse.json(
        { error: "League not found" },
        { status: 404 }
      );
    }

    // Check if user is a member
    const isMember = league.members.some(member => member.id === userId);
    
    if (!isMember) {
      return NextResponse.json(
        { error: "You are not a member of this league" },
        { status: 400 }
      );
    }

    // Check if user is the only admin
    const isAdmin = league.admins.some(admin => admin.id === userId);
    const isOnlyAdmin = isAdmin && league.admins.length === 1;
    
    if (isOnlyAdmin && league.members.length > 1) {
      return NextResponse.json(
        { error: "You cannot leave the league as the only admin. Please transfer admin rights first or delete the league." },
        { status: 400 }
      );
    }

    // If user is the only member and admin, delete the league
    if (league.members.length === 1 && isOnlyAdmin) {
      await prisma.league.delete({
        where: { id: leagueId }
      });
      
      return NextResponse.json({ message: "League deleted successfully" });
    }

    // Remove user from league (both as member and admin if applicable)
    await prisma.league.update({
      where: { id: leagueId },
      data: {
        members: {
          disconnect: { id: userId }
        },
        admins: {
          disconnect: { id: userId }
        }
      }
    });

    return NextResponse.json({ message: "Left league successfully" });
  } catch (error) {
    console.error("Error leaving league:", error);
    return NextResponse.json(
      { error: "Failed to leave league" },
      { status: 500 }
    );
  }
}

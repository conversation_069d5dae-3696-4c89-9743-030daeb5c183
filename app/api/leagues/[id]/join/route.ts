// app/api/leagues/[id]/join/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { id } = await params;
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if league exists
    const league = await prisma.league.findUnique({
      where: { id },
      include: {
        members: true,
      },
    });

    if (!league) {
      return NextResponse.json({ error: "League not found" }, { status: 404 });
    }

    // Check if user is already a member
    const isAlreadyMember = league.members.some(
      (member) => member.id === userId
    );
    if (isAlreadyMember) {
      return NextResponse.json(
        { error: "User is already a member of this league" },
        { status: 409 }
      );
    }

    // Add user to league
    const updatedLeague = await prisma.league.update({
      where: { id },
      data: {
        members: {
          connect: { id: userId },
        },
      },
      include: {
        admins: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            totalPoints: true,
          },
        },
      },
    });

    return NextResponse.json(updatedLeague);
  } catch (error) {
    console.error("Error joining league:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { id } = await params;
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if league exists
    const league = await prisma.league.findUnique({
      where: { id },
      include: {
        members: true,
        admins: true,
      },
    });

    if (!league) {
      return NextResponse.json({ error: "League not found" }, { status: 404 });
    }

    // Check if user is a member
    const isMember = league.members.some((member) => member.id === userId);
    if (!isMember) {
      return NextResponse.json(
        { error: "User is not a member of this league" },
        { status: 409 }
      );
    }

    // Check if user is the only admin
    const isAdmin = league.admins.some((admin) => admin.id === userId);
    if (isAdmin && league.admins.length === 1) {
      return NextResponse.json(
        {
          error:
            "Cannot leave league: You are the only admin. Transfer admin rights first or delete the league.",
        },
        { status: 409 }
      );
    }

    // Remove user from league
    const updatedLeague = await prisma.league.update({
      where: { id },
      data: {
        members: {
          disconnect: { id: userId },
        },
        // Also remove from admins if they are an admin
        ...(isAdmin && {
          admins: {
            disconnect: { id: userId },
          },
        }),
      },
      include: {
        admins: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            totalPoints: true,
          },
        },
      },
    });

    return NextResponse.json(updatedLeague);
  } catch (error) {
    console.error("Error leaving league:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

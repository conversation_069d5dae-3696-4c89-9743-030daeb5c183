import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/prisma";

// DELETE /api/leagues/[id]/members/[memberId] - Remove a member from league (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string, memberId: string }> }
) {
  try {
    const { userId } = await auth();
    const { id: leagueId, memberId } = await params;
    
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if league exists and user is admin
    const league = await prisma.league.findUnique({
      where: { id: leagueId },
      include: {
        admins: {
          select: { id: true }
        },
        members: {
          select: { id: true }
        }
      }
    });

    if (!league) {
      return NextResponse.json(
        { error: "League not found" },
        { status: 404 }
      );
    }

    const isAdmin = league.admins.some(admin => admin.id === userId);
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Only league admins can remove members" },
        { status: 403 }
      );
    }

    // Check if member exists in league
    const isMember = league.members.some(member => member.id === memberId);
    if (!isMember) {
      return NextResponse.json(
        { error: "User is not a member of this league" },
        { status: 400 }
      );
    }

    // Prevent admin from removing themselves if they're the only admin
    const memberIsAdmin = league.admins.some(admin => admin.id === memberId);
    const isOnlyAdmin = memberIsAdmin && league.admins.length === 1;
    
    if (isOnlyAdmin) {
      return NextResponse.json(
        { error: "Cannot remove the only admin. Transfer admin rights first or delete the league." },
        { status: 400 }
      );
    }

    // Remove member from league (both as member and admin if applicable)
    await prisma.league.update({
      where: { id: leagueId },
      data: {
        members: {
          disconnect: { id: memberId }
        },
        admins: {
          disconnect: { id: memberId }
        }
      }
    });

    return NextResponse.json({ message: "Member removed successfully" });
  } catch (error) {
    console.error("Error removing member:", error);
    return NextResponse.json(
      { error: "Failed to remove member" },
      { status: 500 }
    );
  }
}

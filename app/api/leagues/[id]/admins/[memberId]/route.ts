import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/prisma";

// POST /api/leagues/[id]/admins/[memberId] - Grant admin rights (admin only)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string, memberId: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: leagueId, memberId } = await params;

    // Check if league exists and user is admin
    const league = await prisma.league.findUnique({
      where: { id: leagueId },
      include: {
        admins: {
          select: { id: true }
        },
        members: {
          select: { id: true }
        }
      }
    });

    if (!league) {
      return NextResponse.json(
        { error: "League not found" },
        { status: 404 }
      );
    }

    const isAdmin = league.admins.some(admin => admin.id === userId);
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Only league admins can grant admin rights" },
        { status: 403 }
      );
    }

    // Check if user is a member
    const isMember = league.members.some(member => member.id === memberId);
    if (!isMember) {
      return NextResponse.json(
        { error: "User must be a member to become an admin" },
        { status: 400 }
      );
    }

    // Check if user is already an admin
    const isAlreadyAdmin = league.admins.some(admin => admin.id === memberId);
    if (isAlreadyAdmin) {
      return NextResponse.json(
        { error: "User is already an admin" },
        { status: 400 }
      );
    }

    // Grant admin rights
    const updatedLeague = await prisma.league.update({
      where: { id: leagueId },
      data: {
        admins: {
          connect: { id: memberId }
        }
      },
      include: {
        admins: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            totalPoints: true
          },
          orderBy: {
            totalPoints: 'desc'
          }
        },
        _count: {
          select: {
            members: true
          }
        }
      }
    });

    return NextResponse.json(updatedLeague);
  } catch (error) {
    console.error("Error granting admin rights:", error);
    return NextResponse.json(
      { error: "Failed to grant admin rights" },
      { status: 500 }
    );
  }
}

// DELETE /api/leagues/[id]/admins/[memberId] - Remove admin rights (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string, memberId: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: leagueId, memberId } = await params;

    // Check if league exists and user is admin
    const league = await prisma.league.findUnique({
      where: { id: leagueId },
      include: {
        admins: {
          select: { id: true }
        }
      }
    });

    if (!league) {
      return NextResponse.json(
        { error: "League not found" },
        { status: 404 }
      );
    }

    const isAdmin = league.admins.some(admin => admin.id === userId);
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Only league admins can remove admin rights" },
        { status: 403 }
      );
    }

    // Check if user is an admin
    const isTargetAdmin = league.admins.some(admin => admin.id === memberId);
    if (!isTargetAdmin) {
      return NextResponse.json(
        { error: "User is not an admin" },
        { status: 400 }
      );
    }

    // Prevent removing the last admin
    if (league.admins.length === 1) {
      return NextResponse.json(
        { error: "Cannot remove the only admin. Assign another admin first." },
        { status: 400 }
      );
    }

    // Remove admin rights
    const updatedLeague = await prisma.league.update({
      where: { id: leagueId },
      data: {
        admins: {
          disconnect: { id: memberId }
        }
      },
      include: {
        admins: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            totalPoints: true
          },
          orderBy: {
            totalPoints: 'desc'
          }
        },
        _count: {
          select: {
            members: true
          }
        }
      }
    });

    return NextResponse.json(updatedLeague);
  } catch (error) {
    console.error("Error removing admin rights:", error);
    return NextResponse.json(
      { error: "Failed to remove admin rights" },
      { status: 500 }
    );
  }
}

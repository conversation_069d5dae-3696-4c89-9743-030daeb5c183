// app/api/players/[id]/stats/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(req.url);
    const gw = searchParams.get("gw");

    // Check if player exists
    const player = await prisma.player.findUnique({
      where: { id: params.id },
    });

    if (!player) {
      return NextResponse.json(
        { error: "Player not found" },
        { status: 404 }
      );
    }

    if (gw) {
      // Get specific gameweek stats
      const gwStats = await prisma.playerGWStats.findUnique({
        where: {
          playerId_GW: {
            playerId: params.id,
            GW: parseInt(gw),
          },
        },
        include: {
          player: {
            include: {
              team: true,
            },
          },
        },
      });

      if (!gwStats) {
        return NextResponse.json(
          { error: "Stats not found for this gameweek" },
          { status: 404 }
        );
      }

      return NextResponse.json(gwStats);
    } else {
      // Get all stats for the player
      const [gwStats, totalStats] = await Promise.all([
        prisma.playerGWStats.findMany({
          where: { playerId: params.id },
          orderBy: { GW: 'asc' },
        }),
        prisma.playerTotalStats.findUnique({
          where: { playerId: params.id },
        }),
      ]);

      return NextResponse.json({
        player,
        gwStats,
        totalStats,
      });
    }
  } catch (error) {
    console.error("Error fetching player stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and admin status
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isAdmin: true },
    });

    if (!user?.isAdmin) {
      return NextResponse.json(
        { error: "Forbidden: Admin access required" },
        { status: 403 }
      );
    }

    // Check if player exists
    const player = await prisma.player.findUnique({
      where: { id: params.id },
    });

    if (!player) {
      return NextResponse.json(
        { error: "Player not found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const {
      GW,
      price,
      points,
      transfersIn,
      transfersOut,
      owners,
      minutesPlayed,
      goalsScored,
      assists,
      cleanSheets,
      goalsConceded,
      ownGoals,
      penaltiesSaved,
      penaltiesMissed,
      yellowCards,
      redCards,
      saves,
      bonus,
      bps,
    } = body;

    // Validate required fields
    if (GW === undefined || price === undefined) {
      return NextResponse.json(
        { error: "Missing required fields: GW, price" },
        { status: 400 }
      );
    }

    // Check if stats already exist for this GW
    const existingStats = await prisma.playerGWStats.findUnique({
      where: {
        playerId_GW: {
          playerId: params.id,
          GW: parseInt(GW),
        },
      },
    });

    if (existingStats) {
      return NextResponse.json(
        { error: "Stats already exist for this gameweek. Use PUT to update." },
        { status: 409 }
      );
    }

    const newStats = await prisma.playerGWStats.create({
      data: {
        playerId: params.id,
        GW: parseInt(GW),
        price: parseInt(price),
        points: parseInt(points) || 0,
        transfersIn: parseInt(transfersIn) || 0,
        transfersOut: parseInt(transfersOut) || 0,
        owners: parseInt(owners) || 0,
        minutesPlayed: parseInt(minutesPlayed) || 0,
        goalsScored: parseInt(goalsScored) || 0,
        assists: parseInt(assists) || 0,
        cleanSheets: parseInt(cleanSheets) || 0,
        goalsConceded: parseInt(goalsConceded) || 0,
        ownGoals: parseInt(ownGoals) || 0,
        penaltiesSaved: parseInt(penaltiesSaved) || 0,
        penaltiesMissed: parseInt(penaltiesMissed) || 0,
        yellowCards: parseInt(yellowCards) || 0,
        redCards: parseInt(redCards) || 0,
        saves: parseInt(saves) || 0,
        bonus: parseInt(bonus) || 0,
        bps: parseInt(bps) || 0,
      },
      include: {
        player: {
          include: {
            team: true,
          },
        },
      },
    });

    return NextResponse.json(newStats, { status: 201 });
  } catch (error) {
    console.error("Error creating player stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

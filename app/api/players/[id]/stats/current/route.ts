// app/api/players/[id]/stats/current/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getActiveGameWeekId } from "@/lib/activeGameweek";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Get the active gameweek ID
    const activeGW = await getActiveGameWeekId();
    
    if (!activeGW) {
      return NextResponse.json(
        { error: "No active gameweek found" },
        { status: 404 }
      );
    }

    // Check if player exists
    const player = await prisma.player.findUnique({
      where: { id },
    });

    if (!player) {
      return NextResponse.json({ error: "Player not found" }, { status: 404 });
    }

    // Get current gameweek stats
    const gwStats = await prisma.playerGWStats.findUnique({
      where: {
        playerId_GW: {
          playerId: id,
          GW: activeGW,
        },
      },
      include: {
        player: {
          include: {
            team: true,
          },
        },
      },
    });

    if (!gwStats) {
      return NextResponse.json(
        { error: "Stats not found for current gameweek" },
        { status: 404 }
      );
    }

    return NextResponse.json(gwStats);
  } catch (error) {
    console.error("Error fetching current player stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

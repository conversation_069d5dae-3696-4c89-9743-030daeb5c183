// app/api/players/[id]/stats/[gw]/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string; gw: string }> }
) {
  try {
    const { id, gw } = await params;

    // gw parameter is now a GameWeek ID, not an integer
    const gwStats = await prisma.playerGWStats.findUnique({
      where: {
        playerId_GW: {
          playerId: id,
          GW: gw, // gw is now the GameWeek ID
        },
      },
      include: {
        player: {
          include: {
            team: true,
          },
        },
      },
    });

    if (!gwStats) {
      return NextResponse.json(
        { error: "Stats not found for this player and gameweek" },
        { status: 404 }
      );
    }

    return NextResponse.json(gwStats);
  } catch (error) {
    console.error("Error fetching player GW stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: Request,
  { params }: { params: Promise<{ id: string; gw: string }> }
) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side
    const { id, gw } = await params;

    // Check if stats exist
    const existingStats = await prisma.playerGWStats.findUnique({
      where: {
        playerId_GW: {
          playerId: id,
          GW: gw, // gw is now the GameWeek ID
        },
      },
    });

    if (!existingStats) {
      return NextResponse.json(
        { error: "Stats not found for this player and gameweek" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const {
      price,
      points,
      transfersIn,
      transfersOut,
      owners,
      minutesPlayed,
      goalsScored,
      assists,
      cleanSheets,
      goalsConceded,
      ownGoals,
      penaltiesSaved,
      penaltiesMissed,
      yellowCards,
      redCards,
      saves,
      bonus,
      bps,
    } = body;

    const updatedStats = await prisma.playerGWStats.update({
      where: {
        playerId_GW: {
          playerId: id,
          GW: gw, // gw is now the GameWeek ID
        },
      },
      data: {
        ...(price !== undefined && { price: parseInt(price) }),
        ...(points !== undefined && { points: parseInt(points) }),
        ...(transfersIn !== undefined && {
          transfersIn: parseInt(transfersIn),
        }),
        ...(transfersOut !== undefined && {
          transfersOut: parseInt(transfersOut),
        }),
        ...(owners !== undefined && { owners: parseInt(owners) }),
        ...(minutesPlayed !== undefined && {
          minutesPlayed: parseInt(minutesPlayed),
        }),
        ...(goalsScored !== undefined && {
          goalsScored: parseInt(goalsScored),
        }),
        ...(assists !== undefined && { assists: parseInt(assists) }),
        ...(cleanSheets !== undefined && {
          cleanSheets: parseInt(cleanSheets),
        }),
        ...(goalsConceded !== undefined && {
          goalsConceded: parseInt(goalsConceded),
        }),
        ...(ownGoals !== undefined && { ownGoals: parseInt(ownGoals) }),
        ...(penaltiesSaved !== undefined && {
          penaltiesSaved: parseInt(penaltiesSaved),
        }),
        ...(penaltiesMissed !== undefined && {
          penaltiesMissed: parseInt(penaltiesMissed),
        }),
        ...(yellowCards !== undefined && {
          yellowCards: parseInt(yellowCards),
        }),
        ...(redCards !== undefined && { redCards: parseInt(redCards) }),
        ...(saves !== undefined && { saves: parseInt(saves) }),
        ...(bonus !== undefined && { bonus: parseInt(bonus) }),
        ...(bps !== undefined && { bps: parseInt(bps) }),
      },
      include: {
        player: {
          include: {
            team: true,
          },
        },
      },
    });

    return NextResponse.json(updatedStats);
  } catch (error) {
    console.error("Error updating player GW stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string; gw: string }> }
) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    // For now, we'll just allow all requests since auth is handled client-side
    const { id, gw } = await params;

    // Check if stats exist
    const existingStats = await prisma.playerGWStats.findUnique({
      where: {
        playerId_GW: {
          playerId: id,
          GW: gw, // gw is now the GameWeek ID
        },
      },
    });

    if (!existingStats) {
      return NextResponse.json(
        { error: "Stats not found for this player and gameweek" },
        { status: 404 }
      );
    }

    await prisma.playerGWStats.delete({
      where: {
        playerId_GW: {
          playerId: id,
          GW: gw, // gw is now the GameWeek ID
        },
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting player GW stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

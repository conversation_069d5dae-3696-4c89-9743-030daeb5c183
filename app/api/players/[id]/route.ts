// app/api/players/[id]/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const idsParam = searchParams.get("ids");

  if (!idsParam) {
    return NextResponse.json(
      { error: 'Missing "ids" query parameter' },
      { status: 400 }
    );
  }

  const ids = idsParam.split(",");

  const players = await prisma.player.findMany({
    where: {
      id: {
        in: ids,
      },
    },
  });

  return NextResponse.json(players);
}

export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  const body = await req.json();
  const updated = await prisma.player.update({
    where: { id: params.id },
    data: body,
  });
  return NextResponse.json(updated);
}

export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  await prisma.player.delete({ where: { id: params.id } });
  return NextResponse.json({ success: true });
}

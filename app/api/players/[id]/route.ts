// app/api/players/[id]/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const idsParam = searchParams.get("ids");

  if (!idsParam) {
    return NextResponse.json(
      { error: 'Missing "ids" query parameter' },
      { status: 400 }
    );
  }

  const ids = idsParam.split(",");

  const players = await prisma.player.findMany({
    where: {
      id: {
        in: ids,
      },
    },
  });

  return NextResponse.json(players);
}

export async function PUT(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const body = await req.json();
  const { id } = await params;
  const { name, teamId, position, currentPrice } = body;
  const updated = await prisma.player.update({
    where: { id },
    data: {
      name,
      teamId,
      position,
      currentPrice,
    },
    include: {
      team: true,
      totalStats: true,
      gwStats: true,
    },
  });
  console.log("body", body);
  return NextResponse.json(updated);
}

export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  await prisma.player.delete({ where: { id } });
  return NextResponse.json({ success: true });
}

import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const activeGameweek = await prisma.activeGameWeek.findFirst({
      include: {
        gameWeek: true
      }
    });
    
    if (!activeGameweek) {
      return NextResponse.json(
        { error: "No active gameweek found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json(activeGameweek);
  } catch (error) {
    console.error("Error fetching active gameweek:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const body = await req.json();
    const { gameweekId } = body;

    if (!gameweekId) {
      return NextResponse.json(
        { error: "Missing required fields: gameweekId" },
        { status: 400 }
      );
    }

    // Verify the gameweek exists
    const gameweek = await prisma.gameWeek.findUnique({
      where: { id: gameweekId }
    });

    if (!gameweek) {
      return NextResponse.json(
        { error: "Gameweek not found" },
        { status: 404 }
      );
    }

    // Use upsert to handle both create and update cases
    const activeGameweek = await prisma.activeGameWeek.upsert({
      where: { gameweekId },
      update: { gameweekId },
      create: { gameweekId },
      include: {
        gameWeek: true
      }
    });

    return NextResponse.json(activeGameweek);
  } catch (error) {
    console.error("Error updating active gameweek:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

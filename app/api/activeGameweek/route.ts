
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const activeGameweek = await prisma.activeGameweek.findFirst();

    return NextResponse.json(activeGameweek);
  } catch (error) {
    console.error("Error fetching active gameweek:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const body = await req.json();
    const { gameweekId } = body;

    if (!gameweekId) {
      return NextResponse.json(
        { error: "Missing required fields: gameweekId" },
        { status: 400 }
      );
    }

    const activeGameweek = await prisma.activeGameWeek.update({
      where: { id: 1 },
      update: { gameweekId },
    });

    return NextResponse.json(activeGameweek);
  } catch (error) {
    console.error("Error updating active gameweek:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

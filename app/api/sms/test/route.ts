import { NextResponse } from "next/server";
import twilio from "twilio";

export async function GET() {
  try {
    // Check environment variables
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const verifyServiceSid = process.env.TWILIO_VERIFY_SERVICE_SID;

    const envCheck = {
      TWILIO_ACCOUNT_SID: accountSid ? "✓ Set" : "✗ Missing",
      TWILIO_AUTH_TOKEN: authToken ? "✓ Set" : "✗ Missing", 
      TWILIO_VERIFY_SERVICE_SID: verifyServiceSid ? "✓ Set" : "✗ Missing",
    };

    if (!accountSid || !authToken || !verifyServiceSid) {
      return NextResponse.json({
        status: "error",
        message: "Missing Twilio environment variables",
        envCheck,
      });
    }

    // Test Twilio client initialization
    const client = twilio(accountSid, authToken);
    
    // Test API connection by fetching account info
    const account = await client.api.accounts(accountSid).fetch();
    
    // Test verify service
    const service = await client.verify.v2.services(verifyServiceSid).fetch();

    return NextResponse.json({
      status: "success",
      message: "Twilio configuration is working",
      envCheck,
      account: {
        sid: account.sid,
        friendlyName: account.friendlyName,
        status: account.status,
      },
      verifyService: {
        sid: service.sid,
        friendlyName: service.friendlyName,
      },
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    
    return NextResponse.json({
      status: "error",
      message: "Twilio configuration test failed",
      error: errorMessage,
    }, { status: 500 });
  }
}

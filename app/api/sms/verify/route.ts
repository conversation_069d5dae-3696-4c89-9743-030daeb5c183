import { NextRequest, NextResponse } from 'next/server';
import twilio from 'twilio';

const client = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber, code } = await request.json();

    if (!phoneNumber || !code) {
      return NextResponse.json(
        { error: 'Phone number and verification code are required' },
        { status: 400 }
      );
    }

    // Format phone number for Tunisia (+216)
    const formattedPhone = phoneNumber.startsWith('+216') 
      ? phoneNumber 
      : `+216${phoneNumber.replace(/\s/g, '')}`;

    // Verify the code using Twilio Verify service
    const verificationCheck = await client.verify.v2
      .services(process.env.TWILIO_VERIFY_SERVICE_SID!)
      .verificationChecks
      .create({
        to: formattedPhone,
        code: code
      });

    if (verificationCheck.status === 'approved') {
      return NextResponse.json({
        success: true,
        verified: true,
        message: 'Phone number verified successfully'
      });
    } else {
      return NextResponse.json({
        success: false,
        verified: false,
        message: 'Invalid verification code'
      }, { status: 400 });
    }

  } catch (error: unknown) {
    console.error('SMS verification error:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    return NextResponse.json(
      {
        error: 'Failed to verify code',
        details: errorMessage
      },
      { status: 500 }
    );
  }
}

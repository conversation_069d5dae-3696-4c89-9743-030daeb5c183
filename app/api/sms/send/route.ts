import { NextRequest, NextResponse } from "next/server";
import twilio from "twilio";

const client = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);
console.log("Twilio client created" + client  );

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber } = await request.json();
    console.log("Received phone number:", phoneNumber);

    if (!phoneNumber) {
      return NextResponse.json(
        { error: "Phone number is required" },
        { status: 400 }
      );
    }

    // Format phone number for Tunisia (+216)
    const formattedPhone = phoneNumber.startsWith("+216")
      ? phoneNumber
      : `+216${phoneNumber.replace(/\s/g, "")}`;

    console.log("Formatted phone number:", formattedPhone);

    // Send verification code using Twilio Verify service
    const verification = await client.verify.v2
      .services(process.env.TWILIO_VERIFY_SERVICE_SID!)
      .verifications.create({
        to: formattedPhone,
        channel: "sms",
      });

    return NextResponse.json({
      success: true,
      status: verification.status,
      message: "Verification code sent successfully",
    });
  } catch (error: unknown) {
    console.error("SMS send error:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      {
        error: "Failed to send verification code",
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from "next/server";
import twilio from "twilio";

// Check if environment variables are set
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const verifyServiceSid = process.env.TWILIO_VERIFY_SERVICE_SID;

console.log("Twilio Environment Variables Check:");
console.log("TWILIO_ACCOUNT_SID:", accountSid ? "✓ Set" : "✗ Missing");
console.log("TWILIO_AUTH_TOKEN:", authToken ? "✓ Set" : "✗ Missing");
console.log("TWILIO_VERIFY_SERVICE_SID:", verifyServiceSid ? "✓ Set" : "✗ Missing");

if (!accountSid || !authToken || !verifyServiceSid) {
  console.error("Missing required Twilio environment variables");
}

const client = accountSid && authToken ? twilio(accountSid, authToken) : null;

export async function POST(request: NextRequest) {
  try {
    // Check if <PERSON><PERSON><PERSON> is properly configured
    if (!client) {
      console.error("Twilio client not initialized - check environment variables");
      return NextResponse.json(
        {
          error: "SMS service not configured",
          details: "Missing Twilio environment variables"
        },
        { status: 500 }
      );
    }

    const { phoneNumber } = await request.json();
    console.log("Received phone number:", phoneNumber);

    if (!phoneNumber) {
      return NextResponse.json(
        { error: "Phone number is required" },
        { status: 400 }
      );
    }

    // Format phone number for Tunisia (+216)
    const formattedPhone = phoneNumber.startsWith("+216")
      ? phoneNumber
      : `+216${phoneNumber.replace(/\s/g, "")}`;

    console.log("Formatted phone number:", formattedPhone);

    // Send verification code using Twilio Verify service
    console.log("Attempting to send SMS via Twilio...");
    const verification = await client.verify.v2
      .services(verifyServiceSid!)
      .verifications.create({
        to: formattedPhone,
        channel: "sms",
      });

    console.log("Twilio verification response:", verification);

    return NextResponse.json({
      success: true,
      status: verification.status,
      message: "Verification code sent successfully",
    });
  } catch (error: unknown) {
    console.error("SMS send error:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      {
        error: "Failed to send verification code",
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}

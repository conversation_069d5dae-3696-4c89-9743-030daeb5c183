// app/api/userGWSheet/current/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { getActiveGameWeekId } from "@/lib/activeGameweek";

export async function GET() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the active gameweek ID
    const activeGWId = await getActiveGameWeekId();
    
    if (!activeGWId) {
      return NextResponse.json(
        { error: "No active gameweek found" },
        { status: 404 }
      );
    }

    const userGWSheet = await prisma.userGWSheet.findFirst({
      where: {
        userId,
        GW: activeGWId,
      },
    });

    if (!userGWSheet) {
      return NextResponse.json(
        { error: "GW Sheet not found for current gameweek" },
        { status: 404 }
      );
    }

    return NextResponse.json(userGWSheet);
  } catch (error) {
    console.error("Error fetching current GW sheet:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the active gameweek ID
    const activeGWId = await getActiveGameWeekId();
    
    if (!activeGWId) {
      return NextResponse.json(
        { error: "No active gameweek found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const { starters, subs, captainId, viceCaptainId } = body;

    // Validate input
    if (!starters || !Array.isArray(starters) || starters.length !== 11) {
      return NextResponse.json(
        { error: "Invalid starters data" },
        { status: 400 }
      );
    }

    if (!subs || !Array.isArray(subs) || subs.length !== 4) {
      return NextResponse.json(
        { error: "Invalid substitutes data" },
        { status: 400 }
      );
    }

    // Check if sheet already exists
    const existingSheet = await prisma.userGWSheet.findFirst({
      where: {
        userId,
        GW: activeGWId,
      },
    });

    if (existingSheet) {
      return NextResponse.json(
        { error: "Sheet already exists for current gameweek. Use PUT to update." },
        { status: 409 }
      );
    }

    // Create new sheet
    const newSheet = await prisma.userGWSheet.create({
      data: {
        userId,
        GW: activeGWId,
        starters,
        subs,
        captainId,
        viceCaptainId,
        totalPoints: 0, // TODO: Will be calculated later
      },
    });

    return NextResponse.json(newSheet, { status: 201 });
  } catch (error) {
    console.error("Error creating current GW sheet:", error);
    return NextResponse.json(
      { error: "Failed to create GW sheet" },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the active gameweek ID
    const activeGWId = await getActiveGameWeekId();
    
    if (!activeGWId) {
      return NextResponse.json(
        { error: "No active gameweek found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const { starters, subs, captainId, viceCaptainId } = body;

    // Validate input
    if (!starters || !Array.isArray(starters) || starters.length !== 11) {
      return NextResponse.json(
        { error: "Invalid starters data" },
        { status: 400 }
      );
    }

    if (!subs || !Array.isArray(subs) || subs.length !== 4) {
      return NextResponse.json(
        { error: "Invalid substitutes data" },
        { status: 400 }
      );
    }

    // Update sheet
    const updatedSheet = await prisma.userGWSheet.update({
      where: {
        userId_GW: {
          userId,
          GW: activeGWId,
        },
      },
      data: {
        starters,
        subs,
        captainId,
        viceCaptainId,
      },
    });

    return NextResponse.json(updatedSheet);
  } catch (error) {
    console.error("Error updating current GW sheet:", error);
    return NextResponse.json(
      { error: "Failed to update GW sheet" },
      { status: 500 }
    );
  }
}

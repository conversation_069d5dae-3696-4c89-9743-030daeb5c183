// app/api/userGWSheet/[userId]/[GW]/route.ts
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ userId: string; GW: string }> }
) {
  const { userId, GW } = await params;

  const userGWSheets = await prisma.userGWSheet.findFirst({
    where: {
      userId,
      GW,
    },
  });

  if (!userGWSheets) {
    return NextResponse.json({ error: "GW Sheet not found" }, { status: 404 });
  }

  return NextResponse.json(userGWSheets);
}

export async function POST(
  req: Request,
  { params }: { params: Promise<{ userId: string; GW: string }> }
) {
  // Check authentication
  const { userId: authUserId } = await auth();
  const { userId, GW } = await params;

  // Only allow users to create their own sheets
  if (authUserId !== userId) {
    return NextResponse.json(
      { error: "Unauthorized: You can only create your own sheets" },
      { status: 403 }
    );
  }

  try {
    const body = await req.json();
    const { starters, subs, captainId, viceCaptainId } = body;

    // Validate input
    if (!starters || !Array.isArray(starters) || starters.length !== 11) {
      return NextResponse.json(
        { error: "Invalid starters data" },
        { status: 400 }
      );
    }

    if (!subs || !Array.isArray(subs) || subs.length !== 4) {
      return NextResponse.json(
        { error: "Invalid substitutes data" },
        { status: 400 }
      );
    }

    // Check if sheet already exists
    const existingSheet = await prisma.userGWSheet.findFirst({
      where: {
        userId,
        GW,
      },
    });

    if (existingSheet) {
      return NextResponse.json(
        { error: "Sheet already exists. Use PUT to update." },
        { status: 409 }
      );
    }

    // Create new sheet
    const newSheet = await prisma.userGWSheet.create({
      data: {
        userId,
        GW,
        starters,
        subs,
        captainId,
        viceCaptainId,
        totalPoints: 0, // TODO: Will be calculated later
      },
    });

    return NextResponse.json(newSheet, { status: 201 });
  } catch (error) {
    console.error("Error creating GW sheet:", error);
    return NextResponse.json(
      { error: "Failed to create GW sheet" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: Request,
  { params }: { params: Promise<{ userId: string; GW: string }> }
) {
  // Check authentication
  const { userId: authUserId } = await auth();
  const { userId, GW } = await params;

  // Only allow users to update their own sheets
  if (authUserId !== userId) {
    return NextResponse.json(
      { error: "Unauthorized: You can only update your own sheets" },
      { status: 403 }
    );
  }

  try {
    const body = await req.json();
    const { starters, subs, captainId, viceCaptainId } = body;

    // Validate input
    if (!starters || !Array.isArray(starters) || starters.length !== 11) {
      return NextResponse.json(
        { error: "Invalid starters data" },
        { status: 400 }
      );
    }

    if (!subs || !Array.isArray(subs) || subs.length !== 4) {
      return NextResponse.json(
        { error: "Invalid substitutes data" },
        { status: 400 }
      );
    }

    // Update sheet
    const updatedSheet = await prisma.userGWSheet.update({
      where: {
        userId_GW: {
          userId,
          GW,
        },
      },
      data: {
        starters,
        subs,
        captainId,
        viceCaptainId,
      },
    });

    return NextResponse.json(updatedSheet);
  } catch (error) {
    console.error("Error updating GW sheet:", error);
    return NextResponse.json(
      { error: "Failed to update GW sheet" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ userId: string; GW: string }> }
) {
  // Check authentication
  const { userId: authUserId } = await auth();
  const { userId, GW } = await params;

  // TODO: dev
  // Only allow admins to delete sheets
  // Check if user is admin
  if (!authUserId) {
    return NextResponse.json(
      { error: "Unauthorized: Invalid user" },
      { status: 401 }
    );
  }

  const user = await prisma.user.findUnique({
    where: { id: authUserId },
    select: { isAdmin: true },
  });

  if (!user?.isAdmin) {
    return NextResponse.json(
      { error: "Unauthorized: You can only delete your own sheets" },
      { status: 403 }
    );
  }

  try {
    await prisma.userGWSheet.delete({
      where: {
        userId_GW: {
          userId,
          GW,
        },
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting GW sheet:", error);
    return NextResponse.json(
      { error: "Failed to delete GW sheet" },
      { status: 500 }
    );
  }
}

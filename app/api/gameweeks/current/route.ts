import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Get the current season's latest gameweek
    // This is a simple implementation - you might want to add more logic
    // to determine the "current" gameweek based on dates or other criteria
    const currentGameweek = await prisma.gameWeek.findFirst({
      orderBy: [
        { season: "desc" },
        { GW: "desc" }
      ]
    });

    if (!currentGameweek) {
      return NextResponse.json(
        { error: "No gameweeks found" },
        { status: 404 }
      );
    }

    return NextResponse.json(currentGameweek);
  } catch (error) {
    console.error("Error fetching current gameweek:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    // Simple admin check - in a real app you'd validate the admin token
    const body = await req.json();
    const { gameweekId } = body;

    if (!gameweekId) {
      return NextResponse.json(
        { error: "Missing required field: gameweekId" },
        { status: 400 }
      );
    }

    // Verify the gameweek exists
    const gameweek = await prisma.gameWeek.findUnique({
      where: { id: gameweekId }
    });

    if (!gameweek) {
      return NextResponse.json(
        { error: "GameWeek not found" },
        { status: 404 }
      );
    }

    // In a real app, you might store the current gameweek in a settings table
    // For now, we'll just return the gameweek as confirmation
    return NextResponse.json({
      message: "Current gameweek set successfully",
      gameweek
    });
  } catch (error) {
    console.error("Error setting current gameweek:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Save, User } from "lucide-react";
import Link from "next/link";
import { Team } from "@prisma/client";

export default function AddPlayer() {
  const router = useRouter();
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    teamId: "",
    position: "",
    currentPrice: "",
  });

  useEffect(() => {
    fetchTeams();
  }, []);

  const fetchTeams = async () => {
    try {
      const response = await fetch("/api/admin/teams");
      const data = await response.json();
      setTeams(data);
    } catch (error) {
      console.error("Error fetching teams:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch("/api/admin/players", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name,
          teamId: formData.teamId,
          position: formData.position,
          currentPrice: parseFloat(formData.currentPrice),
        }),
      });

      if (response.ok) {
        router.push("/admin/players");
      } else {
        const error = await response.json();
        alert(error.error || "Failed to create player");
      }
    } catch (error) {
      console.error("Error creating player:", error);
      alert("Error creating player");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/players">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Add New Player</h1>
          <p className="text-muted-foreground">
            Create a new player profile
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Player Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Player Name */}
              <div className="space-y-2">
                <Label htmlFor="name">Player Name *</Label>
                <Input
                  id="name"
                  placeholder="Enter player name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  required
                />
              </div>

              {/* Team Selection */}
              <div className="space-y-2">
                <Label htmlFor="team">Team *</Label>
                <Select
                  value={formData.teamId}
                  onValueChange={(value) => handleInputChange("teamId", value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a team" />
                  </SelectTrigger>
                  <SelectContent>
                    {teams.map((team) => (
                      <SelectItem key={team.id} value={team.id}>
                        <div className="flex items-center gap-2">
                          <span>{team.name}</span>
                          <span className="text-muted-foreground">({team.abbr})</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Position */}
              <div className="space-y-2">
                <Label htmlFor="position">Position *</Label>
                <Select
                  value={formData.position}
                  onValueChange={(value) => handleInputChange("position", value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select position" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GK">Goalkeeper (GK)</SelectItem>
                    <SelectItem value="DEF">Defender (DEF)</SelectItem>
                    <SelectItem value="MID">Midfielder (MID)</SelectItem>
                    <SelectItem value="ATK">Attacker (ATK)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Current Price */}
              <div className="space-y-2">
                <Label htmlFor="price">Current Price (Million) *</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.1"
                  min="0.1"
                  max="20"
                  placeholder="e.g., 5.5"
                  value={formData.currentPrice}
                  onChange={(e) => handleInputChange("currentPrice", e.target.value)}
                  required
                />
                <p className="text-sm text-muted-foreground">
                  Price in millions (e.g., 5.5 for 5.5M)
                </p>
              </div>

              {/* Submit Button */}
              <div className="flex gap-4 pt-4">
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Create Player
                </Button>
                <Link href="/admin/players">
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

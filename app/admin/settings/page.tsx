"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Settings,
  Calendar,
  Database,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle2,
  Clock,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { GameWeek } from "@prisma/client";


interface AppSettings {
  currentGameweekId: string | null;
  currentGameweek: GameWeek | null;
}

export default function AdminSettingsPage() {
  const [gameweeks, setGameweeks] = useState<GameWeek[]>([]);
  const [settings, setSettings] = useState<AppSettings>({
    currentGameweekId: null,
    currentGameweek: null,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch all gameweeks
      const gameweeksResponse = await fetch('/api/gameweeks');
      const gameweeksData = await gameweeksResponse.json();
      setGameweeks(gameweeksData);

      // Fetch current gameweek
      const activeGameweekResponse = await fetch('/api/activeGameweek');
      const activeGameweekData = await activeGameweekResponse.json();
      setSettings({
        currentGameweekId: activeGameweekData?.gameweekId || null,
        currentGameweek: activeGameweekData?.gameweek || null,
      });
    } catch (error) {
      console.error('Error fetching data:', error);
      setMessage({ type: 'error', text: 'Failed to load settings data' });
    } finally {
      setLoading(false);
    }
  };

  const handleGameweekChange = (gameweekId: string) => {
    const selectedGameweek = gameweeks.find(gw => gw.id === gameweekId);
    setSettings({
      currentGameweekId: gameweekId,
      currentGameweek: selectedGameweek || null,
    });
  };

  const saveSettings = async () => {
    if (!settings.currentGameweekId) {
      setMessage({ type: 'error', text: 'Please select a current gameweek' });
      return;
    }

    try {
      setSaving(true);
      
      // Update current gameweek
      const response = await fetch('/api/activeGameweek', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ gameweekId: settings.currentGameweekId }),
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Settings saved successfully!' });
        setTimeout(() => setMessage(null), 3000);
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      setMessage({ type: 'error', text: 'Failed to save settings' });
    } finally {
      setSaving(false);
    }
  };

  const createNewGameweek = async () => {
    const currentSeason = '2024-25';
    const nextGW = Math.max(...gameweeks.filter(gw => gw.season === currentSeason).map(gw => gw.GW)) + 1;
    
    if (nextGW > 30) {
      setMessage({ type: 'error', text: 'Cannot create more than 30 gameweeks per season' });
      return;
    }

    try {
      setSaving(true);
      
      const response = await fetch('/api/gameweeks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ season: currentSeason, GW: nextGW }),
      });

      if (response.ok) {
        setMessage({ type: 'success', text: `Gameweek ${nextGW} created successfully!` });
        fetchData(); // Refresh data
      } else {
        throw new Error('Failed to create gameweek');
      }
    } catch (error) {
      console.error('Error creating gameweek:', error);
      setMessage({ type: 'error', text: 'Failed to create new gameweek' });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const currentSeasonGameweeks = gameweeks.filter(gw => gw.season === '2024-25');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Settings className="h-8 w-8 text-primary" />
          Admin Settings
        </h1>
        <p className="text-muted-foreground">
          Manage application settings and current gameweek
        </p>
      </div>

      {/* Alert Messages */}
      {message && (
        <Alert className={message.type === 'error' ? 'border-destructive' : 'border-green-500'}>
          {message.type === 'error' ? (
            <AlertTriangle className="h-4 w-4" />
          ) : (
            <CheckCircle2 className="h-4 w-4" />
          )}
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Current Gameweek Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Current Gameweek Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="current-gameweek">Current Gameweek</Label>
              <Select
                value={settings.currentGameweekId || ""}
                onValueChange={handleGameweekChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select current gameweek" />
                </SelectTrigger>
                <SelectContent>
                  {currentSeasonGameweeks.map((gw) => (
                    <SelectItem key={gw.id} value={gw.id}>
                      Gameweek {gw.GW} - {gw.season}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Current Status</Label>
              <div className="flex items-center gap-2">
                {settings.currentGameweek ? (
                  <Badge variant="default" className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    GW {settings.currentGameweek.GW} - {settings.currentGameweek.season}
                  </Badge>
                ) : (
                  <Badge variant="secondary">No gameweek selected</Badge>
                )}
              </div>
            </div>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Gameweek Management</h3>
              <p className="text-sm text-muted-foreground">
                Total gameweeks for 2024-25: {currentSeasonGameweeks.length}/38
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={createNewGameweek}
                disabled={saving || currentSeasonGameweeks.length >= 38}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Create Next GW
              </Button>
              <Button onClick={saveSettings} disabled={saving}>
                {saving ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save Settings
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Database Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">{gameweeks.length}</div>
              <div className="text-sm text-muted-foreground">Total Gameweeks</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">16</div>
              <div className="text-sm text-muted-foreground">Teams</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">~400</div>
              <div className="text-sm text-muted-foreground">Players</div>
            </div>
          </div>

          <Alert>
            <Database className="h-4 w-4" />
            <AlertDescription>
              Use the migration script to reset and restore database with backup data.
              Run: <code className="bg-muted px-1 rounded">npm run migrate:restore</code>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}

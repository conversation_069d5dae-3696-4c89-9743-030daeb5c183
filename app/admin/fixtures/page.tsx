"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Filter,
  Calendar,
  Clock,
  PlayCircle,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";
import { Fixture, Team } from "@prisma/client";

interface FixtureWithTeams extends Fixture {
  teamH: Team;
  teamA: Team;
}

export default function FixturesManagement() {
  const [fixtures, setFixtures] = useState<FixtureWithTeams[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRound, setSelectedRound] = useState<string>("all");
  const [selectedTeam, setSelectedTeam] = useState<string>("all");

  useEffect(() => {
    fetchFixtures();
    fetchTeams();
  }, []);

  const fetchFixtures = async () => {
    try {
      const response = await fetch("/api/fixtures");
      const data = await response.json();
      setFixtures(data);
    } catch (error) {
      console.error("Error fetching fixtures:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTeams = async () => {
    try {
      const response = await fetch("/api/teams");
      const data = await response.json();
      setTeams(data);
    } catch (error) {
      console.error("Error fetching teams:", error);
    }
  };

  const handleDeleteFixture = async (fixtureId: string) => {
    if (!confirm("Are you sure you want to delete this fixture?")) return;

    try {
      const response = await fetch(`/api/fixtures/${fixtureId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setFixtures(fixtures.filter(f => f.id !== fixtureId));
      } else {
        alert("Failed to delete fixture");
      }
    } catch (error) {
      console.error("Error deleting fixture:", error);
      alert("Error deleting fixture");
    }
  };

  const filteredFixtures = fixtures.filter(fixture => {
    const matchesSearch = 
      fixture.teamH.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fixture.teamA.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRound = selectedRound === "all" || fixture.round.toString() === selectedRound;
    const matchesTeam = selectedTeam === "all" || 
                       fixture.teamHId === selectedTeam || 
                       fixture.teamAId === selectedTeam;
    
    return matchesSearch && matchesRound && matchesTeam;
  });

  const getFixtureStatus = (fixture: FixtureWithTeams) => {
    const now = new Date();
    const kickoff = new Date(fixture.kickoffTime);
    
    if (fixture.teamHScore !== null && fixture.teamAScore !== null) {
      return { status: "completed", label: "Completed", color: "bg-green-500" };
    } else if (kickoff < now) {
      return { status: "live", label: "Live", color: "bg-red-500" };
    } else {
      return { status: "scheduled", label: "Scheduled", color: "bg-blue-500" };
    }
  };

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    };
  };

  // Get unique rounds for filter
  const rounds = Array.from(new Set(fixtures.map(f => f.round))).sort((a, b) => a - b);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Fixture Management</h1>
          <p className="text-muted-foreground">
            Manage matches and input results
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/admin/match-events">
            <Button variant="outline">
              <PlayCircle className="h-4 w-4 mr-2" />
              Match Input
            </Button>
          </Link>
          <Link href="/admin/fixtures/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Fixture
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search fixtures..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedRound} onValueChange={setSelectedRound}>
              <SelectTrigger>
                <SelectValue placeholder="All Rounds" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Rounds</SelectItem>
                {rounds.map((round) => (
                  <SelectItem key={round} value={round.toString()}>
                    Round {round}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedTeam} onValueChange={setSelectedTeam}>
              <SelectTrigger>
                <SelectValue placeholder="All Teams" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Teams</SelectItem>
                {teams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {filteredFixtures.length} fixtures
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Fixtures Table */}
      <Card>
        <CardHeader>
          <CardTitle>Fixtures ({filteredFixtures.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Round</TableHead>
                  <TableHead>Match</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Score</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredFixtures.map((fixture) => {
                  const status = getFixtureStatus(fixture);
                  const dateTime = formatDateTime(fixture.kickoffTime);
                  
                  return (
                    <TableRow key={fixture.id}>
                      <TableCell>
                        <Badge variant="outline">
                          Round {fixture.round}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-4">
                          {/* Home Team */}
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={fixture.teamH.logo} />
                              <AvatarFallback>{fixture.teamH.abbr}</AvatarFallback>
                            </Avatar>
                            <span className="font-medium">{fixture.teamH.name}</span>
                          </div>
                          
                          <span className="text-muted-foreground">vs</span>
                          
                          {/* Away Team */}
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={fixture.teamA.logo} />
                              <AvatarFallback>{fixture.teamA.abbr}</AvatarFallback>
                            </Avatar>
                            <span className="font-medium">{fixture.teamA.name}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{dateTime.date}</div>
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {dateTime.time}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {fixture.teamHScore !== null && fixture.teamAScore !== null ? (
                          <div className="font-bold text-lg">
                            {fixture.teamHScore} - {fixture.teamAScore}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge className={`${status.color} text-white`}>
                          {status.label}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          {status.status !== "completed" && (
                            <Link href={`/admin/match-events/${fixture.id}`}>
                              <Button variant="outline" size="sm">
                                <PlayCircle className="h-4 w-4" />
                              </Button>
                            </Link>
                          )}
                          <Link href={`/admin/fixtures/${fixture.id}/edit`}>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteFixture(fixture.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  PlayCircle,
  Calendar,
  Clock,
  Users,
  Target,
} from "lucide-react";
import Link from "next/link";
import { Fixture, Team } from "@prisma/client";

interface FixtureWithTeams extends Fixture {
  teamH: Team;
  teamA: Team;
}

export default function MatchEventsPage() {
  const [fixtures, setFixtures] = useState<FixtureWithTeams[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRound, setSelectedRound] = useState<string>("all");

  useEffect(() => {
    fetchFixtures();
  }, []);

  const fetchFixtures = async () => {
    try {
      const response = await fetch("/api/fixtures");
      const data = await response.json();
      setFixtures(data);
    } catch (error) {
      console.error("Error fetching fixtures:", error);
    } finally {
      setLoading(false);
    }
  };

  const getFixtureStatus = (fixture: FixtureWithTeams) => {
    const now = new Date();
    const kickoff = new Date(fixture.kickoffTime);
    
    if (fixture.teamHScore !== null && fixture.teamAScore !== null) {
      return { status: "completed", label: "Completed", color: "bg-green-500", canEdit: true };
    } else if (kickoff < now) {
      return { status: "live", label: "Live", color: "bg-red-500", canEdit: true };
    } else {
      return { status: "scheduled", label: "Scheduled", color: "bg-blue-500", canEdit: false };
    }
  };

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    };
  };

  // Filter fixtures that can have match events input
  const availableFixtures = fixtures.filter(fixture => {
    const status = getFixtureStatus(fixture);
    const matchesRound = selectedRound === "all" || fixture.round.toString() === selectedRound;
    return status.canEdit && matchesRound;
  });

  // Get unique rounds for filter
  const rounds = Array.from(new Set(fixtures.map(f => f.round))).sort((a, b) => a - b);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Match Events Input</h1>
        <p className="text-muted-foreground">
          Input detailed match data including player minutes, goals, and assists
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{availableFixtures.length}</div>
                <div className="text-sm text-muted-foreground">Available Matches</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <PlayCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {fixtures.filter(f => getFixtureStatus(f).status === "completed").length}
                </div>
                <div className="text-sm text-muted-foreground">Completed</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
                <Clock className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {fixtures.filter(f => getFixtureStatus(f).status === "live").length}
                </div>
                <div className="text-sm text-muted-foreground">Live</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <Target className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {fixtures.filter(f => getFixtureStatus(f).status === "scheduled").length}
                </div>
                <div className="text-sm text-muted-foreground">Scheduled</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filter */}
      <Card>
        <CardHeader>
          <CardTitle>Select Match to Input Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <Select value={selectedRound} onValueChange={setSelectedRound}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Rounds" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Rounds</SelectItem>
                {rounds.map((round) => (
                  <SelectItem key={round} value={round.toString()}>
                    Round {round}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Badge variant="outline">
              {availableFixtures.length} matches available
            </Badge>
          </div>

          {/* Available Fixtures */}
          <div className="grid gap-4">
            {availableFixtures.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <PlayCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No matches available for data input</p>
                <p className="text-sm">Matches must be live or completed to input data</p>
              </div>
            ) : (
              availableFixtures.map((fixture) => {
                const status = getFixtureStatus(fixture);
                const dateTime = formatDateTime(fixture.kickoffTime);
                
                return (
                  <Card key={fixture.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-6">
                          {/* Round */}
                          <Badge variant="outline">
                            Round {fixture.round}
                          </Badge>
                          
                          {/* Match */}
                          <div className="flex items-center gap-4">
                            {/* Home Team */}
                            <div className="flex items-center gap-2">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={fixture.teamH.logo} />
                                <AvatarFallback>{fixture.teamH.abbr}</AvatarFallback>
                              </Avatar>
                              <span className="font-semibold">{fixture.teamH.name}</span>
                            </div>
                            
                            {/* Score or VS */}
                            <div className="text-center min-w-[60px]">
                              {fixture.teamHScore !== null && fixture.teamAScore !== null ? (
                                <div className="font-bold text-xl">
                                  {fixture.teamHScore} - {fixture.teamAScore}
                                </div>
                              ) : (
                                <span className="text-muted-foreground font-medium">VS</span>
                              )}
                            </div>
                            
                            {/* Away Team */}
                            <div className="flex items-center gap-2">
                              <span className="font-semibold">{fixture.teamA.name}</span>
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={fixture.teamA.logo} />
                                <AvatarFallback>{fixture.teamA.abbr}</AvatarFallback>
                              </Avatar>
                            </div>
                          </div>
                          
                          {/* Date & Time */}
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <div>
                              <div>{dateTime.date}</div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {dateTime.time}
                              </div>
                            </div>
                          </div>
                          
                          {/* Status */}
                          <Badge className={`${status.color} text-white`}>
                            {status.label}
                          </Badge>
                        </div>
                        
                        {/* Action Button */}
                        <Link href={`/admin/match-events/${fixture.id}`}>
                          <Button>
                            <Users className="h-4 w-4 mr-2" />
                            Input Data
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

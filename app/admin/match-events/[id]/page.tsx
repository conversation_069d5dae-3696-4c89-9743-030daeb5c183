"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  ArrowLeft,
  Save,
  Users,
  Clock,
  Plus,
  Minus,
  Calendar,
  Trophy,
} from "lucide-react";
import Link from "next/link";
import { Fixture, Team, Player } from "@prisma/client";
import { calculatePoints } from "@/lib/utils";
import { PlayerGWStats } from "@prisma/client";

interface FixtureWithTeams extends Fixture {
  teamH: Team;
  teamA: Team;
}

interface PlayerWithTeam extends Player {
  team: Team;
}

interface PlayerMatchData {
  playerId: string;
  minutesPlayed: number;
  goalsScored: number;
  assists: number;
  yellowCards: number;
  redCards: number;
  cleanSheets: number;
  goalsConceded: number;
  saves: number;
  penaltiesSaved: number;
  penaltiesMissed: number;
  ownGoals: number;
}

export default function MatchInputPage() {
  const params = useParams();
  const router = useRouter();
  const fixtureId = params.id as string;

  const [fixture, setFixture] = useState<FixtureWithTeams | null>(null);
  const [homeTeamPlayers, setHomeTeamPlayers] = useState<PlayerWithTeam[]>([]);
  const [awayTeamPlayers, setAwayTeamPlayers] = useState<PlayerWithTeam[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Match result
  const [homeScore, setHomeScore] = useState<number>(0);
  const [awayScore, setAwayScore] = useState<number>(0);

  // Player data for both teams - now includes existing PlayerGWStats data
  const [playerData, setPlayerData] = useState<Record<string, PlayerMatchData>>(
    {}
  );
  const [existingPlayerGWStats, setExistingPlayerGWStats] = useState<Record<string, PlayerGWStats>>(
    {}
  );

  useEffect(() => {
    const fetchFixtureData = async () => {
      try {
        // Fetch fixture details
        const fixtureResponse = await fetch(`/api/fixtures/${fixtureId}`);
        const fixtureData = await fixtureResponse.json();
        setFixture(fixtureData);

        // Set existing scores if available
        if (fixtureData.teamHScore !== null)
          setHomeScore(fixtureData.teamHScore);
        if (fixtureData.teamAScore !== null)
          setAwayScore(fixtureData.teamAScore);

        // Fetch players for both teams
        const [homePlayersResponse, awayPlayersResponse] = await Promise.all([
          fetch(`/api/players?teamId=${fixtureData.teamHId}`),
          fetch(`/api/players?teamId=${fixtureData.teamAId}`),
        ]);

        const homePlayers = await homePlayersResponse.json();
        const awayPlayers = await awayPlayersResponse.json();

        setHomeTeamPlayers(homePlayers);
        setAwayTeamPlayers(awayPlayers);

        // Fetch existing PlayerGWStats for all players in this gameweek
        const allPlayers = [...homePlayers, ...awayPlayers];
        const playerGWStatsPromises = allPlayers.map(async (player) => {
          try {
            const response = await fetch(
              `/api/admin/playerGWstat?playerId=${player.id}&GW=${fixtureData.round}`
            );
            if (response.ok) {
              const stats = await response.json();
              return { playerId: player.id, stats };
            }
          } catch {
            console.log(`No existing stats for player ${player.id}`);
          }
          return { playerId: player.id, stats: null };
        });

        const playerGWStatsResults = await Promise.all(playerGWStatsPromises);

        // Initialize player data with existing stats or defaults
        const initialPlayerData: Record<string, PlayerMatchData> = {};
        const existingStats: Record<string, PlayerGWStats> = {};

        allPlayers.forEach((player) => {
          const existingStat = playerGWStatsResults.find(
            (result) => result.playerId === player.id
          )?.stats;

          if (existingStat) {
            existingStats[player.id] = existingStat;
            initialPlayerData[player.id] = {
              playerId: player.id,
              minutesPlayed: existingStat.minutesPlayed,
              goalsScored: existingStat.goalsScored,
              assists: existingStat.assists,
              yellowCards: existingStat.yellowCards,
              redCards: existingStat.redCards,
              cleanSheets: existingStat.cleanSheets,
              goalsConceded: existingStat.goalsConceded,
              saves: existingStat.saves,
              penaltiesSaved: existingStat.penaltiesSaved,
              penaltiesMissed: existingStat.penaltiesMissed,
              ownGoals: existingStat.ownGoals,
            };
          } else {
            initialPlayerData[player.id] = {
              playerId: player.id,
              minutesPlayed: 0,
              goalsScored: 0,
              assists: 0,
              yellowCards: 0,
              redCards: 0,
              cleanSheets: 0,
              goalsConceded: 0,
              saves: 0,
              penaltiesSaved: 0,
              penaltiesMissed: 0,
              ownGoals: 0,
            };
          }
        });

        setPlayerData(initialPlayerData);
        setExistingPlayerGWStats(existingStats);
      } catch (error) {
        console.error("Error fetching fixture data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (fixtureId) {
      fetchFixtureData();
    }
  }, [fixtureId]);

  const updatePlayerData = (
    playerId: string,
    field: keyof PlayerMatchData,
    value: number
  ) => {
    setPlayerData((prev) => ({
      ...prev,
      [playerId]: {
        ...prev[playerId],
        [field]: Math.max(0, value), // Ensure non-negative values
      },
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Update fixture score
      await fetch(`/api/admin/fixtures/${fixtureId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          teamHScore: homeScore,
          teamAScore: awayScore,
        }),
      });

      // Update player stats for each player using PUT method
      const savePromises = Object.values(playerData).map(async (data) => {
        // Calculate points based on the player's stats
        const playerStats = {
          minutesPlayed: data.minutesPlayed,
          goalsScored: data.goalsScored,
          assists: data.assists,
          cleanSheets: data.cleanSheets,
          goalsConceded: data.goalsConceded,
          saves: data.saves,
          penaltiesSaved: data.penaltiesSaved,
          penaltiesMissed: data.penaltiesMissed,
          yellowCards: data.yellowCards,
          redCards: data.redCards,
          ownGoals: data.ownGoals,
        } as PlayerGWStats;

        const calculatedPoints = calculatePoints(playerStats);

        // Use PUT method to update existing PlayerGWStats
        return fetch(`/api/admin/playerGWstat`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            playerId: data.playerId,
            GW: fixture?.round,
            points: calculatedPoints,
            price: existingPlayerGWStats[data.playerId]?.price || 100, // Use existing price or default
            transfersIn: existingPlayerGWStats[data.playerId]?.transfersIn || 0,
            transfersOut: existingPlayerGWStats[data.playerId]?.transfersOut || 0,
            owners: existingPlayerGWStats[data.playerId]?.owners || 0,
            bonus: existingPlayerGWStats[data.playerId]?.bonus || 0,
            bps: existingPlayerGWStats[data.playerId]?.bps || 0,
            minutesPlayed: data.minutesPlayed,
            goalsScored: data.goalsScored,
            assists: data.assists,
            yellowCards: data.yellowCards,
            redCards: data.redCards,
            cleanSheets: data.cleanSheets,
            goalsConceded: data.goalsConceded,
            saves: data.saves,
            penaltiesSaved: data.penaltiesSaved,
            penaltiesMissed: data.penaltiesMissed,
            ownGoals: data.ownGoals,
          }),
        });
      });

      await Promise.all(savePromises);

      alert("Match data saved successfully!");
      router.push("/admin/match-events");
    } catch (error) {
      console.error("Error saving match data:", error);
      alert("Error saving match data");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!fixture) {
    return (
      <div className="text-center py-8">
        <p>Fixture not found</p>
        <Link href="/admin/match-events">
          <Button variant="outline" className="mt-4">
            Back to Match Events
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/match-events">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Match Input</h1>
          <p className="text-muted-foreground">
            Input detailed match data and player statistics
          </p>
        </div>
      </div>

      {/* Match Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Round {fixture.round}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              {/* Home Team */}
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={fixture.teamH.logo} />
                  <AvatarFallback>{fixture.teamH.abbr}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-bold text-lg">{fixture.teamH.name}</div>
                  <div className="text-sm text-muted-foreground">Home</div>
                </div>
              </div>

              {/* Score Input */}
              <div className="flex items-center gap-4">
                <div className="text-center">
                  <Label htmlFor="homeScore" className="text-sm">
                    Home Score
                  </Label>
                  <Input
                    id="homeScore"
                    type="number"
                    min="0"
                    value={homeScore}
                    onChange={(e) =>
                      setHomeScore(parseInt(e.target.value) || 0)
                    }
                    className="w-20 text-center text-xl font-bold"
                  />
                </div>
                <div className="text-2xl font-bold text-muted-foreground">
                  -
                </div>
                <div className="text-center">
                  <Label htmlFor="awayScore" className="text-sm">
                    Away Score
                  </Label>
                  <Input
                    id="awayScore"
                    type="number"
                    min="0"
                    value={awayScore}
                    onChange={(e) =>
                      setAwayScore(parseInt(e.target.value) || 0)
                    }
                    className="w-20 text-center text-xl font-bold"
                  />
                </div>
              </div>

              {/* Away Team */}
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <div className="font-bold text-lg">{fixture.teamA.name}</div>
                  <div className="text-sm text-muted-foreground">Away</div>
                </div>
                <Avatar className="h-12 w-12">
                  <AvatarImage src={fixture.teamA.logo} />
                  <AvatarFallback>{fixture.teamA.abbr}</AvatarFallback>
                </Avatar>
              </div>
            </div>

            {/* Match Info */}
            <div className="text-right">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                {new Date(fixture.kickoffTime).toLocaleDateString()}
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                {new Date(fixture.kickoffTime).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Player Data Input */}
      <Tabs defaultValue="home" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="home" className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={fixture.teamH.logo} />
              <AvatarFallback>{fixture.teamH.abbr}</AvatarFallback>
            </Avatar>
            {fixture.teamH.name}
          </TabsTrigger>
          <TabsTrigger value="away" className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={fixture.teamA.logo} />
              <AvatarFallback>{fixture.teamA.abbr}</AvatarFallback>
            </Avatar>
            {fixture.teamA.name}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="home" className="space-y-4">
          <PlayerStatsInput
            players={homeTeamPlayers}
            playerData={playerData}
            updatePlayerData={updatePlayerData}
            teamName={fixture.teamH.name}
          />
        </TabsContent>

        <TabsContent value="away" className="space-y-4">
          <PlayerStatsInput
            players={awayTeamPlayers}
            playerData={playerData}
            updatePlayerData={updatePlayerData}
            teamName={fixture.teamA.name}
          />
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      <div className="flex justify-end gap-4">
        <Link href="/admin/match-events">
          <Button variant="outline">Cancel</Button>
        </Link>
        <Button onClick={handleSave} disabled={saving}>
          {saving ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          Save Match Data
        </Button>
      </div>
    </div>
  );
}

// Component for player stats input
interface PlayerStatsInputProps {
  players: PlayerWithTeam[];
  playerData: Record<string, PlayerMatchData>;
  updatePlayerData: (
    playerId: string,
    field: keyof PlayerMatchData,
    value: number
  ) => void;
  teamName: string;
}

function PlayerStatsInput({
  players,
  playerData,
  updatePlayerData,
  teamName,
}: PlayerStatsInputProps) {
  const StatInput = ({
    playerId,
    field,
    label,
    max = 999,
  }: {
    playerId: string;
    field: keyof PlayerMatchData;
    label: string;
    max?: number;
  }) => {
    const value = playerData[playerId]?.[field] || 0;

    return (
      <div className="flex items-center gap-2">
        <Label className="text-xs min-w-[60px]">{label}</Label>
        <div className="flex items-center gap-1">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => updatePlayerData(playerId, field, Number(value) - 1)}
            disabled={Number(value) <= 0}
          >
            <Minus className="h-3 w-3" />
          </Button>
          <Input
            type="number"
            min="0"
            max={max}
            value={value}
            onChange={(e) =>
              updatePlayerData(playerId, field, parseInt(e.target.value) || 0)
            }
            className="w-16 text-center h-6 text-xs"
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => updatePlayerData(playerId, field, Number(value) + 1)}
            disabled={Number(value) >= max}
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          {teamName} Players
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {players.map((player) => (
            <Card key={player.id} className="p-4">
              <div className="flex items-start gap-4">
                {/* Player Info */}
                <div className="flex items-center gap-3 min-w-[200px]">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={player.team.jersey} />
                    <AvatarFallback>
                      {player.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold">{player.name}</div>
                    <Badge variant="outline" className="text-xs">
                      {player.position}
                    </Badge>
                  </div>
                </div>

                {/* Stats Grid */}
                <div className="flex-1 grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  <StatInput
                    playerId={player.id}
                    field="minutesPlayed"
                    label="Minutes"
                    max={120}
                  />
                  <StatInput
                    playerId={player.id}
                    field="goalsScored"
                    label="Goals"
                    max={10}
                  />
                  <StatInput
                    playerId={player.id}
                    field="assists"
                    label="Assists"
                    max={10}
                  />
                  <StatInput
                    playerId={player.id}
                    field="yellowCards"
                    label="Yellow"
                    max={2}
                  />
                  <StatInput
                    playerId={player.id}
                    field="redCards"
                    label="Red"
                    max={1}
                  />
                  {player.position === "GK" && (
                    <>
                      <StatInput
                        playerId={player.id}
                        field="saves"
                        label="Saves"
                        max={20}
                      />
                      <StatInput
                        playerId={player.id}
                        field="penaltiesSaved"
                        label="Pen Saved"
                        max={5}
                      />
                      <StatInput
                        playerId={player.id}
                        field="goalsConceded"
                        label="Goals Con."
                        max={10}
                      />
                    </>
                  )}
                  {(player.position === "DEF" || player.position === "GK") && (
                    <StatInput
                      playerId={player.id}
                      field="cleanSheets"
                      label="Clean Sheet"
                      max={1}
                    />
                  )}
                  <StatInput
                    playerId={player.id}
                    field="penaltiesMissed"
                    label="Pen Missed"
                    max={5}
                  />
                  <StatInput
                    playerId={player.id}
                    field="ownGoals"
                    label="Own Goals"
                    max={5}
                  />
                </div>

                {/* Calculated Points */}
                <div className="text-right min-w-[80px]">
                  <div className="text-sm text-muted-foreground">Points</div>
                  <div className="text-xl font-bold text-primary">
                    {playerData[player.id] ? (() => {
                      const stats = {
                        minutesPlayed: playerData[player.id].minutesPlayed,
                        goalsScored: playerData[player.id].goalsScored,
                        assists: playerData[player.id].assists,
                        cleanSheets: playerData[player.id].cleanSheets,
                        goalsConceded: playerData[player.id].goalsConceded,
                        saves: playerData[player.id].saves,
                        penaltiesSaved: playerData[player.id].penaltiesSaved,
                        penaltiesMissed: playerData[player.id].penaltiesMissed,
                        yellowCards: playerData[player.id].yellowCards,
                        redCards: playerData[player.id].redCards,
                        ownGoals: playerData[player.id].ownGoals,
                      } as PlayerGWStats;
                      return calculatePoints(stats);
                    })() : 0}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

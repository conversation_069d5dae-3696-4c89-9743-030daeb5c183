import { getPlayers } from "@/lib/players";
import HomeContent from "@/components/HomeContent";
import { getTranslations } from "next-intl/server";

export default async function HomePage() {
  const t = await getTranslations("Home");
  const players = await getPlayers();

  if (players.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t("loading")}...</p>
        </div>
      </div>
    );
  }

  return <HomeContent players={players} />;
}

import { But<PERSON> } from "@/components/ui/button";
import { SignInButton, SignUpButton } from "@clerk/nextjs";
import { Trophy, Users, BarChart2 } from "lucide-react";
import HomePageContent from "@/components/HomePageContent";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { auth } from "@clerk/nextjs/server";

// Force dynamic rendering to prevent static generation during build
export const dynamic = 'force-dynamic';

export default async function Home() {

  // If the user is logged in, show the dashboard
  const user = await getCurrentUser();
  const { userId } = await auth();

  if (userId && !user) {
    redirect("/onboarding");
  }
  if (userId && user) {
    return <HomePageContent />;
  }

  return (
    <main className="flex flex-col items-center min-h-screen p-4">
      <div className="max-w-4xl mx-auto text-center py-12">
        <h1 className="text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-violet-600 dark:from-blue-400 dark:to-violet-400">
          Welcome to Tunisian Fantasy League
        </h1>
        <p className="text-xl mb-8 text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Build your dream team, compete with friends, and prove your football
          knowledge in Tunisia&#39;s premier fantasy football experience.
        </p>

        <div className="flex flex-wrap gap-4 justify-center mb-12">
          <SignUpButton mode="modal">
            <Button size="lg" className="px-8">
              Get Started
            </Button>
          </SignUpButton>

          <SignInButton mode="modal">
            <Button size="lg" variant="outline" className="px-8">
              Sign In
            </Button>
          </SignInButton>
        </div>
      </div>

      {/* Features section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 w-full max-w-5xl mb-16">
        <div className="bg-card rounded-xl p-6 shadow-sm border">
          <div className="mb-4 bg-primary/10 p-3 rounded-full w-fit">
            <Trophy className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Compete for Prizes</h3>
          <p className="text-muted-foreground">
            Win weekly and seasonal prizes based on your team&#39;s performance
            in the Tunisian league.
          </p>
        </div>

        <div className="bg-card rounded-xl p-6 shadow-sm border">
          <div className="mb-4 bg-primary/10 p-3 rounded-full w-fit">
            <Users className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Create Leagues</h3>
          <p className="text-muted-foreground">
            Form private leagues with friends and colleagues to see who has the
            best football knowledge.
          </p>
        </div>

        <div className="bg-card rounded-xl p-6 shadow-sm border">
          <div className="mb-4 bg-primary/10 p-3 rounded-full w-fit">
            <BarChart2 className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Advanced Stats</h3>
          <p className="text-muted-foreground">
            Access detailed statistics and insights to make informed decisions
            about your team.
          </p>
        </div>
      </div>

      {/* CTA section */}
      <div className="w-full max-w-3xl bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl p-8 text-center mb-12">
        <h2 className="text-2xl font-bold mb-4">
          Ready to test your football knowledge?
        </h2>
        <p className="mb-6">
          Join thousands of Tunisian football fans competing for glory and
          prizes.
        </p>
        <SignUpButton mode="modal">
          <Button size="lg">Create Your Team Now</Button>
        </SignUpButton>
      </div>
    </main>
  );
}

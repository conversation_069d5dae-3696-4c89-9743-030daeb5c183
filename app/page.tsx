
import { getPlayers } from "@/lib/players";
import { PlayerInfo } from "@/types/types";


export default async function HomePageContent() {
  const players : PlayerInfo[] = await getPlayers();
  
  if (players.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <HomePageContent players={players} />
  );
}

"use client";

import { createContext, useContext, useState, ReactNode } from "react";

type ViewMode = "list" | "pitch";

interface ViewContextType {
  view: ViewMode;
  setView: (view: ViewMode) => void;
}

// Create context with a default value
const ViewContext = createContext<ViewContextType | undefined>(undefined);

// Provider component
export function ViewProvider({ children, defaultView = "pitch" }: { 
  children: ReactNode;
  defaultView?: ViewMode;
}) {
  const [view, setView] = useState<ViewMode>(defaultView);
  
  return (
    <ViewContext.Provider value={{ view, setView }}>
      {children}
    </ViewContext.Provider>
  );
}

// Custom hook to use the context
export function useView() {
  const context = useContext(ViewContext);
  if (context === undefined) {
    throw new Error("useView must be used within a ViewProvider");
  }
  return context;
}
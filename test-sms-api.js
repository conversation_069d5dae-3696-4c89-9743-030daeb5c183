// Simple test script to check SMS API without 307 redirects
// Run with: node test-sms-api.js

const testSmsApi = async () => {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Testing SMS API endpoints...\n');

  // Test 1: SMS Test endpoint
  console.log('1. Testing SMS configuration...');
  try {
    const testResponse = await fetch(`${baseUrl}/api/sms/test`);
    console.log(`   Status: ${testResponse.status}`);
    
    if (testResponse.status === 307) {
      console.log('   ❌ Got 307 redirect - middleware issue');
      const location = testResponse.headers.get('location');
      console.log(`   Redirect to: ${location}`);
    } else {
      const testData = await testResponse.json();
      console.log(`   ✅ Response: ${testData.status}`);
      console.log(`   Message: ${testData.message}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log('\n2. Testing SMS send endpoint...');
  try {
    const sendResponse = await fetch(`${baseUrl}/api/sms/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phoneNumber: '12345678' })
    });
    
    console.log(`   Status: ${sendResponse.status}`);
    
    if (sendResponse.status === 307) {
      console.log('   ❌ Got 307 redirect - middleware issue');
      const location = sendResponse.headers.get('location');
      console.log(`   Redirect to: ${location}`);
    } else {
      const sendData = await sendResponse.json();
      console.log(`   Response: ${JSON.stringify(sendData, null, 2)}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log('\n3. Testing SMS verify endpoint...');
  try {
    const verifyResponse = await fetch(`${baseUrl}/api/sms/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phoneNumber: '12345678', code: '123456' })
    });
    
    console.log(`   Status: ${verifyResponse.status}`);
    
    if (verifyResponse.status === 307) {
      console.log('   ❌ Got 307 redirect - middleware issue');
      const location = verifyResponse.headers.get('location');
      console.log(`   Redirect to: ${location}`);
    } else {
      const verifyData = await verifyResponse.json();
      console.log(`   Response: ${JSON.stringify(verifyData, null, 2)}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }
};

// Run the test
testSmsApi().catch(console.error);

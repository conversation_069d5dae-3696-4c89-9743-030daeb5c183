"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import PitchView from "@/components/PitchView";
import {
  Trophy,
  TrendingUp,
  TrendingDown,
  Users,
  RefreshCw,
  Target,
  Award,
  Calendar,
  CheckCircle,
  Sparkles,
} from "lucide-react";
import Link from "next/link";
import { PlayerInfo } from "@/types/types";
import { Button } from "@/components/ui/button";

interface RoundStats {
  roundNumber: number;
  status: "active" | "complete" | "upcoming";
  userScore: number;
  averageScore: number;
  highestScore: number;
  transfersMade: number;
  wildcardsPlayed: number;
  mostCaptained: string;
}

interface TransferData {
  player: PlayerInfo;
  transfersIn: number;
  transfersOut: number;
  netTransfers: number;
}

export default  function HomePageContent(players: PlayerInfo[]) {
  const [roundStats, setRoundStats] = useState<RoundStats>({
    roundNumber: 0,
    status: "upcoming",
    userScore: 0,
    averageScore: 0,
    highestScore: 0,
    transfersMade: 0,
    wildcardsPlayed: 0,
    mostCaptained: "",
  });

  const [dreamTeam, setDreamTeam] = useState<PlayerInfo[]>([]);
  const [transferData, setTransferData] = useState<TransferData[]>([]);
  const [isLoading, setIsLoading] = useState(true);


      // Fake data for testing
    const generateFakeRoundStats = (): RoundStats => {
      return {
        roundNumber: 30,
        status: "complete",
        userScore: Math.floor(Math.random() * 100),
        averageScore: Math.floor(Math.random() * 100),
        highestScore: Math.floor(Math.random() * 100),
        transfersMade: Math.floor(Math.random() * 1000),
        wildcardsPlayed: Math.floor(Math.random() * 100),
        mostCaptained: "Bleili",
      };
    };

    const playerFakeStats = (player: PlayerInfo, GW: string) => {
      return {
        id: "1",
        playerId: player.id,
        GW,
        points: Math.floor(Math.random() * 10),
        minutesPlayed: Math.floor(Math.random() * 90),
        goalsScored: Math.floor(Math.random() * 3),
        assists: Math.floor(Math.random() * 2),
        cleanSheets: Math.floor(Math.random() * 1),
        goalsConceded: Math.floor(Math.random() * 3),
        ownGoals: 0,
        penaltiesSaved: 0,
        penaltiesMissed: 0,
        yellowCards: Math.floor(Math.random() * 2),
        redCards: Math.floor(Math.random() * 1),
        saves: Math.floor(Math.random() * 5),
        bonus: Math.floor(Math.random() * 3),
        bps: Math.floor(Math.random() * 50), // Added bps property
        price: Number(player.currentPrice),
        transfersIn: 0,
        transfersOut: 0,
        owners: 0,
      };
    };

    const generateFakeDreamTeam = (GW: string) => {
      const GKs = players.filter((p) => p.position === "GK");
      const DEFs = players.filter((p) => p.position === "DEF");
      const MIDs = players.filter((p) => p.position === "MID");
      const ATKs = players.filter((p) => p.position === "ATK");
      // get random players
      GKs.sort(() => Math.random() - 0.5);
      DEFs.sort(() => Math.random() - 0.5);
      MIDs.sort(() => Math.random() - 0.5);
      ATKs.sort(() => Math.random() - 0.5);
      // add random points for the current fake gameweek
      GKs.forEach((p) => {
        p.gwStats = [playerFakeStats(p, GW)];
      });
      DEFs.forEach((p) => {
        p.gwStats = [playerFakeStats(p, GW)];
      });
      MIDs.forEach((p) => {
        p.gwStats = [playerFakeStats(p, GW)];
      });
      ATKs.forEach((p) => {
        p.gwStats = [playerFakeStats(p, GW)];
      });
      // get top 1 GK, top 4 DEF, top 4 MID, top 2 ATK
      return [
        ...GKs.slice(0, 1),
        ...DEFs.slice(0, 4),
        ...MIDs.slice(0, 4),
        ...ATKs.slice(0, 2),
      ];
    };

    const generateFakeTransferData = () => {
      // random players
      const randomPlayers = players.sort(() => Math.random() - 0.5).slice(0, 50);
      return randomPlayers.map((p) => {
        const transfersIn = Math.floor(Math.random() * 1000);
        const transfersOut = Math.floor(Math.random() * 1000);
        return {
          player: p,
          transfersIn,
          transfersOut,
          netTransfers: transfersIn - transfersOut,
        };
      });
    };

  useEffect(() => {
    // const loadData = async () => {

    //   setIsLoading(true);

    //   try {
    //     // Fetch round stats
    //     const roundStatsResponse = await fetch("/api/stats?type=overview");
    //     if (roundStatsResponse.ok) {
    //       const roundStatsData = await roundStatsResponse.json();
    //       setRoundStats(roundStatsData);
    //     }

    //     // Fetch dream team
    //     const dreamTeamResponse = await fetch("/api/stats?type=dream-team");
    //     if (dreamTeamResponse.ok) {
    //       const dreamTeamData = await dreamTeamResponse.json();
    //       setDreamTeam(dreamTeamData.players);
    //     }

    //     // Fetch transfer data
    //     const transferDataResponse = await fetch("/api/stats?type=transfers");
    //     if (transferDataResponse.ok) {
    //       const transferData = await transferDataResponse.json();
    //       setTransferData(transferData);
    //     }
    //   } catch (error) {
    //     console.error("Error loading data:", error);
    //   } finally {
    //     setIsLoading(false);
    //   }
    // };

    // loadData();

    const setFakeDate = () => {
      const stats = generateFakeRoundStats();
      setRoundStats(stats);
      setDreamTeam([]);
      setTransferData([]);
      setDreamTeam(generateFakeDreamTeam("30"));
      setTransferData(generateFakeTransferData());
      setIsLoading(false);
    };
    
    setFakeDate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [players]);





  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-6">
        {/* Welcome Header */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold">
            Welcome back Manager!
          </h1>
          <p className="text-muted-foreground">
            Here&#39;s your Tunisian Fantasy League overview
          </p>
        </div>

        {/* Round Status Card */}
        <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-secondary/5">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {/* Gameweek {activeGameweek?.gameWeek.GW} Status */}
                Gameweek 30 Status
              </CardTitle>
              <Badge
                variant={
                  roundStats.status === "complete" ? "default" : "secondary"
                }
                className="flex items-center gap-1"
              >
                <CheckCircle className="h-3 w-3" />
                {roundStats.status === "complete" ? "Complete" : "Active"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {roundStats.userScore}
                </div>
                <div className="text-xs text-muted-foreground">Your Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {roundStats.averageScore}
                </div>
                <div className="text-xs text-muted-foreground">
                  Average Score
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {roundStats.highestScore}
                </div>
                <div className="text-xs text-muted-foreground">
                  Highest Score
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {roundStats.transfersMade.toLocaleString()}
                </div>
                <div className="text-xs text-muted-foreground">
                  Transfers Made
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {roundStats.wildcardsPlayed}
                </div>
                <div className="text-xs text-muted-foreground">
                  Wildcards Played
                </div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold">
                  {roundStats.mostCaptained}
                </div>
                <div className="text-xs text-muted-foreground">
                  Most Captained
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <Link href="/team">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 mx-auto mb-2 text-primary" />
                <h3 className="font-semibold text-sm">My Team</h3>
              </CardContent>
            </Card>
          </Link>

          <Link href="/transfers">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <RefreshCw className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                <h3 className="font-semibold text-sm">Transfers</h3>
              </CardContent>
            </Card>
          </Link>

          <Link href="/leagues">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <Trophy className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
                <h3 className="font-semibold text-sm">Leagues</h3>
              </CardContent>
            </Card>
          </Link>

          <Link href="/statistics">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <Target className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <h3 className="font-semibold text-sm">Statistics</h3>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="dream-team" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="dream-team">Dream Team</TabsTrigger>
            <TabsTrigger value="transfers">Transfer Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="dream-team" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-500" />
                  {/* Gameweek {activeGameweek?.gameWeek.GW} Dream Team */}
                  Gameweek 30 Dream Team
                </CardTitle>
                {/* generate Dream team button */}
                <Button
                  variant="secondary"
                  size="sm"
                  className="flex items-center gap-2"
                  onClick={() => {
                    setDreamTeam(generateFakeDreamTeam("30"));
                  }}
                >
                  <Sparkles className="h-4 w-4" />
                  Generate Dream Team
                </Button>
              </CardHeader>
              <CardContent>
                <PitchView
                  // round={activeGameweek?.gameWeek.id || ""}
                  round={"30"}
                  starters={dreamTeam}
                  onSelect={() => {}}
                  showPoints
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transfers" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-600">
                    <TrendingUp className="h-5 w-5" />
                    Most Transferred In
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {transferData
                      .filter((p) => p.netTransfers > 0)
                      .slice(0, 5)
                      .map(({ player, netTransfers }, index) => (
                        <div
                          key={player.id}
                          className="flex items-center justify-between p-2 rounded-lg bg-muted/50"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center text-xs font-bold text-green-600">
                              {index + 1}
                            </div>
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={player.team.jersey} />
                            </Avatar>
                            <div>
                              <div className="font-semibold text-sm">
                                {player.name}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {player.team.name} • {player.position}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-green-600">
                              +{netTransfers.toLocaleString()}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {Number(player.currentPrice)}M
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-600">
                    <TrendingDown className="h-5 w-5" />
                    Most Transferred Out
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {transferData
                      .filter((p) => p.netTransfers < 0)
                      .slice(0, 5)
                      .map(({ player, netTransfers }, index) => (
                        <div
                          key={player.id}
                          className="flex items-center justify-between p-2 rounded-lg bg-muted/50"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center text-xs font-bold text-red-600">
                              {index + 1}
                            </div>
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={player.team.jersey} />
                            </Avatar>
                            <div>
                              <div className="font-semibold text-sm">
                                {player.name}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {player.team.name} • {player.position}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-red-600">
                              {netTransfers.toLocaleString()}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {Number(player.currentPrice)}M
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

"use client";
import { useLocale } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Globe } from "lucide-react";
import { setUserLocale } from "@/lib/locale";
import { useTransition } from "react";
import { Locale } from "@/i18n/config";

export function LanguageSwitcher() {
  const locale = useLocale(); 
  const [isPending, startTransition] = useTransition(); 

  function onChange(value: string) {
    const locale = value as Locale;
    startTransition(() => {
      setUserLocale(locale);
    });
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Globe className="h-[1.2rem] w-[1.2rem]" />
          <span className="sr-only">Toggle language</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onChange("en")}>
          En
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onChange("fr")}>
          Fr
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onChange("ar")}>
          Ar
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

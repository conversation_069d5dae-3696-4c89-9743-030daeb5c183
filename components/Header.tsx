import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import TeamViewToggle from "@/components/TeamViewToggle";

interface HeaderProps {
  round?: string;
  isDreamTeamVisible?: boolean;
  isNextFixtureVisible?: boolean;
  isPreviousFixtureVisible?: boolean;
  isTransfersPage?: boolean;
  hasChanges?: boolean;
  isLoading?: boolean;
  onSave?: () => void;
  title?: string;
}

export default function Header({
  round,
  isDreamTeamVisible = false,
  isNextFixtureVisible = false,
  isPreviousFixtureVisible = false,
  isTransfersPage = false,
  hasChanges = false,
  isLoading = false,
  onSave,
  title,
}: HeaderProps) {
  const router = useRouter();

  const handlePrevious = () => {
    if (round) {
      const prevRound = parseInt(round) - 1;
      // TODO: Change this to the current user's ID
      router.push(`/points/1/round/${prevRound}`);
    }
  };

  const handleNext = () => {
    if (round) {
      const nextRound = parseInt(round) + 1;
      // TODO: Change this to the current user's ID
      router.push(`/points/1/round/${nextRound}`);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
      <div className="flex items-center justify-center sm:justify-start gap-2 w-full sm:w-auto">
        {isPreviousFixtureVisible && (
          <Button variant="outline" onClick={handlePrevious} className="h-8 w-20">
            Previous
          </Button>
        )}
        <span className="font-semibold text-lg text-center min-w-[100px]">
          {title || (round ? `Round ${round}` : "Transfers")}
        </span>
        {isNextFixtureVisible && (
          <Button variant="outline" onClick={handleNext} className="h-8 w-20">
            Next
          </Button>
        )}
      </div>
      
      {/* Center the view toggle */}
      {isTransfersPage && (
        <div className="flex items-center justify-center">
          <TeamViewToggle />
        </div>
      )}
      
      <div className="flex items-center justify-center sm:justify-end gap-4 w-full sm:w-auto">
        {isTransfersPage && onSave && (
          <Button
            onClick={onSave}
            disabled={!hasChanges || isLoading}
          >
            {isLoading ? "Saving..." : "Save Transfers"}
          </Button>
        )}
        {isDreamTeamVisible && <Button variant="secondary">Dream Team</Button>}
      </div>
    </div>
  );
}

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import TeamViewToggle from "@/components/TeamViewToggle";
import { ViewMode } from "@/types/types";
import { useFantasy } from "@/hooks/useFantasy";
import {
  Users,
  RefreshCw,
  DollarSign,
  ChevronLeft,
  ChevronRight,
  Save,
  Sparkles,
} from "lucide-react";

interface HeaderProps {
  view: ViewMode;
  setView: (view: ViewMode) => void;
  round?: string;
  isDreamTeamVisible?: boolean;
  isNextFixtureVisible?: boolean;
  isPreviousFixtureVisible?: boolean;
  hasChanges?: boolean;
  isLoading?: boolean;
  onSave?: () => void;
  title?: string;
  userId: string;
  playerCount?: number;
  isSaveDisabled?: boolean;
  remainingTransfers?: number;
  remainingBudget?: number;
}

export default function Header({
  view,
  setView,
  isDreamTeamVisible = false,
  isNextFixtureVisible = false,
  isPreviousFixtureVisible = false,
  isLoading = false,
  onSave,
  title,
  userId,
  playerCount,
  remainingTransfers,
  remainingBudget,
  isSaveDisabled = false,
}: HeaderProps) {
  const router = useRouter();
  const { activeGameweek, getGameweekBySeasonAndGW } = useFantasy();
  const currentGW = activeGameweek?.gameWeek;

  const handlePrevious = () => {
    if (currentGW) {
      const prevRound = currentGW.GW - 1;
      router.push(
        `/points/${userId}/round/${
          getGameweekBySeasonAndGW(currentGW.season, prevRound)?.id
        }`
      );
    }
  };

  const handleNext = () => {
    if (currentGW) {
      const nextRound = currentGW.GW + 1;
      router.push(
        `/points/${userId}/round/${
          getGameweekBySeasonAndGW(currentGW.season, nextRound)?.id
        }`
      );
    }
  };

  return (
    <div className="w-full space-y-4">
      {/* Team Stats Bar */}
      {playerCount !== undefined && remainingTransfers && remainingBudget && (
        <Card className="bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
              <div className="flex items-center justify-center gap-2">
                <Users className="h-4 w-4 text-primary" />
                <span className="font-semibold text-sm text-muted-foreground">
                  Players:
                </span>
                <Badge variant="outline" className="font-bold">
                  {playerCount} / 15
                </Badge>
              </div>
              <div className="flex items-center justify-center gap-2">
                <RefreshCw className="h-4 w-4 text-blue-500" />
                <span className="font-semibold text-sm text-muted-foreground">
                  Transfers:
                </span>
                <Badge
                  variant={remainingTransfers > 0 ? "default" : "destructive"}
                  className="font-bold"
                >
                  0 / {remainingTransfers}
                </Badge>
              </div>
              <div className="flex items-center justify-center gap-2">
                <DollarSign className="h-4 w-4 text-green-500" />
                <span className="font-semibold text-sm text-muted-foreground">
                  Budget:
                </span>
                <Badge variant="outline" className="font-bold">
                  ${remainingBudget.toFixed(1)}M
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      {/* Main Header */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            {/* Navigation */}
            <div className="flex items-center justify-center sm:justify-start gap-3">
              {isPreviousFixtureVisible && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevious}
                  className="flex items-center gap-2"
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
              )}

              <div className="flex items-center gap-2">
                <Badge
                  variant="secondary"
                  className="text-lg font-bold px-3 py-1"
                >
                  {title || `Gameweek ${currentGW?.GW || "?"}`}
                </Badge>
              </div>

              {isNextFixtureVisible && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNext}
                  className="flex items-center gap-2"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* View Toggle */}
            <div className="flex items-center justify-center">
              <TeamViewToggle view={view} setView={setView} />
            </div>

            {/* Actions */}
            <div className="flex items-center justify-center sm:justify-end gap-3">
              {onSave && (
                <Button
                  onClick={onSave}
                  disabled={isSaveDisabled || isLoading}
                  className="flex items-center gap-2"
                  size="sm"
                >
                  <Save className="h-4 w-4" />
                  {isLoading ? "Saving..." : "Save Team"}
                </Button>
              )}
              {isDreamTeamVisible && (
                <Button
                  variant="secondary"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Sparkles className="h-4 w-4" />
                  Dream Team
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

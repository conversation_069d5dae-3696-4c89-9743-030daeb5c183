import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import TeamViewToggle from "@/components/TeamViewToggle";
import { ViewMode } from "@/types/types";

interface HeaderProps {
  view: ViewMode;
  setView: (view: ViewMode) => void;
  round?: string;
  isDreamTeamVisible?: boolean;
  isNextFixtureVisible?: boolean;
  isPreviousFixtureVisible?: boolean;
  hasChanges?: boolean;
  isLoading?: boolean;
  onSave?: () => void;
  title?: string;
  userId: string;
  playerCount?: number;
  isSaveDisabled?: boolean;
  remainingTransfers?: number;
  remainingBudget?: number;
}

export default function Header({
  round,
  view,
  setView,
  isDreamTeamVisible = false,
  isNextFixtureVisible = false,
  isPreviousFixtureVisible = false,
  isLoading = false,
  onSave,
  title,
  userId,
  playerCount,
  remainingTransfers,
  remainingBudget,
  isSaveDisabled = false,
}: HeaderProps) {
  const router = useRouter();
  console.log("Header", { playerCount, remainingTransfers, remainingBudget });

  const handlePrevious = () => {
    if (round) {
      const prevRound = parseInt(round) - 1;
      // TODO: Change this to the current user's ID
      router.push(`/points/${userId}/round/${prevRound}`);
    }
  };

  const handleNext = () => {
    if (round) {
      const nextRound = parseInt(round) + 1;
      // TODO: Change this to the current user's ID
      router.push(`/points/${userId}/round/${nextRound}`);
    }
  };

  return (
    <div className=" flex flex-col gap-4 w-full sm:flex-row sm:justify-between sm:items-center">
      {playerCount !== undefined && remainingTransfers && remainingBudget && (
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="flex items-center justify-center sm:justify-start gap-2 w-full sm:w-auto">
            <span className="font-semibold text-lg text-center min-w-[100px]">
              {playerCount} / 15
            </span>
          </div>
          <div className="flex items-center justify-center sm:justify-start gap-2 w-full sm:w-auto">
            <span className="font-semibold text-lg text-center min-w-[100px]">
              Transfers: 0 / {remainingTransfers}
            </span>
          </div>
          <div className="flex items-center justify-center sm:justify-start gap-2 w-full sm:w-auto">
            <span className="font-semibold text-lg text-center min-w-[100px]">
              Budget: ${remainingBudget.toFixed(1)}M
            </span>
          </div>
        </div>
      )}
      <div className="flex flex-col mb-4 sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="flex items-center justify-center sm:justify-start gap-2 w-full sm:w-auto">
          {isPreviousFixtureVisible && (
            <Button
              variant="outline"
              onClick={handlePrevious}
              className="h-8 w-20"
            >
              Previous
            </Button>
          )}
          <span className="font-semibold text-lg text-center min-w-[100px]">
          {title || `Round ${round}`}
          </span>
          {isNextFixtureVisible && (
            <Button variant="outline" onClick={handleNext} className="h-8 w-20">
              Next
            </Button>
          )}
        </div>

        <div className="flex items-center justify-center">
          <TeamViewToggle view={view} setView={setView} />
        </div>

        <div className="flex items-center justify-center sm:justify-end gap-4 w-full sm:w-auto">
          {onSave && (
            <Button onClick={onSave} disabled={isSaveDisabled || isLoading}>
              {isLoading ? "Saving..." : "Save Team"}
            </Button>
          )}
          {isDreamTeamVisible && (
            <Button variant="secondary">Dream Team</Button>
          )}
        </div>
      </div>
    </div>
  );
}

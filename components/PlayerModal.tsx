// components/PlayerModal.tsx
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { getSubsList, isSub } from "@/lib/utils";
import { PlayerInfo } from "@/types/types";

interface PlayerModalProps {
  player: PlayerInfo | null;
  starters?: PlayerInfo[];
  subs?: PlayerInfo[];
  onClose: () => void;
  onMakeCaptain?: (p: string) => void;
  onMakeViceCaptain?: (p: string) => void;
  onSubstitute?: (outId: string, inId: string) => void;
  onRemove?: (p: PlayerInfo) => void;
  onRestore?: (p: PlayerInfo) => void;
  onAdd?: (p: PlayerInfo) => void;
  removedPlayers?: PlayerInfo[];
  isEditable?: boolean;
  isBuildingTeam?: boolean;
}

export default function PlayerModal({
  player,
  starters = [],
  subs = [],
  onClose,
  onMakeCaptain,
  onMakeViceCaptain,
  onSubstitute,
  onRemove,
  onAdd,
  onRestore,
  isEditable = true,
  removedPlayers = [],
  isBuildingTeam = false,
}: PlayerModalProps) {
  if (!player) return null;
  console.log("subs", starters, subs, removedPlayers);
  const isRemoveVisible =
    isEditable &&
    onRemove &&
    [...starters, ...subs].some((p) => p.id === player.id) &&
    !removedPlayers.some((p) => p.id === player.id);
  const isRestoreVisible =
    isEditable && onRestore && !isBuildingTeam &&
    removedPlayers.some((p) => p.id === player.id);
  // TODO: Fix this
  const isAddVisible =
    isEditable &&
    onAdd &&
    !!removedPlayers.length &&
    [...starters, ...subs].every((p) => p.id !== player.id);
  const playerIsSub = isSub(player, subs);
  const subsList = playerIsSub ? [] : getSubsList(player, subs, starters);

  return (
    <Dialog open={!!player} onOpenChange={onClose}>
      <DialogContent className="bg-card border border-border">
        <DialogHeader>
          <DialogTitle className="text-foreground">
            {player ? player.name : "Select a player"}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {/* TODO: Get team abbr from player.teamId */}
            {player
              ? `Team: ${player.teamId} – ${player.position}`
              : "Select a player to add"}
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-2 mt-2">
          {player && (
            <Button
              variant="outline"
              onClick={() => alert("Details coming soon")}
            >
              View Details
            </Button>
          )}
          {isEditable && player && onMakeCaptain && !playerIsSub && (
            <Button
              variant="secondary"
              onClick={() => {
                onMakeCaptain(player.id);
                onClose();
              }}
            >
              Make Captain
            </Button>
          )}
          {isEditable && player && onMakeViceCaptain && !playerIsSub && (
            <Button
              variant="secondary"
              onClick={() => {
                onMakeViceCaptain(player.id);
                onClose();
              }}
            >
              Make Vice Captain
            </Button>
          )}

          {isRemoveVisible && (
            <Button
              variant="destructive"
              onClick={() => {
                onRemove(player);
                onClose();
              }}
            >
              Remove Player
            </Button>
          )}

          {isRestoreVisible && (
            <Button
              variant="secondary"
              onClick={() => {
                onRestore(player);
                onClose();
              }}
            >
              Restore Player
            </Button>
          )}

          {isAddVisible && (
            <Button
              variant="secondary"
              onClick={() => {
                onAdd(player);
                onClose();
              }}
            >
              Add Player
            </Button>
          )}

          {isEditable && player && onSubstitute && !playerIsSub && (
            <div>
              <p className="text-sm font-medium text-muted-foreground mb-1">
                Substitute with:
              </p>
              <div className="flex flex-col gap-1">
                {subsList.map((s) => {
                  return (
                    <Button
                      key={s.id}
                      variant="ghost"
                      onClick={() => {
                        onSubstitute(player.id, s.id);
                        onClose();
                      }}
                    >
                      {`${s.name} (${s.position})`}
                    </Button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { User, Save, Loader2 } from "lucide-react";
import { Team } from "@prisma/client";
import { PlayerInfo } from "@/types/types";


interface EditPlayerDialogProps {
  player: PlayerInfo | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (player: PlayerInfo) => void;
}

export default function EditPlayerDialog({
  player,
  open,
  onOpenChange,
  onSave,
}: EditPlayerDialogProps) {
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    teamId: "",
    position: "",
    currentPrice: "",
  });

  useEffect(() => {
    if (open) {
      fetchTeams();
      if (player) {
        setFormData({
          name: player.name,
          teamId: player.teamId,
          position: player.position,
          currentPrice: player.currentPrice.toString(),
        });
      }
    }
  }, [open, player]);

  const fetchTeams = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/teams");
      const data = await response.json();
      setTeams(data);
    } catch (error) {
      console.error("Error fetching teams:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!player) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/admin/players/${player.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: formData.name,
          teamId: formData.teamId,
          position: formData.position,
          currentPrice: parseFloat(formData.currentPrice),
        }),
      });

      if (response.ok) {
        const updatedPlayer = await response.json();
        onSave(updatedPlayer);
        onOpenChange(false);
      } else {
        const error = await response.json();
        alert(error.error || "Failed to update player");
      }
    } catch (error) {
      console.error("Error updating player:", error);
      alert("Error updating player");
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const selectedTeam = teams.find(t => t.id === formData.teamId);

  const getPositionColor = (position: string) => {
    switch (position) {
      case "GK": return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      case "DEF": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "MID": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";
      case "ATK": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Edit Player
          </DialogTitle>
          <DialogDescription>
            Update player information and details.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Player Preview */}
            {player && (
              <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={player.team.jersey} />
                  <AvatarFallback>
                    {formData.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="font-semibold">{formData.name || "Player Name"}</div>
                  <div className="flex items-center gap-2 mt-1">
                    {selectedTeam && (
                      <>
                        <Avatar className="h-5 w-5">
                          <AvatarImage src={selectedTeam.logo} />
                          <AvatarFallback>{selectedTeam.abbr}</AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-muted-foreground">{selectedTeam.name}</span>
                      </>
                    )}
                    {formData.position && (
                      <Badge className={getPositionColor(formData.position)}>
                        {formData.position}
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">Price</div>
                  <div className="font-bold text-lg">
                    ${formData.currentPrice || "0.0"}M
                  </div>
                </div>
              </div>
            )}

            {/* Player Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Player Name *</Label>
              <Input
                id="name"
                placeholder="Enter player name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                required
              />
            </div>

            {/* Team Selection */}
            <div className="space-y-2">
              <Label htmlFor="team">Team *</Label>
              <Select
                value={formData.teamId}
                onValueChange={(value) => handleInputChange("teamId", value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a team">
                    {selectedTeam && (
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={selectedTeam.logo} />
                          <AvatarFallback>{selectedTeam.abbr}</AvatarFallback>
                        </Avatar>
                        <span>{selectedTeam.name}</span>
                        <span className="text-muted-foreground">({selectedTeam.abbr})</span>
                      </div>
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {teams.map((team) => (
                    <SelectItem key={team.id} value={team.id}>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={team.logo} />
                          <AvatarFallback>{team.abbr}</AvatarFallback>
                        </Avatar>
                        <span>{team.name}</span>
                        <span className="text-muted-foreground">({team.abbr})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Position */}
            <div className="space-y-2">
              <Label htmlFor="position">Position *</Label>
              <Select
                value={formData.position}
                onValueChange={(value) => handleInputChange("position", value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select position">
                    {formData.position && (
                      <div className="flex items-center gap-2">
                        <Badge className={getPositionColor(formData.position)}>
                          {formData.position}
                        </Badge>
                        <span>
                          {formData.position === "GK" && "Goalkeeper"}
                          {formData.position === "DEF" && "Defender"}
                          {formData.position === "MID" && "Midfielder"}
                          {formData.position === "ATK" && "Attacker"}
                        </span>
                      </div>
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GK">
                    <div className="flex items-center gap-2">
                      <Badge className={getPositionColor("GK")}>GK</Badge>
                      <span>Goalkeeper</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="DEF">
                    <div className="flex items-center gap-2">
                      <Badge className={getPositionColor("DEF")}>DEF</Badge>
                      <span>Defender</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="MID">
                    <div className="flex items-center gap-2">
                      <Badge className={getPositionColor("MID")}>MID</Badge>
                      <span>Midfielder</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="ATK">
                    <div className="flex items-center gap-2">
                      <Badge className={getPositionColor("ATK")}>ATK</Badge>
                      <span>Attacker</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Current Price */}
            <div className="space-y-2">
              <Label htmlFor="price">Current Price (Million) *</Label>
              <Input
                id="price"
                type="number"
                step="0.1"
                min="0.1"
                max="20"
                placeholder="e.g., 5.5"
                value={formData.currentPrice}
                onChange={(e) => handleInputChange("currentPrice", e.target.value)}
                required
              />
              <p className="text-sm text-muted-foreground">
                Price in millions (e.g., 5.5 for 5.5M)
              </p>
            </div>
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving || loading}>
            {saving ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

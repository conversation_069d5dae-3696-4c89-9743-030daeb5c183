"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import PitchView from "@/components/PitchView";
import {
  Trophy,
  TrendingUp,
  TrendingDown,
  Users,
  RefreshCw,
  Target,
  Award,
  Calendar,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";
import { PlayerInfo } from "@/types/types";
import { ActiveGameWeekData } from "@/lib/activeGameweek";

interface RoundStats {
  roundNumber: number;
  status: "active" | "complete" | "upcoming";
  userScore: number;
  averageScore: number;
  highestScore: number;
  transfersMade: number;
  wildcardsPlayed: number;
  mostCaptained: string;
}

interface TransferData {
  player: PlayerInfo;
  transfersIn: number;
  transfersOut: number;
  netTransfers: number;
}

export default function HomePageContent() {
  const { user } = useUser();
  const [activeGameweek, setActiveGameweek] = useState<ActiveGameWeekData | null>(null);
  const [roundStats, setRoundStats] = useState<RoundStats>({
    roundNumber: 0,
    status: "complete",
    userScore: 0,
    averageScore: 0,
    highestScore: 0,
    transfersMade: 0,
    wildcardsPlayed: 0,
    mostCaptained: "A.Malek",
  });

  const [dreamTeam, setDreamTeam] = useState<PlayerInfo[]>([]);
  const [transferData, setTransferData] = useState<TransferData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch active gameweek and data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);

      try {
        // Fetch active gameweek
        const activeGwResponse = await fetch('/api/activeGameweek');
        if (activeGwResponse.ok) {
          const activeGwData = await activeGwResponse.json();
          setActiveGameweek(activeGwData);

          // Update round stats with active gameweek
          setRoundStats(prev => ({
            ...prev,
            roundNumber: activeGwData.gameWeek.GW,
          }));
        }

        // TODO: Fetch dream team data from API based on active gameweek
        // Mock dream team data for now
        const mockDreamTeam: PlayerInfo[] = [] as PlayerInfo[];

        // TODO: Fetch transfer data from API based on active gameweek
        // Mock transfer data for now
        const mockTransferData: TransferData[] = [];

        setDreamTeam(mockDreamTeam);
        setTransferData(mockTransferData);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-6">
        {/* Welcome Header */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold">
            Welcome back, {user?.firstName || "Manager"}!
          </h1>
          <p className="text-muted-foreground">
            Here&#39;s your Tunisian Fantasy League overview
          </p>
        </div>

        {/* Round Status Card */}
        <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-secondary/5">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Gameweek {activeGameweek?.gameWeek.GW || roundStats.roundNumber} Status
              </CardTitle>
              <Badge
                variant={
                  roundStats.status === "complete" ? "default" : "secondary"
                }
                className="flex items-center gap-1"
              >
                <CheckCircle className="h-3 w-3" />
                {roundStats.status === "complete" ? "Complete" : "Active"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {roundStats.userScore}
                </div>
                <div className="text-xs text-muted-foreground">Your Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {roundStats.averageScore}
                </div>
                <div className="text-xs text-muted-foreground">
                  Average Score
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {roundStats.highestScore}
                </div>
                <div className="text-xs text-muted-foreground">
                  Highest Score
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {roundStats.transfersMade.toLocaleString()}
                </div>
                <div className="text-xs text-muted-foreground">
                  Transfers Made
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {roundStats.wildcardsPlayed}
                </div>
                <div className="text-xs text-muted-foreground">
                  Wildcards Played
                </div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold">
                  {roundStats.mostCaptained}
                </div>
                <div className="text-xs text-muted-foreground">
                  Most Captained
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <Link href="/team">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 mx-auto mb-2 text-primary" />
                <h3 className="font-semibold text-sm">My Team</h3>
              </CardContent>
            </Card>
          </Link>

          <Link href="/transfers">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <RefreshCw className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                <h3 className="font-semibold text-sm">Transfers</h3>
              </CardContent>
            </Card>
          </Link>

          <Link href="/leagues">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <Trophy className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
                <h3 className="font-semibold text-sm">Leagues</h3>
              </CardContent>
            </Card>
          </Link>

          <Link href="/statistics">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <Target className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <h3 className="font-semibold text-sm">Statistics</h3>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="dream-team" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="dream-team">Dream Team</TabsTrigger>
            <TabsTrigger value="transfers">Transfer Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="dream-team" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-500" />
                  Gameweek {activeGameweek?.gameWeek.GW || roundStats.roundNumber} Dream Team
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PitchView
                  round={activeGameweek?.gameWeek.id || "1"}
                  starters={dreamTeam}
                  onSelect={() => {}}
                  showPrice
                  showPoints
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transfers" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-600">
                    <TrendingUp className="h-5 w-5" />
                    Most Transferred In
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {transferData
                      .filter((p) => p.netTransfers > 0)
                      .slice(0, 5)
                      .map(({ player, netTransfers }, index) => (
                        <div
                          key={player.id}
                          className="flex items-center justify-between p-2 rounded-lg bg-muted/50"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center text-xs font-bold text-green-600">
                              {index + 1}
                            </div>
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={player.team.jersey} />
                            </Avatar>
                            <div>
                              <div className="font-semibold text-sm">
                                {player.name}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {player.team.name} • {player.position}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-green-600">
                              +{netTransfers.toLocaleString()}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {Number(player.currentPrice)}M
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-600">
                    <TrendingDown className="h-5 w-5" />
                    Most Transferred Out
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {transferData
                      .filter((p) => p.netTransfers < 0)
                      .slice(0, 5)
                      .map(({ player, netTransfers }, index) => (
                        <div
                          key={player.id}
                          className="flex items-center justify-between p-2 rounded-lg bg-muted/50"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center text-xs font-bold text-red-600">
                              {index + 1}
                            </div>
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={player.team.jersey} />
                            </Avatar>
                            <div>
                              <div className="font-semibold text-sm">
                                {player.name}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {player.team.name} • {player.position}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-red-600">
                              {netTransfers.toLocaleString()}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {Number(player.currentPrice)}M
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

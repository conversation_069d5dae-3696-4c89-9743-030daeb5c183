"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import PitchView from "@/components/PitchView";
import { Player } from "@prisma/client";
import {
  Trophy,
  TrendingUp,
  TrendingDown,
  Users,
  RefreshCw,
  Star,
  Crown,
  ArrowRight,
  Target,
  Award,
  Calendar,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";

interface RoundStats {
  roundNumber: number;
  status: "active" | "complete" | "upcoming";
  userScore: number;
  averageScore: number;
  highestScore: number;
  transfersMade: number;
  wildcardsPlayed: number;
  mostCaptained: string;
}

interface TransferData {
  playerId: string;
  playerName: string;
  position: string;
  team: string;
  price: number;
  transfersIn: number;
  transfersOut: number;
  netTransfers: number;
}

export default function HomePageContent() {
  const { user } = useUser();
  const [roundStats, setRoundStats] = useState<RoundStats>({
    roundNumber: 34,
    status: "complete",
    userScore: 44,
    averageScore: 46,
    highestScore: 120,
    transfersMade: 47072,
    wildcardsPlayed: 613,
    mostCaptained: "Mitrović",
  });

  const [dreamTeam, setDreamTeam] = useState<Player[]>([]);
  const [transferData, setTransferData] = useState<TransferData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API calls
    const loadData = async () => {
      setIsLoading(true);
      
      // Mock dream team data
      const mockDreamTeam: Player[] = [
        { id: "1", name: "Ben Cherifia", position: "GK", teamId: "EST", currentPrice: 4.5 },
        { id: "2", name: "Khalil Chemmam", position: "DEF", teamId: "EST", currentPrice: 5.2 },
        { id: "3", name: "Mohamed Ali Ben Romdhane", position: "DEF", teamId: "EST", currentPrice: 5.8 },
        { id: "4", name: "Raed Bouchniba", position: "DEF", teamId: "EST", currentPrice: 5.1 },
        { id: "5", name: "Hamza Mathlouthi", position: "DEF", teamId: "EST", currentPrice: 4.9 },
        { id: "6", name: "Anice Badri", position: "MID", teamId: "EST", currentPrice: 7.2 },
        { id: "7", name: "Mohamed Ali Yaâkoubi", position: "MID", teamId: "EST", currentPrice: 6.8 },
        { id: "8", name: "Haythem Jouini", position: "MID", teamId: "EST", currentPrice: 6.5 },
        { id: "9", name: "Aleksandar Mitrović", position: "ATK", teamId: "EST", currentPrice: 9.8 },
        { id: "10", name: "Rodrigue Kossi", position: "ATK", teamId: "CSS", currentPrice: 8.2 },
        { id: "11", name: "Bilel Mejri", position: "ATK", teamId: "CA", currentPrice: 7.5 },
      ] as Player[];

      // Mock transfer data
      const mockTransferData: TransferData[] = [
        { playerId: "1", playerName: "Aleksandar Mitrović", position: "ATK", team: "EST", price: 9.8, transfersIn: 15420, transfersOut: 2341, netTransfers: 13079 },
        { playerId: "2", playerName: "Anice Badri", position: "MID", team: "EST", price: 7.2, transfersIn: 12890, transfersOut: 3456, netTransfers: 9434 },
        { playerId: "3", playerName: "Rodrigue Kossi", position: "ATK", team: "CSS", price: 8.2, transfersIn: 11234, transfersOut: 4567, netTransfers: 6667 },
        { playerId: "4", playerName: "Mohamed Ali Ben Romdhane", position: "DEF", team: "EST", price: 5.8, transfersIn: 9876, transfersOut: 2345, netTransfers: 7531 },
        { playerId: "5", playerName: "Bilel Mejri", position: "ATK", team: "CA", price: 7.5, transfersIn: 8765, transfersOut: 5432, netTransfers: 3333 },
        { playerId: "6", playerName: "Haythem Jouini", position: "MID", team: "EST", price: 6.5, transfersIn: 7654, transfersOut: 8901, netTransfers: -1247 },
        { playerId: "7", playerName: "Khalil Chemmam", position: "DEF", team: "EST", price: 5.2, transfersIn: 6543, transfersOut: 9876, netTransfers: -3333 },
        { playerId: "8", playerName: "Ben Cherifia", position: "GK", team: "EST", price: 4.5, transfersIn: 5432, transfersOut: 7890, netTransfers: -2458 },
      ];

      setDreamTeam(mockDreamTeam);
      setTransferData(mockTransferData);
      setIsLoading(false);
    };

    loadData();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-6">
        {/* Welcome Header */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold">
            Welcome back, {user?.firstName || "Manager"}!
          </h1>
          <p className="text-muted-foreground">
            Here's your Tunisian Fantasy League overview
          </p>
        </div>

        {/* Round Status Card */}
        <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-secondary/5">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Round {roundStats.roundNumber} Status
              </CardTitle>
              <Badge 
                variant={roundStats.status === "complete" ? "default" : "secondary"}
                className="flex items-center gap-1"
              >
                <CheckCircle className="h-3 w-3" />
                {roundStats.status === "complete" ? "Complete" : "Active"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{roundStats.userScore}</div>
                <div className="text-xs text-muted-foreground">Your Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{roundStats.averageScore}</div>
                <div className="text-xs text-muted-foreground">Average Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{roundStats.highestScore}</div>
                <div className="text-xs text-muted-foreground">Highest Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{roundStats.transfersMade.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Transfers Made</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{roundStats.wildcardsPlayed}</div>
                <div className="text-xs text-muted-foreground">Wildcards Played</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold">{roundStats.mostCaptained}</div>
                <div className="text-xs text-muted-foreground">Most Captained</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <Link href="/team">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 mx-auto mb-2 text-primary" />
                <h3 className="font-semibold text-sm">My Team</h3>
              </CardContent>
            </Card>
          </Link>
          
          <Link href="/transfers">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <RefreshCw className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                <h3 className="font-semibold text-sm">Transfers</h3>
              </CardContent>
            </Card>
          </Link>
          
          <Link href="/leagues">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <Trophy className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
                <h3 className="font-semibold text-sm">Leagues</h3>
              </CardContent>
            </Card>
          </Link>
          
          <Link href="/statistics">
            <Card className="cursor-pointer hover:shadow-md transition-all duration-200 h-full">
              <CardContent className="p-4 text-center">
                <Target className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <h3 className="font-semibold text-sm">Statistics</h3>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="dream-team" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="dream-team">Dream Team</TabsTrigger>
            <TabsTrigger value="transfers">Transfer Activity</TabsTrigger>
          </TabsList>
          
          <TabsContent value="dream-team" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-500" />
                  Round {roundStats.roundNumber} Dream Team
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PitchView
                  starters={dreamTeam}
                  onSelect={() => {}}
                  showPrice
                  showPoints
                />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="transfers" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-600">
                    <TrendingUp className="h-5 w-5" />
                    Most Transferred In
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {transferData
                      .filter(p => p.netTransfers > 0)
                      .slice(0, 5)
                      .map((player, index) => (
                        <div key={player.playerId} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center text-xs font-bold text-green-600">
                              {index + 1}
                            </div>
                            <Avatar className="h-8 w-8">
                              <AvatarImage src="/jerseys/jersey1.webp" />
                            </Avatar>
                            <div>
                              <div className="font-semibold text-sm">{player.playerName}</div>
                              <div className="text-xs text-muted-foreground">{player.team} • {player.position}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-green-600">+{player.netTransfers.toLocaleString()}</div>
                            <div className="text-xs text-muted-foreground">{player.price}M</div>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-600">
                    <TrendingDown className="h-5 w-5" />
                    Most Transferred Out
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {transferData
                      .filter(p => p.netTransfers < 0)
                      .slice(0, 5)
                      .map((player, index) => (
                        <div key={player.playerId} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center text-xs font-bold text-red-600">
                              {index + 1}
                            </div>
                            <Avatar className="h-8 w-8">
                              <AvatarImage src="/jerseys/jersey1.webp" />
                            </Avatar>
                            <div>
                              <div className="font-semibold text-sm">{player.playerName}</div>
                              <div className="text-xs text-muted-foreground">{player.team} • {player.position}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-red-600">{player.netTransfers.toLocaleString()}</div>
                            <div className="text-xs text-muted-foreground">{player.price}M</div>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

"use client";

import { PlayerInfo } from "@/types/types";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trophy, Users, TrendingUp, Star } from "lucide-react";

interface HomePageContentProps {
  players: PlayerInfo[];
}

export default function HomePageContent({ players }: HomePageContentProps) {
  // Generate fake dream team from top players
  const dreamTeam = players
    .sort((a, b) => parseFloat(String(b.currentPrice)) - parseFloat(String(a.currentPrice)))
    .slice(0, 11);

  // Calculate some stats
  const totalPlayers = players.length;
  const avgPrice = players.reduce((sum, p) => sum + parseFloat(String(p.currentPrice)), 0) / totalPlayers;
  const topPlayer = players.reduce((top, player) =>
    parseFloat(String(player.currentPrice)) > parseFloat(String(top.currentPrice)) ? player : top
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent mb-4">
            Tunisia Fantasy League
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            Experience the ultimate fantasy football with Tunisian Premier League
          </p>
          <Badge variant="secondary" className="text-lg px-4 py-2">
            🚧 Preview Version - App Under Construction
          </Badge>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Players</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalPlayers}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Price</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{avgPrice.toFixed(1)}M</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Most Expensive</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{String(topPlayer.currentPrice)}M</div>
              <p className="text-xs text-muted-foreground">{topPlayer.name}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Dream Team</CardTitle>
              <Trophy className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dreamTeam.length}</div>
              <p className="text-xs text-muted-foreground">Top Players</p>
            </CardContent>
          </Card>
        </div>

        {/* Dream Team Preview */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Sample Dream Team
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dreamTeam.map((player) => (
                <div key={player.id} className="flex items-center space-x-3 p-3 rounded-lg border">
                  <div className="flex-1">
                    <p className="font-medium">{player.name}</p>
                    <p className="text-sm text-muted-foreground">{player.position}</p>
                  </div>
                  <Badge variant="outline">{String(player.currentPrice)}M</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Coming Soon Features */}
        <Card>
          <CardHeader>
            <CardTitle>🚧 Coming Soon</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-semibold">Team Management</h4>
                <p className="text-sm text-muted-foreground">
                  Build your ultimate fantasy team with budget constraints
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Live Scoring</h4>
                <p className="text-sm text-muted-foreground">
                  Real-time points based on actual match performance
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Leagues & Competition</h4>
                <p className="text-sm text-muted-foreground">
                  Compete with friends in private leagues
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Transfer Market</h4>
                <p className="text-sm text-muted-foreground">
                  Strategic player transfers with weekly limits
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// components/TeamTable.tsx
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Player, PlayerGWStats, PlayerTotalStats } from "@prisma/client";

interface TeamTableProps {
  players: Player[];
  onSelect: (p: Player) => void;
  captainId?: string;
  viceCaptainId?: string;
  totalStats?: PlayerTotalStats | null;
  gwStats?: PlayerGWStats | null;
}

export default function TeamTable({
  players,
  totalStats = null,
  gwStats = null,
  onSelect,
  captainId,
  viceCaptainId,
}: TeamTableProps) {
  const renderStatsColumns = (p: Player, totalStats = null, gwStats = null) => {
    // Fix this
    const stats = {
      points: 0,
      minutesPlayed: 0,
      goalsScored: 0,
      assists: 0,
      cleanSheets: 0,
      goalsConceded: 0,
      ownGoals: 0,
      penaltiesSaved: 0,
      penaltiesMissed: 0,
      yellowCards: 0,
      redCards: 0,
      saves: 0,
      bonus: 0,
      bps: 0,
    };

    return (
      <TooltipProvider>
        <TableCell>
          <div className="flex flex-col">
            <span className="font-semibold">{p.name}</span>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <span>{p.id === captainId ? "C" : p.id === viceCaptainId ? "V" : ""}</span>
              <Avatar className="h-4 w-4">
              {/* TODO: Get team abbr and logo from p.teamId */} 
                <AvatarImage src={p.teamId} />
              </Avatar>
              <span>{`${p.teamId}/${p.position}`}</span>
            </div>
          </div>
        </TableCell>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.points}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Points</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.minutesPlayed}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Minutes Played</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.goalsScored}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Goals Scored</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.assists}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Assists</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.cleanSheets}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Clean Sheets</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.goalsConceded}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Goals Conceded</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.ownGoals}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Own Goals</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.penaltiesSaved}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Penalties Saved</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.penaltiesMissed}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Penalties Missed</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.yellowCards}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Yellow Cards</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.redCards}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Red Cards</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.saves}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Saves</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.bonus}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Bonus</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.bps}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Bonus Points System</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  return (
    <div className="overflow-x-auto border border-border rounded-lg bg-card">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted">
            <TableHead className="text-foreground">Player</TableHead>
            <TableHead>Pts</TableHead>
            <TableHead>MP</TableHead>
            <TableHead>GS</TableHead>
            <TableHead>A</TableHead>
            <TableHead>CS</TableHead>
            <TableHead>GC</TableHead>
            <TableHead>OG</TableHead>
            <TableHead>PS</TableHead>
            <TableHead>PM</TableHead>
            <TableHead>YC</TableHead>
            <TableHead>RC</TableHead>
            <TableHead>S</TableHead>
            <TableHead>B</TableHead>
            <TableHead>BPS</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {players.map((p) => (
            <TableRow
              key={p.id}
              onClick={() => onSelect(p)}
              className="cursor-pointer hover:bg-muted"
            >
              {renderStatsColumns(p)}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

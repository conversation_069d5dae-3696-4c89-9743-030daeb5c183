// components/TeamTable.tsx
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { PlayerGWStats, PlayerTotalStats } from "@prisma/client";
import { Crown, DollarSign, TrendingUp } from "lucide-react";
import { PlayerInfo } from "@/types/types";

interface TeamTableProps {
  players: PlayerInfo[];
  onSelect: (p: PlayerInfo) => void;
  captainId?: string;
  viceCaptainId?: string;
  totalStats?: PlayerTotalStats | null;
  gwStats?: PlayerGWStats | null;
  showMobileCards?: boolean;
}

export default function TeamTable({
  players,
  onSelect,
  captainId,
  viceCaptainId,
}: TeamTableProps) {
  // Mobile card view component
  const MobilePlayerCard = ({ player }: { player: PlayerInfo }) => {
    const role =
      player.id === captainId ? "C" : player.id === viceCaptainId ? "V" : null;

    return (
      <Card
        className="cursor-pointer hover:shadow-md transition-all duration-200 border-border/50"
        onClick={() => onSelect(player)}
      >
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 flex-1">
              <Avatar className="h-10 w-10">
                <AvatarImage src={player.team.jersey} />
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold text-sm truncate">
                    {player.name}
                  </h4>
                  {role && (
                    <Badge
                      variant={role === "C" ? "default" : "secondary"}
                      className="text-xs px-1.5 py-0.5 h-auto"
                    >
                      {role === "C" && <Crown className="w-2 h-2 mr-0.5" />}
                      {role}
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-2 mt-1">
                  <Badge
                    variant="outline"
                    className="text-xs px-1.5 py-0.5 h-auto"
                  >
                    {player.position}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    {player.team.abbr}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex flex-col items-end gap-1">
              <div className="flex items-center gap-1 text-xs font-semibold text-primary">
                <DollarSign className="w-3 h-3" />
                {Number(player.currentPrice)}M
              </div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <TrendingUp className="w-3 h-3" />0 pts
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Responsive view: cards on mobile, table on desktop
  return (
    <>
      {/* Mobile Card View */}
      <div className="block sm:hidden space-y-2">
        {players.map((player) => (
          <MobilePlayerCard key={player.id} player={player} />
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="hidden sm:block overflow-x-auto border border-border rounded-lg bg-card">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted">
              <TableHead className="text-foreground">Player</TableHead>
              <TableHead className="text-center">Price</TableHead>
              <TableHead className="text-center">Points</TableHead>
              <TableHead className="text-center">Position</TableHead>
              <TableHead className="text-center">Team</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {players.map((player) => {
              const role =
                player.id === captainId
                  ? "C"
                  : player.id === viceCaptainId
                  ? "V"
                  : null;

              return (
                <TableRow
                  key={player.id}
                  onClick={() => onSelect(player)}
                  className="cursor-pointer hover:bg-muted transition-colors"
                >
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={player.team.jersey} />
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">{player.name}</span>
                          {role && (
                            <Badge
                              variant={role === "C" ? "default" : "secondary"}
                              className="text-xs px-1.5 py-0.5 h-auto"
                            >
                              {role === "C" && (
                                <Crown className="w-2 h-2 mr-0.5" />
                              )}
                              {role}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="outline" className="font-semibold">
                      {Number(player.currentPrice)}M
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-sm">0</span>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="secondary">{player.position}</Badge>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-sm">{player.team.abbr}</span>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </>
  );
}

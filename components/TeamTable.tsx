// components/TeamTable.tsx
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Player, PlayerGWStats, PlayerTotalStats } from "@prisma/client";
import { Star, Crown, DollarSign, TrendingUp } from "lucide-react";

interface TeamTableProps {
  players: Player[];
  onSelect: (p: Player) => void;
  captainId?: string;
  viceCaptainId?: string;
  totalStats?: PlayerTotalStats | null;
  gwStats?: PlayerGWStats | null;
  showMobileCards?: boolean;
}

export default function TeamTable({
  players,
  totalStats = null,
  gwStats = null,
  onSelect,
  captainId,
  viceCaptainId,
  showMobileCards = false,
}: TeamTableProps) {
  const renderStatsColumns = (p: Player, totalStats = null, gwStats = null) => {
    // Fix this
    const stats = {
      points: 0,
      minutesPlayed: 0,
      goalsScored: 0,
      assists: 0,
      cleanSheets: 0,
      goalsConceded: 0,
      ownGoals: 0,
      penaltiesSaved: 0,
      penaltiesMissed: 0,
      yellowCards: 0,
      redCards: 0,
      saves: 0,
      bonus: 0,
      bps: 0,
    };

    return (
      <TooltipProvider>
        <TableCell>
          <div className="flex flex-col">
            <span className="font-semibold">{p.name}</span>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <span>{p.id === captainId ? "C" : p.id === viceCaptainId ? "V" : ""}</span>
              <Avatar className="h-4 w-4">
              {/* TODO: Get team abbr and logo from p.teamId */} 
                <AvatarImage src={p.teamId} />
              </Avatar>
              <span>{`${p.teamId}/${p.position}`}</span>
            </div>
          </div>
        </TableCell>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.points}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Points</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.minutesPlayed}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Minutes Played</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.goalsScored}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Goals Scored</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.assists}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Assists</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.cleanSheets}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Clean Sheets</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.goalsConceded}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Goals Conceded</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.ownGoals}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Own Goals</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.penaltiesSaved}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Penalties Saved</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.penaltiesMissed}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Penalties Missed</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.yellowCards}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Yellow Cards</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.redCards}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Red Cards</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.saves}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Saves</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.bonus}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Bonus</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <TableCell>{stats.bps}</TableCell>
          </TooltipTrigger>
          <TooltipContent>
            <p>Bonus Points System</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  // Mobile card view component
  const MobilePlayerCard = ({ player }: { player: Player }) => {
    const role = player.id === captainId ? "C" : player.id === viceCaptainId ? "V" : null;

    return (
      <Card
        className="cursor-pointer hover:shadow-md transition-all duration-200 border-border/50"
        onClick={() => onSelect(player)}
      >
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 flex-1">
              <Avatar className="h-10 w-10">
                <AvatarImage src="/jerseys/jersey1.webp" />
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold text-sm truncate">{player.name}</h4>
                  {role && (
                    <Badge
                      variant={role === "C" ? "default" : "secondary"}
                      className="text-xs px-1.5 py-0.5 h-auto"
                    >
                      {role === "C" && <Crown className="w-2 h-2 mr-0.5" />}
                      {role}
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-auto">
                    {player.position}
                  </Badge>
                  <span className="text-xs text-muted-foreground">{player.teamId}</span>
                </div>
              </div>
            </div>

            <div className="flex flex-col items-end gap-1">
              <div className="flex items-center gap-1 text-xs font-semibold text-primary">
                <DollarSign className="w-3 h-3" />
                {player.currentPrice.toFixed(1)}M
              </div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <TrendingUp className="w-3 h-3" />
                0 pts
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Responsive view: cards on mobile, table on desktop
  return (
    <>
      {/* Mobile Card View */}
      <div className="block sm:hidden space-y-2">
        {players.map((player) => (
          <MobilePlayerCard key={player.id} player={player} />
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="hidden sm:block overflow-x-auto border border-border rounded-lg bg-card">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted">
              <TableHead className="text-foreground">Player</TableHead>
              <TableHead className="text-center">Price</TableHead>
              <TableHead className="text-center">Points</TableHead>
              <TableHead className="text-center">Position</TableHead>
              <TableHead className="text-center">Team</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {players.map((player) => {
              const role = player.id === captainId ? "C" : player.id === viceCaptainId ? "V" : null;

              return (
                <TableRow
                  key={player.id}
                  onClick={() => onSelect(player)}
                  className="cursor-pointer hover:bg-muted transition-colors"
                >
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="/jerseys/jersey1.webp" />
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">{player.name}</span>
                          {role && (
                            <Badge
                              variant={role === "C" ? "default" : "secondary"}
                              className="text-xs px-1.5 py-0.5 h-auto"
                            >
                              {role === "C" && <Crown className="w-2 h-2 mr-0.5" />}
                              {role}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="outline" className="font-semibold">
                      {player.currentPrice.toFixed(1)}M
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-sm">0</span>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="secondary">{player.position}</Badge>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-sm">{player.teamId}</span>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </>
  );
}

"use client";

import { useEffect } from 'react';
import { useData } from '@/hooks/useData';

interface DataProviderProps {
  children: React.ReactNode;
}

export default function DataProvider({ children }: DataProviderProps) {
  const { fetchData, isLoading } = useData();

  useEffect(() => {
    // Load all core data when the app starts
    fetchData();
  }, [fetchData]);

  // You can optionally show a loading state while initial data is being fetched
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <>{children}</>;
}

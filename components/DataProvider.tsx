"use client";

import { useEffect, useState } from "react";
import { useFantasy } from "@/hooks/useFantasy";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface DataProviderProps {
  children: React.ReactNode;
}

export default function DataProvider({ children }: DataProviderProps) {
  const userId = useUser().user?.id;
  const router = useRouter();
  const [isUserOnboarded, setIsUserOnboarded] = useState(false);
  const {
    activeGameweek,
    gameweeks,
    fetchGameweeks,
    fetchActiveGameweek,
    players,
    fetchPlayers,
    teams,
    fetchTeams,
    fetchTeam,
    isLoading,
  } = useFantasy();

  // Initialize gameweeks and active gameweek
  useEffect(() => {
    if (gameweeks.length === 0) {
      fetchGameweeks();
    }
  }, [fetchGameweeks, gameweeks.length]);

  useEffect(() => {
    if (!activeGameweek) {
      fetchActiveGameweek();
    }
  }, [fetchActiveGameweek, activeGameweek]);

  // Initialize data
  useEffect(() => {
    if (players.length === 0) {
      fetchPlayers();
    }
  }, [fetchPlayers, players.length]);

  useEffect(() => {
    if (teams.length === 0) {
      fetchTeams();
    }
  }, [fetchTeams, teams.length]);

  // Fetch user team when we have all required data
  useEffect(() => {
    if (!userId) return;
    if (!activeGameweek) return;
    if (players.length === 0) return;
    if (teams.length === 0) return;

    //  move the onboarding logic here
    if (userId && activeGameweek && players.length > 0 && teams.length > 0) {
      // Check if user has completed onboarding
      if (!isUserOnboarded) {
        fetch(`/api/user/${userId}`)
          .then((response) => {
            if (!response.ok) {
              setIsUserOnboarded(false);
              fetchTeam(userId, activeGameweek.gameWeek.id, true);
              router.push("/onboarding");
            }
            return response.json();
          })
          .then((user) => {
            if (!user.hasOnboarded) {
              // Redirect to onboarding page
              setIsUserOnboarded(false);
              fetchTeam(userId, activeGameweek.gameWeek.id, true);
              router.push("/onboarding");
            } else {
              // Fetch user team
              setIsUserOnboarded(true);
              fetchTeam(userId, activeGameweek.gameWeek.id);
            }
          })
          .catch((error) => {
            setIsUserOnboarded(false);
            fetchTeam(userId, activeGameweek.gameWeek.id, true);
            router.push("/onboarding");
            toast
          });
      }
      else {
        // Fetch user team
        fetchTeam(userId, activeGameweek.gameWeek.id);
      }
    }
  }, [userId, activeGameweek, players.length, teams.length, isUserOnboarded, fetchTeam, router]);

  // You can optionally show a loading state while initial data is being fetched
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <>{children}</>;
}

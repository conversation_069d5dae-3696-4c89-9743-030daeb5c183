// app/team/[userId]/[round]/components/StatsPanel.tsx
"use client";

interface StatsPanelProps {
  avgPoints: number;
  highestPoints: number;
  totalGWPoints: number;
  roundRank: number;
  transfersRemaining: number;
}

export default function StatsPanel({
  avgPoints,
  highestPoints,
  totalGWPoints,
  roundRank,
  transfersRemaining,
}: StatsPanelProps) {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4 bg-card p-4 rounded-xl text-center">
      {/* For small screens: top row */}
      <div className="sm:order-1">
        <p className="text-xs text-muted-foreground">Average Pts</p>
        <p className="font-semibold text-lg">{avgPoints}</p>
      </div>
      
      <div className="sm:order-5 md:order-4">
        <p className="text-xs text-muted-foreground">RD Rank</p>
        <p className="font-semibold text-lg">{roundRank}</p>
      </div>
      
      {/* For small screens: middle row (points) - spans 2 columns on sm */}
      <div className="col-span-2 sm:col-span-3 md:col-span-1 sm:order-3">
        <p className="text-2xl font-extrabold text-muted-foreground">Points</p>
        <p className="text-2xl font-bold">{totalGWPoints}</p>
      </div>
      
      {/* For small screens: bottom row */}
      <div className="sm:order-2">
        <p className="text-xs text-muted-foreground">Highest Pts</p>
        <p className="font-semibold text-lg">{highestPoints}</p>
      </div>
      
      <div className="sm:order-4 md:order-5">
        <p className="text-xs text-muted-foreground">Transfers</p>
        <p className="font-semibold text-lg">{transfersRemaining}</p>
      </div>
    </div>
  );
}

// app/team/[userId]/[round]/components/StatsPanel.tsx
"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp,
  Trophy,
  Target,
  Medal,
  RefreshCw
} from "lucide-react";

interface StatsPanelProps {
  avgPoints: number;
  highestPoints: number;
  totalGWPoints: number;
  roundRank: number;
  transfersRemaining: number;
}

export default function StatsPanel({
  avgPoints,
  highestPoints,
  totalGWPoints,
  roundRank,
  transfersRemaining,
}: StatsPanelProps) {
  return (
    <Card className="w-full shadow-lg border-0 bg-gradient-to-r from-card to-card/80">
      <CardContent className="p-6">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-6">
          {/* Average Points */}
          <div className="flex flex-col items-center space-y-2 sm:order-1">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-blue-500" />
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Average
              </span>
            </div>
            <div className="text-2xl font-bold text-foreground">{avgPoints}</div>
            <Badge variant="outline" className="text-xs">pts</Badge>
          </div>

          {/* Round Rank */}
          <div className="flex flex-col items-center space-y-2 sm:order-5 md:order-4">
            <div className="flex items-center gap-2">
              <Medal className="h-4 w-4 text-orange-500" />
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Rank
              </span>
            </div>
            <div className="text-2xl font-bold text-foreground">#{roundRank}</div>
            <Badge variant="outline" className="text-xs">round</Badge>
          </div>

          {/* Total Points - Featured */}
          <div className="col-span-2 sm:col-span-3 md:col-span-1 sm:order-3">
            <Card className="bg-primary/5 border-primary/20">
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Trophy className="h-5 w-5 text-primary" />
                  <span className="text-sm font-semibold text-primary uppercase tracking-wide">
                    Total Points
                  </span>
                </div>
                <div className="text-4xl font-extrabold text-primary mb-2">
                  {totalGWPoints}
                </div>
                <Badge className="bg-primary text-primary-foreground">
                  This Round
                </Badge>
              </CardContent>
            </Card>
          </div>

          {/* Highest Points */}
          <div className="flex flex-col items-center space-y-2 sm:order-2">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-green-500" />
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Highest
              </span>
            </div>
            <div className="text-2xl font-bold text-foreground">{highestPoints}</div>
            <Badge variant="outline" className="text-xs">pts</Badge>
          </div>

          {/* Transfers Remaining */}
          <div className="flex flex-col items-center space-y-2 sm:order-4 md:order-5">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4 text-purple-500" />
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Transfers
              </span>
            </div>
            <div className="text-2xl font-bold text-foreground">{transfersRemaining}</div>
            <Badge
              variant={transfersRemaining > 0 ? "default" : "destructive"}
              className="text-xs"
            >
              remaining
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

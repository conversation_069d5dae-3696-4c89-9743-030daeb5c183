import Link from "next/link";
import { InstagramIcon, FacebookIcon } from "lucide-react";

export default function Footer() {
  return (
    <footer className="w-full border-t border-border/40 bg-background">
      <div className="container flex flex-col items-center justify-between gap-4 py-6 md:h-16 md:flex-row md:py-0">
        <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            <span className="font-medium">TFL Alpha</span> - This is an early
            preview version.
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Link
            href=""
            target="_blank"
            rel="noreferrer"
            className="text-sm font-medium underline underline-offset-4 hover:text-primary"
          >
            Privacy Policy
          </Link>
          <Link
            href=""
            target="_blank"
            rel="noreferrer"
            className="text-sm font-medium underline underline-offset-4 hover:text-primary"
          >
            Terms of Service
          </Link>
          <Link
            href=""
            target="_blank"
            rel="noreferrer"
            className="text-sm font-medium underline underline-offset-4 hover:text-primary"
          >
            Contact Us
          </Link>
          <Link
            href=""
            target="_blank"
            rel="noreferrer"
            className="text-sm font-medium underline underline-offset-4 hover:text-primary"
          >
            FAQ
          </Link>
          <Link
            href=""
            target="_blank"
            rel="noreferrer"
            className="text-sm font-medium underline underline-offset-4 hover:text-primary"
          >
            About Us
          </Link>
          <Link
            href="https://www.instagram.com/tfl/"
            target="_blank"
            rel="noreferrer"
            className="text-sm font-medium underline underline-offset-4 hover:text-primary"
          >
            <InstagramIcon className="h-4 w-4" />
          </Link>
          <Link
            href="https://www.facebook.com/tfl"
            target="_blank"
            rel="noreferrer"
            className="text-sm font-medium underline underline-offset-4 hover:text-primary"
          >
            <FacebookIcon className="h-4 w-4" />
          </Link>
          <p className="text-center text-sm text-muted-foreground md:text-left">
            &copy; {new Date().getFullYear()} TFL. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}

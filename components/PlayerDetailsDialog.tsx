"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  Trophy,
  Target,
  Clock,
  Shield,
  AlertTriangle,
  TrendingUp,
  ArrowUpDown
} from "lucide-react";
import { PlayerGWStats } from "@/hooks/usePlayerGWStats";

interface PlayerDetailsDialogProps {
  player: PlayerGWStats | null;
  isOpen: boolean;
  onClose: () => void;
  isCaptain?: boolean;
  isViceCaptain?: boolean;
}

export default function PlayerDetailsDialog({ 
  player, 
  isOpen, 
  onClose, 
  isCaptain = false,
  isViceCaptain = false 
}: PlayerDetailsDialogProps) {
  if (!player) return null;

  const getPositionColor = (position: string) => {
    switch (position) {
      case "GK": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";
      case "DEF": return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      case "MID": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "ATK": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  const finalPoints = isCaptain ? player.points * 2 : player.points;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={player.player.team.jersey} alt={player.player.team.name} />
              <AvatarFallback>{player.player.team.abbr}</AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center gap-2">
                <span className="text-xl font-bold">{player.player.name}</span>
                {isCaptain && (
                  <Badge variant="default" className="bg-yellow-500 text-white">
                    Captain
                  </Badge>
                )}
                {isViceCaptain && (
                  <Badge variant="outline" className="border-yellow-500 text-yellow-600">
                    Vice Captain
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2 mt-1">
                <Badge className={getPositionColor(player.player.position)}>
                  {player.player.position}
                </Badge>
                <span className="text-sm text-muted-foreground">{player.player.team.name}</span>
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Points Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-yellow-500" />
                Points Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{player.points}</div>
                  <div className="text-sm text-muted-foreground">Base Points</div>
                </div>
                {isCaptain && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{finalPoints}</div>
                    <div className="text-sm text-muted-foreground">Captain Points (2x)</div>
                  </div>
                )}
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{player.bonus}</div>
                  <div className="text-sm text-muted-foreground">Bonus</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{player.bps}</div>
                  <div className="text-sm text-muted-foreground">BPS</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-green-500" />
                Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">Minutes: {player.minutesPlayed}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Goals: {player.goalsScored}</span>
                </div>
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-purple-500" />
                  <span className="text-sm">Assists: {player.assists}</span>
                </div>
                {player.player.position === 'GK' && (
                  <>
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-blue-500" />
                      <span className="text-sm">Saves: {player.saves}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Trophy className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Penalties Saved: {player.penaltiesSaved}</span>
                    </div>
                  </>
                )}
                {(player.player.position === 'DEF' || player.player.position === 'GK') && (
                  <>
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Clean Sheets: {player.cleanSheets}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      <span className="text-sm">Goals Conceded: {player.goalsConceded}</span>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Disciplinary */}
          {(player.yellowCards > 0 || player.redCards > 0 || player.ownGoals > 0 || player.penaltiesMissed > 0) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  Disciplinary & Negative
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {player.yellowCards > 0 && (
                    <div className="text-center">
                      <div className="text-lg font-bold text-yellow-600">{player.yellowCards}</div>
                      <div className="text-sm text-muted-foreground">Yellow Cards</div>
                    </div>
                  )}
                  {player.redCards > 0 && (
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-600">{player.redCards}</div>
                      <div className="text-sm text-muted-foreground">Red Cards</div>
                    </div>
                  )}
                  {player.ownGoals > 0 && (
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-600">{player.ownGoals}</div>
                      <div className="text-sm text-muted-foreground">Own Goals</div>
                    </div>
                  )}
                  {player.penaltiesMissed > 0 && (
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-600">{player.penaltiesMissed}</div>
                      <div className="text-sm text-muted-foreground">Penalties Missed</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Market Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowUpDown className="h-5 w-5 text-blue-500" />
                Market Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">${player.price}M</div>
                  <div className="text-sm text-muted-foreground">Price</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{player.owners.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">Owners</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">{player.transfersIn.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">Transfers In</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-red-600">{player.transfersOut.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">Transfers Out</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}

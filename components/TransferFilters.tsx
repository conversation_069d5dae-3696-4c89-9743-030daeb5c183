// components/TransferFilters.tsx

import React from "react";
import { Position } from "@prisma/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Filter, Users, DollarSign } from "lucide-react";
import { PlayerInfo } from "@/types/types";

interface TransferFiltersProps {
  allPlayers: PlayerInfo[];
  selectedPosition: Position | null;
  onPositionChange: (pos: Position | null) => void;
  selectedTeam: string;
  onTeamChange: (team: string | null) => void;
  onPriceChange: (number: number) => void;
  maxPossiblePrice?: number;
  selectedPrice: number;
  availableTeams?: string[];
}

export default function TransferFilters({
  allPlayers,
  selectedPosition,
  selectedTeam,
  selectedPrice,
  onPositionChange,
  onTeamChange,
  onPriceChange,
  maxPossiblePrice = 20,
  availableTeams = [],
}: TransferFiltersProps) {
  // Use provided teams or extract from players
  const teams =
    availableTeams.length > 0
      ? availableTeams
      : Array.from(new Set(allPlayers.map((p) => p.teamId)));

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Filter className="h-5 w-5" />
          Filters
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Position Filter */}
        <div className="space-y-3">
          <Label className="text-sm font-semibold flex items-center gap-2">
            <Users className="h-4 w-4" />
            Position
          </Label>
          <div className="grid grid-cols-5 gap-2">
            {[
              { value: null, label: "ALL" },
              { value: "GK", label: "GK" },
              { value: "DEF", label: "DEF" },
              { value: "MID", label: "MID" },
              { value: "ATK", label: "ATK" },
            ].map((position) => (
              <Button
                key={position.label}
                variant={
                  selectedPosition === position.value ? "default" : "outline"
                }
                size="sm"
                onClick={() =>
                  onPositionChange(position.value as Position | null)
                }
                className="text-xs font-medium"
              >
                {position.label}
              </Button>
            ))}
          </div>
          {selectedPosition && (
            <Badge variant="secondary" className="text-xs">
              Showing {selectedPosition} players
            </Badge>
          )}
        </div>

        {/* Team Filter */}
        <div className="space-y-3">
          <Label className="text-sm font-semibold">Team</Label>
          <Select
            value={selectedTeam}
            onValueChange={(value) => onTeamChange(value || null)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="All teams" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All teams</SelectItem>
              {teams.map((club) => (
                <SelectItem key={club} value={club}>
                  {club}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {selectedTeam && (
            <Badge variant="secondary" className="text-xs">
              Showing {selectedTeam} players
            </Badge>
          )}
        </div>

        {/* Price Range Filter */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-semibold flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Max Price
            </Label>
            <Badge variant="outline" className="font-semibold">
              {selectedPrice.toFixed(1)}M
            </Badge>
          </div>
          <Slider
            value={[selectedPrice]}
            onValueChange={(value) => onPriceChange(value[0])}
            max={maxPossiblePrice}
            min={0}
            step={0.5}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>0M</span>
            <span>{maxPossiblePrice}M</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// components/TransferFilters.tsx

import React from "react";
import { <PERSON><PERSON><PERSON>, Player } from "@prisma/client";

interface TransferFiltersProps {
  allPlayers: Player[];
  selectedPosition: Position | null;
  onPositionChange: (pos: Position | null) => void;
  selectedTeam: string | null;
  onTeamChange: (team: string | null) => void;
  onPriceChange: (number: number) => void;
  maxPossiblePrice?: number;
  selectedPrice: number;
  availableTeams?: string[];
}

export default function TransferFilters({
  allPlayers,
  selectedPosition,
  selectedTeam,
  selectedPrice,
  onPositionChange,
  onTeamChange,
  onPriceChange,
  maxPossiblePrice = 20,
  availableTeams = [],
}: TransferFiltersProps) {
  // Use provided teams or extract from players
  const teams =
    availableTeams.length > 0
      ? availableTeams
      : Array.from(new Set(allPlayers.map((p) => p.teamId)));

  return (
    <div className="space-y-4 p-4 bg-card rounded-lg border border-border">
      {/* Position Filter */}
      <div>
        <label className="block text-sm font-medium text-foreground mb-1">
          Position
        </label>
        <div className="grid grid-cols-5 gap-1">
          <button
            onClick={() => onPositionChange(null)}
            className={`px-2 py-1 text-xs rounded-md ${
              selectedPosition === null
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground hover:bg-muted/80"
            }`}
          >
            ALL
          </button>
          <button
            onClick={() => onPositionChange("GK")}
            className={`px-2 py-1 text-xs rounded-md ${
              selectedPosition === "GK"
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground hover:bg-muted/80"
            }`}
          >
            GK
          </button>
          <button
            onClick={() => onPositionChange("DEF")}
            className={`px-2 py-1 text-xs rounded-md ${
              selectedPosition === "DEF"
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground hover:bg-muted/80"
            }`}
          >
            DEF
          </button>
          <button
            onClick={() => onPositionChange("MID")}
            className={`px-2 py-1 text-xs rounded-md ${
              selectedPosition === "MID"
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground hover:bg-muted/80"
            }`}
          >
            MID
          </button>
          <button
            onClick={() => onPositionChange("ATK")}
            className={`px-2 py-1 text-xs rounded-md ${
              selectedPosition === "ATK"
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground hover:bg-muted/80"
            }`}
          >
            ATK
          </button>
        </div>
      </div>

      {/* Team Filter */}
      <div>
        <label className="block text-sm font-medium text-foreground mb-1">
          Team
        </label>
        <select
          value={selectedTeam || ""}
          onChange={(e) => onTeamChange(e.target.value || null)}
          className="w-full rounded-md border border-border bg-background text-foreground px-2 py-1"
        >
          <option value="">All</option>
          {teams.map((club) => (
            <option key={club} value={club}>
              {club}
            </option>
          ))}
        </select>
      </div>

      {/* Price Range Filter */}
      <div>
        <label className="block text-sm font-medium text-foreground mb-1">
          Max Price: {selectedPrice.toFixed(1)}M
        </label>
        <div className="flex gap-2 items-center">
          <input
            type="range"
            min="0"
            max={maxPossiblePrice}
            step="0.5"
            value={selectedPrice}
            onChange={(e) => onPriceChange(parseFloat(e.target.value))}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}

// components/PitchView.tsx
import React from "react";
import Image from "next/image";
import PlayerCard from "./PlayerCard";
import { PlayerInfo } from "@/types/types";

interface PitchViewProps {
  starters: PlayerInfo[];
  subs?: PlayerInfo[];
  removedPlayers?: PlayerInfo[];
  captainId?: string | null;
  viceCaptainId?: string | null;
  onSelect: (p: PlayerInfo) => void;
  onRemove?: (p: PlayerInfo) => void;
  showRemove?: boolean;
  showNextFixture?: boolean;
  showPrice?: boolean;
  showPoints?: boolean;
  showRole?: boolean;
  showPosition?: boolean;
  isBuildingTeam?: boolean;
}

export default function PitchView({
  starters,
  subs,
  removedPlayers = [],
  captainId,
  viceCaptainId,
  onSelect,
  onRemove,
  showRemove = false,
  showNextFixture = false,
  showPrice = false,
  showPoints = false,
  showRole = false,
  showPosition = false,
}: PitchViewProps) {
  // Group players by position
  const getByPos = (position: PlayerInfo["position"]) =>
    starters.filter((p) => p.position === position);

  // Render a row of players at a given "top" percentage
  const renderLine = (players: PlayerInfo[], top: string) => {
    const count = players.length;
    return players.map((player, idx) => {
      // Spread horizontally by (count + 1), then multiply by (idx + 1)
      const left = `${(100 / (count + 1)) * (idx + 1)}%`;
      const isRemoved = removedPlayers.some((p) => p.id === player.id);

      // Calculate responsive sizing based on screen size and player count
      const maxPlayersInRow = Math.max(
        getByPos("GK").length,
        getByPos("DEF").length,
        getByPos("MID").length,
        getByPos("ATK").length
      );

      return (
        <div
          key={player.id}
          className="absolute transform -translate-x-1/2 -translate-y-1/2"
          style={{
            top,
            left,
            // Mobile-first responsive sizing
            width: `clamp(45px, ${85 / (maxPlayersInRow + 1)}vw, ${
              300 / (maxPlayersInRow + 1)
            }px)`,
            height: `clamp(55px, ${100 / 6}vh, ${400 / 6}px)`,
            maxWidth: "80px",
            maxHeight: "90px",
          }}
        >
          <PlayerCard
            player={player}
            isRemoved={isRemoved}
            captainId={captainId}
            viceCaptainId={viceCaptainId}
            showNextFixture={showNextFixture}
            showPrice={showPrice}
            showPoints={showPoints}
            showRole={showRole}
            showPosition={showPosition}
            showRemove={showRemove}
            onClick={() => onSelect(player)}
            onRemove={onRemove ? () => onRemove(player) : undefined}
          />
        </div>
      );
    });
  };

  return (
    <div className="w-full flex flex-col items-center justify-center px-2 sm:px-4">
      {/* Pitch container */}
      <div
        className="
          relative
          w-full
          max-w-[320px] sm:max-w-[400px] md:max-w-[500px]
          aspect-[9/16]
          mb-4 sm:mb-6
          mx-auto
          overflow-hidden
          rounded-xl
          border-2 border-border/80
          bg-gradient-to-b from-green-50 to-green-100
          dark:from-green-900/20 dark:to-green-800/20
          backdrop-blur-sm
          shadow-lg
        "
      >
        {/* Underlying pitch image */}
        <Image
          src="/pitch.svg"
          alt="Football Pitch"
          fill
          priority
          sizes="(max-width: 640px) 320px, (max-width: 768px) 400px, 500px"
          className="object-cover z-0 opacity-70"
        />

        {/* Pitch overlay for better contrast */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/5 z-5" />

        {/* Players on top (rows) */}
        <div className="absolute inset-0 z-10 p-2 sm:p-3">
          {renderLine(getByPos("GK"), "12%")}
          {renderLine(getByPos("DEF"), "32%")}
          {renderLine(getByPos("MID"), "52%")}
          {renderLine(getByPos("ATK"), "72%")}
        </div>
      </div>

      {/* Substitutes section (below pitch) */}
      {subs?.length ? (
        <div className="w-full max-w-[320px] sm:max-w-[400px] md:max-w-[500px] text-center px-2">
          <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-3 sm:p-4">
            <h2 className="text-sm sm:text-base font-semibold text-foreground mb-3 flex items-center justify-center gap-2">
              <span className="w-2 h-2 bg-primary rounded-full"></span>
              Substitutes
              <span className="w-2 h-2 bg-primary rounded-full"></span>
            </h2>
            <div className="grid grid-cols-4 gap-2 sm:gap-3 justify-items-center">
              {subs.slice(0, 4).map((player) => (
                <div
                  key={player.id}
                  className="w-[50px] h-[60px] sm:w-[60px] sm:h-[70px] md:w-[70px] md:h-[80px]"
                >
                  <PlayerCard
                    player={player}
                    onClick={() => onSelect(player)}
                    captainId={captainId}
                    viceCaptainId={viceCaptainId}
                    showNextFixture={showNextFixture}
                    showPrice={showPrice}
                    showPoints={showPoints}
                    showRole={showRole}
                    showPosition={showPosition}
                    showRemove={showRemove}
                    onRemove={onRemove ? () => onRemove(player) : undefined}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
}

// components/PitchView.tsx
import React from "react";
import Image from "next/image";
import PlayerCard from "./PlayerCard";
import { Player } from "@prisma/client";

interface PitchViewProps {
  starters: Player[];
  subs?: Player[];
  removedPlayers?: Player[];
  onSelect: (p: Player) => void;
  onRemove?: (p: Player) => void;
  showRemove?: boolean;
  showNextFixture?: boolean;
  showPrice?: boolean;
  showPoints?: boolean;
  showRole?: boolean;
  showPosition?: boolean;
  isBuildingTeam?: boolean;
}

export default function PitchView({
  starters,
  subs,
  removedPlayers = [],
  onSelect,
  onRemove,
  showRemove = false,
  showNextFixture = false,
  showPrice = false,
  showPoints = false,
  showRole = false,
  showPosition = false,
  isBuildingTeam = false,
}: PitchViewProps) {
  // Group players by position
  const getByPos = (position: Player["position"]) =>
    starters.filter((p) => p.position === position);

  // Render a row of players at a given "top" percentage
  const renderLine = (players: Player[], top: string) => {
    const count = players.length;
    return players.map((player, idx) => {
      // Spread horizontally by (count + 1), then multiply by (idx + 1)
      const left = `${(100 / (count + 1)) * (idx + 1)}%`;
      const isRemoved = isBuildingTeam || removedPlayers.some((p) => p.id === player.id);
      
      return (
        <div
          key={player.id}
          className="absolute transform -translate-x-1/2 -translate-y-1/2"
          style={{
            top,
            left,
            width: "calc(100% / (var(--max-players-per-row) + 2))",
            height: "calc(100% / 6)",
            minWidth: "clamp(50px, 8vw, 80px)",
            minHeight: "clamp(60px, 10vw, 90px)",
            // Set a CSS variable for max players per row (used for sizing)
            "--max-players-per-row": Math.max(
              getByPos("GK").length,
              getByPos("DEF").length,
              getByPos("MID").length,
              getByPos("ATK").length
            ),
          } as React.CSSProperties}
        >
          <PlayerCard
            player={player}
            isRemoved={isRemoved}
            showNextFixture={showNextFixture}
            showPrice={showPrice}
            showPoints={showPoints}
            showRole={showRole}
            showPosition={showPosition}
            showRemove={showRemove}
            onClick={() => onSelect(player)}
            onRemove={onRemove ? () => onRemove(player) : undefined}
          />
        </div>
      );
    });
  };

  return (
    <div className="w-full flex flex-col items-center justify-center">
      {/* Pitch container */}
      <div
        className="
          relative 
          w-full 
          max-w-[500px]      
          aspect-[9/16] 
          mb-6 
          mx-auto
          overflow-hidden 
          rounded-lg 
          border border-border/80 
          bg-card/30
          backdrop-blur-sm
          shadow-md
        "
      >
        {/* Underlying pitch image */}
        <Image
          src="/pitch.svg"
          alt="Pitch"
          fill
          priority
          sizes="(max-width: 500px) 100vw, 500px"
          className="object-cover z-0 opacity-80"
        />

        {/* Players on top (rows) */}
        <div className="absolute inset-0 z-10">
          {renderLine(getByPos("GK"), "15%")}
          {renderLine(getByPos("DEF"), "35%")}
          {renderLine(getByPos("MID"), "55%")}
          {renderLine(getByPos("ATK"), "75%")}
        </div>
      </div>

      {/* Substitutes section (below pitch) */}
      {subs?.length ? (
        <div className="w-full max-w-[500px] text-center px-2">
          <h2 className="text-base font-semibold text-foreground mb-2">
            Substitutes
          </h2>
          <div className="flex flex-wrap justify-center gap-2">
            {subs.slice(0, 4).map((player) => (
              <div key={player.id} className="w-[60px] h-[60px] sm:w-[70px] sm:h-[70px]">
                <PlayerCard
                  player={player}
                  onClick={() => onSelect(player)}
                  showNextFixture={showNextFixture}
                  showPrice={showPrice}
                  showPoints={showPoints}
                  showRole={showRole}
                  showPosition={showPosition}
                  showRemove={showRemove}
                  onRemove={onRemove ? () => onRemove(player) : undefined}
                />
              </div>
            ))}
          </div>
        </div>
      ) : null}
    </div>
  );
}

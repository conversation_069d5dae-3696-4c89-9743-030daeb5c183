"use client";

import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Player } from "@prisma/client";
import { 
  TransferSummary, 
  TransferValidationResult,
  validateTeamTransfers,
  calculateTransferSummary 
} from "@/lib/utils";
import { AlertTriangle, TrendingUp, TrendingDown, DollarSign, Zap } from "lucide-react";

interface TransferConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  currentStarters: Player[];
  currentSubs: Player[];
  originalStarters: Player[];
  originalSubs: Player[];
  isLoading?: boolean;
}

export default function TransferConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  currentStarters,
  currentSubs,
  originalStarters,
  originalSubs,
  isLoading = false,
}: TransferConfirmationDialogProps) {
  // Calculate validation and transfer summary
  const validation: TransferValidationResult = validateTeamTransfers(currentStarters, currentSubs);
  const summary: TransferSummary = calculateTransferSummary(
    currentStarters,
    currentSubs,
    originalStarters,
    originalSubs
  );

  const hasTransfers = summary.transferCount > 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Confirm Transfers
          </DialogTitle>
          <DialogDescription>
            Review your transfers and confirm the changes to your team.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Validation Errors */}
          {!validation.isValid && (
            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <h3 className="font-semibold text-destructive">Transfer Validation Failed</h3>
              </div>
              <ul className="space-y-1 text-sm text-destructive">
                {validation.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Warnings */}
          {validation.warnings.length > 0 && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                <h3 className="font-semibold text-yellow-800 dark:text-yellow-200">Warnings</h3>
              </div>
              <ul className="space-y-1 text-sm text-yellow-700 dark:text-yellow-300">
                {validation.warnings.map((warning, index) => (
                  <li key={index}>• {warning}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Transfer Summary */}
          {hasTransfers && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-card border rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-primary">{summary.transferCount}</div>
                <div className="text-xs text-muted-foreground">Transfers</div>
              </div>
              
              <div className="bg-card border rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-destructive">{summary.penaltyPoints}</div>
                <div className="text-xs text-muted-foreground">Penalty Points</div>
              </div>
              
              <div className="bg-card border rounded-lg p-3 text-center">
                <div className="text-2xl font-bold">{summary.newTeamValue}M</div>
                <div className="text-xs text-muted-foreground">Team Value</div>
              </div>
              
              <div className="bg-card border rounded-lg p-3 text-center">
                <div className={`text-2xl font-bold flex items-center justify-center gap-1 ${
                  summary.teamValueChange > 0 ? 'text-green-600' : 
                  summary.teamValueChange < 0 ? 'text-red-600' : 'text-muted-foreground'
                }`}>
                  {summary.teamValueChange > 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : summary.teamValueChange < 0 ? (
                    <TrendingDown className="h-4 w-4" />
                  ) : null}
                  {summary.teamValueChange > 0 ? '+' : ''}{summary.teamValueChange}M
                </div>
                <div className="text-xs text-muted-foreground">Value Change</div>
              </div>
            </div>
          )}

          {/* Players In */}
          {summary.playersIn.length > 0 && (
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2 text-green-600">
                <TrendingUp className="h-4 w-4" />
                Players In ({summary.playersIn.length})
              </h3>
              <div className="space-y-2">
                {summary.playersIn.map((player) => (
                  <div key={player.id} className="flex items-center gap-3 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/jerseys/jersey1.webp" />
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-medium">{player.name}</div>
                      <div className="text-sm text-muted-foreground">{player.teamId}</div>
                    </div>
                    <Badge variant="outline">{player.position}</Badge>
                    <div className="font-semibold">{player.currentPrice}M</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Players Out */}
          {summary.playersOut.length > 0 && (
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2 text-red-600">
                <TrendingDown className="h-4 w-4" />
                Players Out ({summary.playersOut.length})
              </h3>
              <div className="space-y-2">
                {summary.playersOut.map((player) => (
                  <div key={player.id} className="flex items-center gap-3 p-2 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/jerseys/jersey1.webp" />
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-medium">{player.name}</div>
                      <div className="text-sm text-muted-foreground">{player.teamId}</div>
                    </div>
                    <Badge variant="outline">{player.position}</Badge>
                    <div className="font-semibold">{player.currentPrice}M</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No Transfers Message */}
          {!hasTransfers && (
            <div className="text-center py-8 text-muted-foreground">
              <DollarSign className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No transfers to confirm. Your team remains unchanged.</p>
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={onConfirm} 
            disabled={!validation.isValid || isLoading}
            className="min-w-24"
          >
            {isLoading ? "Saving..." : hasTransfers ? "Confirm Transfers" : "Save Team"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// components/PlayerCard.tsx
import React from "react";
import Image from "next/image";
import { Player, PlayerGWStats, PlayerTotalStats } from "@prisma/client";
import { X, Star, TrendingUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface PlayerCardProps {
  player: Player;
  captainId?: string;
  viceCaptainId?: string;
  gwStats?: PlayerGWStats | null;
  totalStats?: PlayerTotalStats | null;
  onClick?: () => void;
  onRemove?: () => void;
  isRemoved?: boolean;
  className?: string;
  style?: React.CSSProperties;
  showNextFixture: boolean;
  showPrice: boolean;
  showPoints: boolean;
  showRole: boolean;
  showPosition: boolean;
  showRemove?: boolean;
}

export default function PlayerCard({
  player,
  captainId,
  viceCaptainId,
  gwStats = null,
  totalStats = null,
  isRemoved = false,
  onClick,
  onRemove,
  showNextFixture,
  showPrice,
  showPoints,
  showRole,
  showPosition,
  showRemove = false,
  className = "",
  style = {},
}: PlayerCardProps) {
  const { name, position, currentPrice } = player;

  const role =
    player.id === captainId ? "C" : player.id === viceCaptainId ? "V" : null;

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove();
    }
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  if (isRemoved) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              onClick={handleClick}
              style={style}
              className={cn(
                "group relative flex flex-col items-center justify-center bg-card/50 backdrop-blur-sm border-2 border-dashed border-muted-foreground/30 rounded-xl text-center cursor-pointer transition-all duration-200 hover:border-primary/50 hover:bg-card/70 p-1 sm:p-2 opacity-60 hover:opacity-80 w-full h-full",
                // Mobile-first sizing
                "min-w-[45px] min-h-[55px]",
                "sm:min-w-[55px] sm:min-h-[65px]",
                "md:min-w-[65px] md:min-h-[75px]",
                className
              )}
            >
              <div className="w-full flex-1 relative mb-1">
                <Image
                  src="/jerseys/no_jersey.webp"
                  alt="Empty slot"
                  fill
                  sizes="(max-width: 640px) 35px, (max-width: 768px) 45px, (max-width: 1024px) 55px, 65px"
                  className="object-contain grayscale group-hover:grayscale-0 transition-all duration-200"
                />
              </div>

              <div className="w-full space-y-0.5">
                <p className="font-medium text-[8px] sm:text-[9px] md:text-[10px] text-muted-foreground group-hover:text-foreground leading-tight truncate transition-colors">
                  {name}
                </p>

                <Badge variant="outline" className="text-[6px] sm:text-[7px] px-1 py-0 opacity-70 h-auto">
                  {position}
                </Badge>
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Click to restore {name}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            onClick={handleClick}
            style={style}
            className={cn(
              "group relative flex flex-col items-center justify-center bg-card/90 backdrop-blur-sm border border-border/50 rounded-xl text-center cursor-pointer transition-all duration-200 hover:shadow-lg hover:shadow-primary/10 hover:border-primary/50 hover:bg-card w-full h-full",
              // Mobile-first sizing with better proportions
              "min-w-[45px] min-h-[55px] p-1",
              "sm:min-w-[55px] sm:min-h-[65px] sm:p-1.5",
              "md:min-w-[65px] md:min-h-[75px] md:p-2",
              "lg:min-w-[70px] lg:min-h-[80px]",
              // Hover effects only on larger screens
              "md:hover:scale-105",
              className
            )}
          >
            {/* Jersey Image */}
            <div className="w-full flex-1 relative mb-1">
              <Image
                src="/jerseys/jersey1.webp"
                alt={`${name} jersey`}
                fill
                sizes="(max-width: 640px) 35px, (max-width: 768px) 45px, (max-width: 1024px) 55px, 65px"
                className="object-contain md:group-hover:scale-110 transition-transform duration-200"
              />
            </div>

            {/* Player Info */}
            <div className="w-full space-y-0.5">
              <p className="font-semibold text-[8px] sm:text-[9px] md:text-[10px] lg:text-[11px] text-foreground leading-tight truncate">
                {name}
              </p>

              {/* Position Badge */}
              {showPosition && (
                <Badge variant="secondary" className="text-[6px] sm:text-[7px] px-1 py-0 h-auto">
                  {position}
                </Badge>
              )}

              {/* Price and Points */}
              <div className="flex flex-col items-center gap-0.5 w-full">
                {showPrice && (
                  <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 px-1 py-0 text-[6px] sm:text-[7px] font-semibold h-auto">
                    ${currentPrice.toFixed(1)}M
                  </Badge>
                )}
                {showPoints && (
                  <Badge variant="outline" className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800 px-1 py-0 text-[6px] sm:text-[7px] font-semibold h-auto">
                    <TrendingUp className="w-2 h-2 mr-0.5" />
                    {gwStats?.points || totalStats?.points || 0}
                  </Badge>
                )}
              </div>
            </div>

            {/* Next Fixture */}
            {showNextFixture && (
              <Badge variant="outline" className="text-[6px] sm:text-[7px] px-1 py-0 mt-0.5 bg-muted/50 h-auto">
                vs TBD
              </Badge>
            )}

            {/* Captain / Vice badges */}
            {showRole && role === "C" && (
              <Badge className="absolute top-0.5 left-0.5 sm:top-1 sm:left-1 bg-yellow-500 hover:bg-yellow-600 text-yellow-900 text-[6px] sm:text-[7px] px-1 sm:px-1.5 py-0.5 font-bold shadow-md h-auto">
                <Star className="w-1.5 h-1.5 sm:w-2 sm:h-2 mr-0.5" />
                C
              </Badge>
            )}
            {showRole && role === "V" && (
              <Badge variant="secondary" className="absolute top-0.5 left-0.5 sm:top-1 sm:left-1 bg-blue-500 hover:bg-blue-600 text-white text-[6px] sm:text-[7px] px-1 sm:px-1.5 py-0.5 font-bold shadow-md h-auto">
                VC
              </Badge>
            )}

            {/* Remove button */}
            {showRemove && onRemove && (
              <Button
                size="sm"
                variant="destructive"
                onClick={handleRemove}
                className="absolute top-0.5 right-0.5 sm:top-1 sm:right-1 w-4 h-4 sm:w-5 sm:h-5 p-0 rounded-full md:hover:scale-110 transition-all duration-200 shadow-md"
                title="Remove player"
              >
                <X className="w-2 h-2 sm:w-3 sm:h-3" />
              </Button>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-center">
            <p className="font-semibold">{name}</p>
            <p className="text-xs text-muted-foreground">{position} • ${currentPrice.toFixed(1)}M</p>
            {(gwStats?.points || totalStats?.points) && (
              <p className="text-xs">Points: {gwStats?.points || totalStats?.points || 0}</p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

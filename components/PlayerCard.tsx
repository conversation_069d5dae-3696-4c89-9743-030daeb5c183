// components/PlayerCard.tsx
import React from "react";
import Image from "next/image";
import { PlayerGWStats, PlayerTotalStats } from "@prisma/client";
import { X, TrendingUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { PlayerInfo } from "@/types/types";

interface PlayerCardProps {
  player: PlayerInfo;
  captainId?: string | null;
  viceCaptainId?: string | null;
  gwStats?: PlayerGWStats | null;
  totalStats?: PlayerTotalStats | null;
  onClick?: () => void;
  onRemove?: () => void;
  isRemoved?: boolean;
  className?: string;
  style?: React.CSSProperties;
  showNextFixture: boolean;
  showPrice: boolean;
  showPoints: boolean;
  showRole: boolean;
  showPosition: boolean;
  showRemove?: boolean;
  isBuildingTeam?: boolean;
}

export default function PlayerCard({
  player,
  captainId,
  viceCaptainId,
  gwStats = null,
  totalStats = null,
  isRemoved = false,
  onClick,
  onRemove,
  showNextFixture,
  showPrice,
  showPoints,
  showRole,
  showPosition,
  showRemove = false,
  isBuildingTeam = false,
  className = "",
  style = {},
}: PlayerCardProps) {
  // suppress ts errors
  const { name, position, currentPrice, team } = player;

  const role =
    player.id === captainId ? "C" : player.id === viceCaptainId ? "V" : null;

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove();
    }
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  if (isRemoved) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              onClick={ isBuildingTeam ? () => {} : handleClick}
              style={style}
              className={cn(
                "group relative flex flex-col items-center justify-center rounded-xl text-center cursor-pointer transition-all duration-200 hover:border-primary/50 hover:bg-card/40 opacity-60 hover:opacity-80 w-full h-full",
                // Mobile-first sizing with even bigger proportions
                "min-w-[70px] min-h-[85px] p-1",
                "sm:min-w-[80px] sm:min-h-[100px] sm:p-1.5",
                "md:min-w-[90px] md:min-h-[110px] md:p-2",
                "lg:min-w-[100px] lg:min-h-[120px] lg:p-2.5",
                className
              )}
            >
              <div className="w-full flex-1 relative mb-1 sm:mb-1.5">
                <Image
                  src="/jerseys/no_jersey.webp"
                  alt="Empty slot"
                  fill
                  sizes="(max-width: 640px) 60px, (max-width: 768px) 70px, (max-width: 1024px) 80px, 90px"
                  className="object-contain grayscale group-hover:grayscale-0 transition-all duration-200 drop-shadow-sm"
                />
              </div>

              <div className="w-full space-y-0.5 sm:space-y-1">
                <p className="font-medium text-[10px] sm:text-[11px] md:text-[12px] text-muted-foreground group-hover:text-foreground leading-tight truncate transition-colors drop-shadow-sm">
                  {name}
                </p>

                <Badge
                  variant="outline"
                  className="text-[8px] sm:text-[9px] px-1.5 py-0.5 opacity-70 h-auto bg-muted/60 backdrop-blur-sm"
                >
                  {position}
                </Badge>
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Click to restore {name}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            onClick={handleClick}
            style={style}
            className={cn(
              "group relative flex flex-col items-center justify-center text-center cursor-pointer transition-all duration-200 w-full h-full",
              // Mobile-first sizing with even bigger proportions
              "min-w-[70px] min-h-[85px] p-1",
              "sm:min-w-[80px] sm:min-h-[100px] sm:p-1.5",
              "md:min-w-[90px] md:min-h-[110px] md:p-2",
              "lg:min-w-[100px] lg:min-h-[120px] lg:p-2.5",
              // Hover effects only on larger screens
              "md:hover:scale-105",
              className
            )}
          >
            {/* Jersey Image */}
            <div className="w-full flex-1 relative mb-1 sm:mb-1.5">
              <Image
                src={team?.jersey}
                alt={`${name} jersey`}
                fill
                sizes="(max-width: 640px) 60px, (max-width: 768px) 70px, (max-width: 1024px) 80px, 90px"
                className="object-contain md:group-hover:scale-110 transition-transform duration-200 drop-shadow-sm"
              />
            </div>

            {/* Player Info */}
            <div className="w-full space-y-0.5 sm:space-y-1">
              <p className="font-semibold text-[10px] sm:text-[11px] md:text-[12px] lg:text-[13px] text-foreground leading-tight truncate drop-shadow-sm">
                {name}
              </p>

              {/* Position Badge */}
              {showPosition && (
                <Badge
                  variant="secondary"
                  className="text-[8px] sm:text-[9px] px-1.5 py-0.5 h-auto bg-secondary/80 backdrop-blur-sm"
                >
                  {position}
                </Badge>
              )}

              {/* Price and Points */}
              <div className="flex flex-col items-center gap-0.5 sm:gap-1 w-full">
                {showPrice && (
                  <Badge
                    variant="outline"
                    className="bg-primary/20 backdrop-blur-sm text-primary border-primary/30 px-1.5 py-0.5 text-[8px] sm:text-[9px] font-semibold h-auto"
                  >
                    ${currentPrice.toFixed(1)}M
                  </Badge>
                )}
                {showPoints && (
                  <Badge
                    variant="outline"
                    className="bg-green-100/80 dark:bg-green-900/30 backdrop-blur-sm text-green-700 dark:text-green-300 border-green-200/50 dark:border-green-800/50 px-1.5 py-0.5 text-[8px] sm:text-[9px] font-semibold h-auto"
                  >
                    <TrendingUp className="w-2 h-2 mr-0.5" />
                    {gwStats?.points || 0}
                  </Badge>
                )}
              </div>
            </div>

            {/* Next Fixture */}
            {showNextFixture && (
              <Badge
                variant="outline"
                className="text-[7px] sm:text-[8px] px-1.5 py-0.5 mt-0.5 sm:mt-1 bg-muted/60 backdrop-blur-sm h-auto"
              >
                vs TBD
              </Badge>
            )}

            {/* Captain / Vice badges */}
            {showRole && role === "C" && (
              <Badge className="absolute top-1 left-1 sm:top-1.5 sm:left-1.5 bg-yellow-500/90 backdrop-blur-sm hover:bg-yellow-600 text-yellow-900 text-[7px] sm:text-[8px] px-1.5 sm:px-2 py-0.5 font-bold shadow-lg h-auto">
                C
              </Badge>
            )}
            {showRole && role === "V" && (
              <Badge
                variant="secondary"
                className="absolute top-1 left-1 sm:top-1.5 sm:left-1.5 bg-blue-500/90 backdrop-blur-sm hover:bg-blue-600 text-white text-[7px] sm:text-[8px] px-1.5 sm:px-2 py-0.5 font-bold shadow-lg h-auto"
              >
                V
              </Badge>
            )}

            {/* Remove button */}
            {showRemove && onRemove && (
              <Button
                size="sm"
                variant="destructive"
                onClick={handleRemove}
                className="absolute top-0.5 right-0.5 sm:top-1 sm:right-1 w-1 h-1 sm:w-3.5 sm:h-3.5 p-0 rounded-full md:hover:scale-110 transition-all duration-200 shadow-lg bg-destructive/90 backdrop-blur-sm"
                title="Remove player"
              >
                <X className="w-1 h-1 sm:w-1 sm:h-1" />
              </Button>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-center">
            <p className="font-semibold">{name}</p>
            <p className="text-xs text-muted-foreground">
              {position} • ${currentPrice.toFixed(1)}M
            </p>
            {(gwStats?.points || totalStats?.points) && (
              <p className="text-xs">
                Points: {gwStats?.points|| 0}
              </p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

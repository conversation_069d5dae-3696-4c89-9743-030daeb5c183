// components/PlayerCard.tsx
import React from "react";
import Image from "next/image";
import { Player, PlayerGWStats, PlayerTotalStats } from "@prisma/client";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

interface PlayerCardProps {
  player: Player;
  captainId?: string;
  viceCaptainId?: string;
  gwStats?: PlayerGWStats | null;
  totalStats?: PlayerTotalStats | null;
  onClick?: () => void;
  onRemove?: () => void;
  isRemoved?: boolean;
  className?: string;
  style?: React.CSSProperties;
  showNextFixture: boolean;
  showPrice: boolean;
  showPoints: boolean;
  showRole: boolean;
  showPosition: boolean;
  showRemove?: boolean;
}

export default function PlayerCard({
  player,
  captainId,
  viceCaptainId,
  gwStats = null,
  totalStats = null,
  isRemoved = false,
  onClick,
  onRemove,
  showNextFixture,
  showPrice,
  showPoints,
  showRole,
  showPosition,
  showRemove = false,
  className = "",
  style = {},
}: PlayerCardProps) {
  const { name, position, currentPrice } = player;

  const role =
    player.id === captainId ? "C" : player.id === viceCaptainId ? "V" : null;

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove();
    }
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  if (isRemoved) {
    return (
      <div
        onClick={handleClick}
        style={style}
        className={cn(
          "relative flex flex-col items-center justify-start bg-transparent backdrop-blur-sm border border-border/30 rounded-md text-center cursor-pointer transition hover:shadow-md px-1 py-1 opacity-50 w-full h-full max-w-[12vw] max-h-[12vw] min-w-[50px] min-h-[60px]",
          className
        )}
      >
        <div className="w-full h-3/5 relative mb-0.5">
          <Image
            src="/jerseys/no_jersey.webp"
            alt="jersey"
            fill
            sizes="(max-width: 768px) 32px, (max-width: 1024px) 48px, 64px"
            className="object-contain"
          />
        </div>

        <p className="font-medium text-[8px] sm:text-[10px] text-foreground leading-tight truncate w-full">
          {name}
        </p>
      </div>
    );
  }

  return (
    <div
      onClick={handleClick}
      style={style}
      className={cn(
        "relative flex flex-col items-center justify-start bg-transparent backdrop-blur-sm border border-border/30 rounded-md text-center cursor-pointer transition hover:shadow-md px-1 py-1 w-full h-full max-w-[12vw] max-h-[12vw] min-w-[50px] min-h-[60px]",
        className
      )}
    >
      <div className="w-full h-3/5 relative mb-0.5">
        <Image
          src="/jerseys/jersey1.webp"
          alt="jersey"
          fill
          sizes="(max-width: 768px) 32px, (max-width: 1024px) 48px, 64px"
          className="object-contain"
        />
      </div>

      <p className="font-medium text-[9px] sm:text-[10px] md:text-[11px] text-foreground leading-tight truncate w-full">
        {name}
      </p>
      {/* Position */}
      {showPosition && (
        <p className="text-[7px] sm:text-[8px] md:text-[9px] text-muted-foreground uppercase tracking-wide mb-0.5 truncate w-full">
          {position}
        </p>
      )}

      {/* Price and Points underneath */}
      <div className="flex flex-col items-center gap-[1px] text-[7px] sm:text-[8px] md:text-[9px] w-full">
        {showPrice && (
          <span className="bg-accent/50 text-accent-foreground px-1 rounded truncate w-full">
            ${currentPrice.toFixed(1)}M
          </span>
        )}
        {showPoints && (
          <span className="bg-muted/50 text-muted-foreground px-1 rounded truncate w-full">
            {gwStats?.points || totalStats?.points || 0} pts
          </span>
        )}
      </div>

      {/* Next Fixture TODO  FIXME */}
      {showNextFixture && (
        <p className="mt-0.5 text-[6px] sm:text-[7px] text-muted-foreground truncate w-full">
          vs
        </p>
      )}

      {/* Captain / Vice badges */}
      {showRole && role === "C" && (
        <div className="absolute top-0.5 left-0.5 bg-primary/80 text-primary-foreground text-[6px] sm:text-[7px] px-0.5 rounded-sm">
          C
        </div>
      )}
      {showRole && role === "V" && (
        <div className="absolute top-0.5 left-0.5 bg-secondary/80 text-secondary-foreground text-[6px] sm:text-[7px] px-0.5 rounded-sm">
          VC
        </div>
      )}

      {/* Remove button */}
      {showRemove && onRemove && (
        <button
          onClick={handleRemove}
          className="absolute top-0.5 right-0.5 text-destructive/80 hover:text-destructive w-4 h-4 sm:w-5 sm:h-5 rounded-full flex items-center justify-center transition-colors"
          title="Remove player"
        >
          <X className="w-full h-full" />
        </button>
      )}
    </div>
  );
}

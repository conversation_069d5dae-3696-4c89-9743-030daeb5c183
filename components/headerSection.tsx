'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs';
import { ModeToggle } from "@/components/mode-toggle";
import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  <PERSON>et, 
  SheetContent, 
  SheetTrigger,
  SheetHeader,
  SheetTitle 
} from "@/components/ui/sheet";
import { cn } from '@/lib/utils';
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"


const HeaderSection = ({ userId = null }: { userId: string | null }) => {
  const pathname = usePathname();
  const [open, setOpen] = useState(false);

  const tabs = [
    { href: '/', label: 'Home', public: true },
    { href: `/points/${userId}/round/1`, label: 'Points', public: false },
    { href: '/team', label: 'Team', public: false },
    { href: '/transfers', label: 'Transfers', public: false },
    { href: '/leagues', label: 'Leagues', public: false },
    { href: '/fixtures', label: 'Fixtures', public: true },
    { href: '/statistics', label: 'Statistics', public: true },
    { href: '/prizes', label: 'Prizes', public: true },
    { href: '/help', label: 'Help', public: true },
  ];
  
  // Close mobile menu when pathname changes
  useEffect(() => {
    setOpen(false);
  }, [pathname]);

  const renderMobileTab = (tab: typeof tabs[0], classes: string) => {
    if (!tab.public) {
      return (
        <SignedIn key={tab.href}>
          <Link 
            href={tab.href} 
            className={classes}
            onClick={() => setOpen(false)}
          >
            {tab.label}
          </Link>
        </SignedIn>
      );
    }

    return (
      <Link 
        href={tab.href} 
        key={tab.href} 
        className={classes}
        onClick={() => setOpen(false)}
      >
        {tab.label}
      </Link>
    );
  };

  const renderDesktopTab = (tab: typeof tabs[0]) => {
    const isActive = tab.href === '/' 
      ? pathname === tab.href 
      : pathname.includes(tab.href);
    
    const Component = () => (
      <Link
        href={tab.href}
        className={cn(
          navigationMenuTriggerStyle(),
          isActive ? "bg-muted text-foreground" : "text-muted-foreground"
        )}
      >
        {tab.label}
      </Link>
    );

    if (!tab.public) {
      return (
        <SignedIn key={tab.href}>
          <NavigationMenuItem>
            <Component />
          </NavigationMenuItem>
        </SignedIn>
      );
    }

    return (
      <NavigationMenuItem key={tab.href}>
        <Component />
      </NavigationMenuItem>
    );
  };

  return (
    <div className="relative w-full shadow bg-background">
      {/* Header content */}
      <header className="relative z-10 flex items-center p-4 h-full justify-between text-foreground">
        {/* Mobile: Menu button and logo */}
        <div className="flex items-center md:hidden">
          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[60%] p-0">
              <SheetHeader className="px-4 pt-4">
                <SheetTitle>Navigation</SheetTitle>
              </SheetHeader>
              <nav className="flex flex-col p-4 space-y-2">
                {tabs.map((tab) => {
                  const active = tab.href === '/' ? pathname === tab.href : pathname.includes(tab.href);
                  const tabClasses = cn(
                    "w-full text-left rounded-xl px-4 py-3 text-sm font-medium transition-colors",
                    active
                      ? "bg-muted text-foreground"
                      : "hover:bg-muted/50 text-muted-foreground"
                  );

                  return renderMobileTab(tab, tabClasses);
                })}
              </nav>
            </SheetContent>
          </Sheet>
          
          {/* Mobile: Centered logo */}
          <Link href="/" className="font-bold text-xl absolute left-1/2 transform -translate-x-1/2">
            TFL
          </Link>
        </div>
        
        {/* Desktop: Left section - Logo */}
        <div className="hidden md:block">
          <Link href="/" className="font-bold text-xl">
            TFL
          </Link>
        </div>
        
        {/* Desktop: Center section - Navigation */}
        <div className="hidden md:flex justify-center">
          <NavigationMenu>
            <NavigationMenuList>
              {tabs.map(renderDesktopTab)}
            </NavigationMenuList>
          </NavigationMenu>
        </div>
        
        {/* Right section - Auth and theme toggle */}
        <div className="flex items-center gap-4">
          <SignedOut>
            <div className="hidden md:block">
              <SignInButton mode="modal">
                <Button variant="outline" size="sm">Sign In</Button>
              </SignInButton>
            </div>
            <div className="hidden md:block">
              <SignUpButton mode="modal">
                <Button size="sm">Sign Up</Button>
              </SignUpButton>
            </div>
          </SignedOut>
          <SignedIn>
            <UserButton />
          </SignedIn>
          
          <ModeToggle />
        </div>
      </header>
    </div>
  );
};

export default HeaderSection;

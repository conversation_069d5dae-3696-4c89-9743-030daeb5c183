// app/team/[userId]/[round]/components/TeamSection.tsx
"use client";

import { useMemo, useState } from "react";
import { useView } from "@/contexts/ViewContext";
import { Button } from "@/components/ui/button";
import TeamTable from "@/components/TeamTable";
import PitchView from "@/components/PitchView";
import PlayerModal from "@/components/PlayerModal";
import { Player } from "@prisma/client";
import TeamViewToggle from "./TeamViewToggle";

export default function TeamSection({
  isPointsPage = false,
  isTeamPage = false,
  isTransferPage = false,
  starters = [],
  subs = [],
  removedPlayers = [],
  hasChanges = false,
  isLoading = false,
  onCaptainChange,
  onViceCaptainChange,
  onSubstitute,
  onRemove,
  onAdd,
  onSave,
  getSubsList,
}: {
  isPointsPage?: boolean;
  isTeamPage?: boolean;
  isTransferPage?: boolean;
  starters?: Player[];
  subs?: Player[];
  removedPlayers?: Player[];
  hasChanges?: boolean;
  isLoading?: boolean;
  onCaptainChange?: (id: string) => void;
  onViceCaptainChange?: (id: string) => void;
  getSubsList?: (p: Player | null) => Player[];
  onSubstitute?: (outId: string, inId: string) => void;
  onRemove?: (p: Player) => void;
  onAdd?: (p: Player) => void;
  onSave?: () => void;
}) {
  const { view } = useView();
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const isSelectedPlayerRemoved = useMemo(
    () => removedPlayers.some((p) => p.id === selectedPlayer?.id),
    [removedPlayers, selectedPlayer]
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-foreground">
          {view === "list" ? "Starters" : "Pitch View"}
        </h2>
        <TeamViewToggle />
        {!isPointsPage && (
          <div className="flex gap-2">
            <Button
              onClick={onSave}
              disabled={!hasChanges || isLoading}
              variant={hasChanges ? "default" : "outline"}
              className={`
                transition-all duration-200 
                ${hasChanges ? "shadow-md hover:shadow-lg" : "opacity-70"}
              `}
            >
              {isLoading ? (
                <>
                  <span className="animate-spin mr-2">⟳</span>
                  Saving...
                </>
              ) : hasChanges ? (
                "Save Changes"
              ) : (
                "No Changes"
              )}
            </Button>
          </div>
        )}
      </div>

      {view === "list" ? (
        <>
          <TeamTable players={starters} onSelect={setSelectedPlayer} />
          <h3 className="text-xl font-semibold text-foreground mt-6">
            Substitutes
          </h3>
          <TeamTable players={subs} onSelect={setSelectedPlayer} />
        </>
      ) : (
        <PitchView
          starters={starters}
          subs={subs}
          removedPlayers={removedPlayers}
          onSelect={setSelectedPlayer}
          showPoints={isPointsPage}
          showRole={!isTransferPage}
          showNextFixture={!isPointsPage}
          showPrice={isTransferPage}
          showRemove={isTransferPage}
        />
      )}

      <PlayerModal
        player={selectedPlayer}
        isSelectedPlayerRemoved={isSelectedPlayerRemoved}
        subsList={isTeamPage ? getSubsList?.(selectedPlayer) ?? [] : []}
        onClose={() => setSelectedPlayer(null)}
        onMakeCaptain={isTeamPage ? onCaptainChange : undefined}
        onMakeViceCaptain={isTeamPage ? onViceCaptainChange : undefined}
        onSubstitute={isTeamPage ? onSubstitute : undefined}
        isEditable={!isPointsPage}
        onRemove={isTransferPage ? onRemove : undefined}
        onAdd={isTransferPage ? onAdd : undefined}
      />
    </div>
  );
}

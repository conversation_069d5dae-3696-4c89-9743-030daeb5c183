import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { ViewMode } from "@/types/types";

export default function TeamViewToggle({
  view,
  setView,
}: {
  view: ViewMode;
  setView: (view: ViewMode) => void;
}) {
  return (
    <ToggleGroup
      type="single"
      value={view}
      onValueChange={(v) => setView(v as "list" | "pitch")}
      className="gap-2"
    >
      <ToggleGroupItem value="list">List View</ToggleGroupItem>
      <ToggleGroupItem value="pitch">Pitch View</ToggleGroupItem>
    </ToggleGroup>
  );
}

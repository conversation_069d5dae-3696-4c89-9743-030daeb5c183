import { useView } from "@/contexts/ViewContext";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

export default function TeamViewToggle() {
  const { view, setView } = useView();
  
  return (
    <ToggleGroup
      type="single"
      value={view}
      onValueChange={(v) => setView(v as "list" | "pitch")}
      className="gap-2"
    >
      <ToggleGroupItem value="list">List View</ToggleGroupItem>
      <ToggleGroupItem value="pitch">Pitch View</ToggleGroupItem>
    </ToggleGroup>
  );
}
// app/team/[userId]/[round]/components/InfoPanel.tsx
"use client";

interface InfoPanelProps {
  squadValue: number;
  transfersLeft: number;
}

export default function InfoPanel({
  squadValue,
  transfersLeft,
}: InfoPanelProps) {
  return (
    <div className="bg-card p-4 rounded-lg border border-border">
      <h3 className="text-lg font-semibold mb-2">Team Info</h3>
      <p>Total Squad Value: ${squadValue.toFixed(1)}M</p>
      <p className="mt-2">Remaining Transfers: {transfersLeft}</p>
    </div>
  );
}
